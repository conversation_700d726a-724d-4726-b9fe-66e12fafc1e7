const path = require("path");

// const env = process.env.VUE_APP_NODE_ENV === "production" ? "prod" : "test";
let env = ''
console.log(process.env.VUE_APP_NODE_ENV)
if(process.env.VUE_APP_NODE_ENV === 'production'){
  env = 'prod'
}else if(process.env.VUE_APP_NODE_ENV === 'test'){
  env = 'test'
}else if(process.env.VUE_APP_NODE_ENV === 'test-74'){
  env = 'test-74'
}
const config = require(`./src/config/index.${env}.js`);
const TerserPlugin = require("terser-webpack-plugin");
const CompressionWebpackPlugin = require('compression-webpack-plugin')
const resolve = dir => {
  return path.join(__dirname, dir);
};

// 项目部署基础
// 默认情况下，我们假设你的应用将被部署在域的根目录下,
// 例如：https://www.my-app.com/
// 默认：'/'
// 如果您的应用程序部署在子路径中，则需要在这指定子路径
// 例如：https://www.foobar.com/my-app/
// 需要将它改为'/my-app/'
const BASE_URL = process.env.VUE_APP_CMD === "build" ? config.CdnDomain : "/";
// const BASE_URL = "/";

module.exports = {
	lintOnSave: false,
  // Project deployment base
  // By default we assume your app will be deployed at the root of a domain,
  // e.g. https://www.my-app.com/
  // If your app is deployed at a sub-path, you will need to specify that
  // sub-path here. For example, if your app is deployed at
  // https://www.foobar.com/my-app/
  // then change this productionSourceMapto '/my-app/'
  publicPath: BASE_URL,
  // tweak internal webpack configuration.
  // see https://github.com/vuejs/vue-cli/blob/dev/docs/webpack.md
  // 如果你不需要使用eslint，把lintOnSave设为false即可
  chainWebpack: config => {
    config.module
    .rule('svg')
    .exclude.add(resolve('src/assets/svg'))
    .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/svg'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()
    config.resolve.alias
      .set("@", resolve("src")) // key,value自行定义，比如.set('@@', resolve('src/components'))
      .set("_c", resolve("src/components"))
      .set("utils", resolve("src/utils")).set('utils', resolve('src/utils')).set('assets', resolve('src/assets'))
    // remove the prefetch plugin
    config.plugins.delete("prefetch");
    config.plugin('html').tap(args => {
      args[0].title = '树家ERP管理平台'
      return args
    })
    // or:
    // modify its options:
    // config.plugin('prefetch').tap(options => {
    //   options[0].fileBlacklist = options[0].fileBlacklist || []
    //   options[0].fileBlacklist.push(/myasyncRoute(.)+?\.js$/)
    //   return options
    // })
  },
  // webpack打包配置
  configureWebpack: config => {
    //   console.log(process.env.NODE_ENV)
    if (process.env.NODE_ENV === "production") {
      //     //为生产环境修改配置。。。
      config.mode = "production";
          const productionGzipExtensions = ['html', 'js', 'css']

          config.plugins.push(
            new CompressionWebpackPlugin({
              filename: '[path].gz[query]',
              algorithm: 'gzip',
              test: new RegExp(
                '\\.(' + productionGzipExtensions.join('|') + ')$'
              ),
              threshold: 10240, // 只有大小大于该值的资源会被处理 10240
              minRatio: 0.8, // 只有压缩率小于这个值的资源才会被处理
              deleteOriginalAssets: false // 删除原文件
            })
          )
      let optimization = {
        runtimeChunk: "single",
        splitChunks: {
          chunks: 'all',
          maxInitialRequests: Infinity,
          minSize: 20000,
          cacheGroups: {
            vendor: {
              test:/[\\/]node_modules[\\/]/,
              name(module){
                const packageName = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/)[1];
                return `${packageName.replace('@','')}`
              }
            }
          }
        },
        // splitChunks: {
        //   cacheGroups: {
        //     vendor: {
        //       chunks: 'all',
        //       test: /node_modules/,
        //       name: 'vendor',
        //       minChunks: 1,
        //       maxInitialRequests: 5,
        //       minSize: 0,
        //       priority: 100
        //     },
        //     common: {
        //       chunks: 'all',
        //       test: /[\\/]src[\\/]js[\\/]/,
        //       name: 'common',
        //       minChunks: 2,
        //       maxInitialRequests: 5,
        //       minSize: 0,
        //       priority: 60
        //     },
        //     styles: {
        //       name: 'styles',
        //       test: /\.(sa|sc|c)ss$/,
        //       chunks: 'all',
        //       enforce: true
        //     },
        //     runtimeChunk: {
        //       name: 'manifest'
        //     }
        //   }
        // },
        minimizer: [
          new TerserPlugin({
            terserOptions: {
              format: {
                comments: false
              },
              compress: {
                drop_console: process.env.VUE_APP_NODE_ENV === 'test' ? false : true, // 测试环境放开打印权限
                pure_funcs: ["console.log"] //去除console
              }
            }
          })
        ]
      };
      Object.assign(config, {
        optimization
      });
    } else {
      config.mode = "development";
    }
  },
  // 设为FALSE打包时不生成.MAP文件
  productionSourceMap: false,
  // 这里写你调用接口的基础路径，来解决跨域，如果设置了代理，那你本地开发环境的axios的baseUrl要写为 '' ，即空字符串
  devServer: {
    // proxy: 'localhost:3000'
    hot: true,
    open: true,
    port: 3000,
	  compress: true,
	  disableHostCheck: true, //webpack4.0 开启热更新
    // proxy:{
    //   "/api":{
    //     // 服务器地址
    //     target:"http://127.0.0.1",
    //     changeOrigin: true,
    //     pathRewrite:{
    //       "^api":""
    //     }
    //   }
    // }
  }
};
