let logisticsList = [
  {
    express_code: 'SF',
    express_name: '顺丰',
    express_no: 'SFO78901233078',
    info: {
      time: '2022年9月3日 下午6:20:39',
      city: '上海市',
      description:
        '【上海市】 快件已被【丰巢的申昆路2177号15号楼转换电梯厅丰巢柜(丰巢智能快递柜)】代收，如有问题请电联（95333,18516259150），感谢使用中通快递，期待再次为您服务！'
    }
  },
  {
    express_code: 'ST',
    express_name: '申通',
    express_no: 'STO78901233078',
    info: {
      time: '2022年9月3日 上午6:20:39',
      city: '上海市',
      description:
        '【上海市】 【华漕】 的周旭（18516259150） 正在第1次派件, 请保持电话畅通,并耐心等待（95720为中通快递员外呼专属号码，请放心接听）'
    }
  },
  {
    express_code: 'ST',
    express_name: '申通',
    express_no: 'STO88888888888',
    info: {
      time: '2022年9月2日 下午6:20:39',
      city: '深圳市',
      description: '【深圳市】 快件离开 【罗湖笋岗】 已发往 【上海浦西转运中心】'
    }
  }
];

export default {
  'get|/logistics/query': option => {
    return {
      status: 200,
      message: 'success',
      data: logisticsList
    };
  }
};
