const routes = [
  {
    id: '102',
    path: 'purchase/list',
    query: {},
    type: 'MENU',
    meta: {
      title: '首采',
      icon: 'fa-shopping-basket'
    },
    to_his: '0',
    children: [
      {
        id: '103',
        p_id: '102',
        path: 'purchase/list',
        query: {},
        type: 'SUB_MENU',
        meta: {
          title: '订单列表'
        }
      },
      {
        id: '123',
        p_id: '103',
        path: 'purchase/detail',
        query: {},
        type: 'PAGE',
        meta: {
          title: '订单详情'
        }
      }
    ]
  },
  {
    id: '101',
    path: 'goods/goods/list',
    query: {},
    type: 'MENU',
    meta: {
      title: '商品',
      icon: 'fa-shopping-cart'
    },
    children: [
      {
        id: '102',
        p_id: '101',
        path: 'goods/goods/list',
        query: {},
        type: 'SUB_MENU',
        meta: {
          title: '商品列表'
        }
      },
      {
        id: '103',
        p_id: '102',
        path: 'goods/goods/detail',
        query: {},
        type: 'PAGE',
        meta: {
          title: '新建商品'
        }
      },
      {
        id: '104',
        p_id: '101',
        path: 'goods/sell/list',
        query: {},
        type: 'SUB_MENU',
        meta: {
          title: '售卖列表'
        }
      },
      {
        id: '105',
        p_id: '104',
        path: 'goods/sell/detail',
        query: {},
        type: 'PAGE',
        meta: {
          title: '新增商品售价'
        }
      }
    ]
  },
  {
    id: '100',
    path: 'order/list',
    query: {},
    type: 'MENU',
    meta: {
      title: '订单',
      icon: 'fa-list'
    },
    children: [
      {
        id: '101',
        p_id: '100',
        path: 'order/list',
        query: {},
        type: 'SUB_MENU',
        meta: {
          title: '订单列表'
        }
      },
      {
        id: '102',
        p_id: '101',
        path: 'order/detail',
        query: {},
        type: 'PAGE',
        meta: {
          title: '订单详情'
        }
      }
    ]
  },
  {
    id: '124',
    path: 'finance/payments/list',
    query: {},
    type: 'MENU',
    meta: {
      title: '财务',
      icon: 'fa-bar-chart'
    },
    children: [
      {
        id: '129',
        p_id: '124',
        path: 'finance/payments/list',
        query: {},
        type: 'SUB_MENU',
        meta: {
          title: '收支明细'
        }
      },
      {
        id: '110',
        p_id: '129',
        path: 'finance/payments/detail',
        query: {},
        type: 'PAGE',
        meta: {
          title: '详情'
        }
      },
      {
        id: '130',
        p_id: '124',
        path: 'finance/statements/list',
        type: 'SUB_MENU',
        meta: {
          title: '财务报表'
        }
      },
      {
        id: '131',
        p_id: '124',
        path: 'finance/income/list',
        type: 'SUB_MENU',
        meta: {
          title: '收入统计'
        }
      }
    ]
  },
  {
    id: '120',
    path: 'organization/company/list',
    query: {},
    type: 'MENU',
    meta: {
      title: '组织',
      icon: 'fa-group'
    },
    children: [
      {
        id: '128',
        p_id: '120',
        path: 'organization/company/list',
        query: {},
        type: 'SUB_MENU',
        meta: {
          title: '省公司列表'
        }
      },
      {
        id: '110',
        p_id: '128',
        path: 'organization/company/edit',
        query: {},
        type: 'PAGE',
        meta: {
          title: '新增/编辑'
        }
      },
      {
        id: '138',
        p_id: '120',
        path: 'organization/clinic/list',
        type: 'SUB_MENU',
        meta: {
          title: '诊所列表'
        }
      },
      {
        id: '139',
        p_id: '138',
        path: 'organization/clinic/edit',
        type: 'PAGE',
        meta: {
          title: '详情'
        }
      },
      {
        id: '140',
        p_id: '120',
        path: 'organization/supplier/list',
        type: 'SUB_MENU',
        meta: {
          title: '供应商列表'
        }
      },
      {
        id: '141',
        p_id: '140',
        path: 'organization/supplier/edit',
        type: 'PAGE',
        meta: {
          title: '新增/编辑'
        }
      }
    ]
  },
  {
    id: '121',
    path: 'platform/role/list',
    query: {},
    type: 'MENU',
    meta: {
      title: '菜单',
      icon: 'fa-wrench'
    },
    children: [
      {
        id: '129',
        p_id: '121',
        path: 'platform/role/list',
        query: {},
        type: 'SUB_MENU',
        meta: {
          title: '角色管理'
        }
      },
      {
        id: '130',
        p_id: '129',
        path: 'platform/role/permission',
        query: {},
        type: 'PAGE',
        meta: {
          title: '权限设置'
        }
      },
      {
        id: '131',
        p_id: '141',
        path: 'platform/menus/menus',
        query: {},
        type: 'SUB_MENU',
        meta: {
          title: '菜单管理'
        }
      },
      {
        id: '132',
        p_id: '143',
        path: 'platform/menus/resource',
        query: {},
        type: 'SUB_MENU',
        meta: {
          title: '功能管理'
        }
      }
    ]
  },
  {
    id: '150',
    path: 'member/list',
    query: {},
    type: 'MENU',
    meta: {
      title: '设置',
      icon: 'fa-cog'
    },
    children: [
      // 	{
      // 		"id": "151",
      // 		"p_id": "150",
      // 		"path": "store/general_set",
      // 		"query": {},
      // 		"type": "SUB_MENU",
      // 		"meta": {
      // 			"title": "通用设置"
      // 		}
      // 	},
      {
        id: '152',
        p_id: '150',
        path: 'member/list',
        query: {},
        type: 'SUB_MENU',
        meta: {
          title: '员工信息'
        }
      }
    ]
  }
];
export default {
  'get|/routes/query': option => {
    return {
      status: 200,
      message: 'success',
      data: routes
    };
  }
};
