@import "./table.less";
body {
  min-width: 1200px;
  margin: 0;
  padding: 0;
  font-size: 12px;
  line-height: 1.5em;
  font-family: @font-family;
  color: #333;
  background-color: #f2f2f2;
  position: relative;
  text-rendering: optimizeLegibility;
  -moz-osx-font-smoothing: grayscale;
  //font-smoothing: antialiased;
  //-webkit-font-smoothing: antialiased;
  font-smoothing: subpixel-antialiased;
  -webkit-font-smoothing: subpixel-antialiased;
}

//p {
//  margin: 0 0 10px;
//}
.block_5 {display:block; height:5px;}
.block_10 {display:block; height:10px;}
.block_15 {display:block; height:15px;}
.block_20 {display:block; height:20px;}
.block_25 {display:block; height:25px;}
.block_30 {display:block; height:30px;}
.block_35 {display:block; height:35px;}
.block_40 {display:block; height:40px;}
.block_45 {display:block; height:45px;}
.lr5 {padding-left: 5px; padding-right: 5px;}
.lr10 {padding-left: 10px; padding-right: 10px;}
.lr15 {padding-left: 16px; padding-right: 16px;}
.lr20 {padding-left: 20px; padding-right: 20px;}
.lr25 {padding-left: 26px; padding-right: 26px;}
.lr30 {padding-left: 30px; padding-right: 30px;}
.lr35 {padding-left: 36px; padding-right: 36px;}
.tb5 {padding-top: 5px; padding-bottom: 5px;}
.tb10 {padding-top: 10px; padding-bottom: 10px;}
.tb15 {padding-top: 15px; padding-bottom: 15px;}
.tb20 {padding-top: 20px; padding-bottom: 20px;}
.tb25 {padding-top: 25px; padding-bottom: 25px;}
.tb30 {padding-top: 30px; padding-bottom: 30px;}
.tb35 {padding-top: 35px; padding-bottom: 35px;}
.space2 {margin-right: 2px;}
.space4 {margin-right: 4px;}
.space6 {margin-right: 6px;}
.ml10{margin-left: 10px;}
.mb10{margin-bottom: 10px;}
.mb20{margin-bottom: 20px;}
.mt20 {margin-top: 20px;}
.mt14 {margin-top: 14px;}
.mt10 {margin-top: 10px;}
.mr10 {margin-right: 10px;}
.mr20 {margin-right: 20px;}
.mt5 {margin-top: 5px;}

.baseSubmitBtn {
  padding: 9px 35px 8px 35px;
  line-height: 17px;
  font-size: 14px;
  height: auto;
  font-weight: 400;
}

/* 详情布局 */
.widget-form-group {
  isplay: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
}
.widget-form-label {
  font-size: 14px;
  min-width: 115px;
  line-height: 30px;
  text-align: right;
  margin-right: 10px;
  color: #555;
}
.widget-form-content {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -moz-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  .radio-wrapper {
    margin-top: 4px;
  }
  .text {
    margin-top: 6px;
  }
}
.widget-form-content.radio-wrapper {
  margin-top: 4px;
}
.widget-form-content.text {
  margin-top: 6px;
}
.widget-form-group .form-control {
  max-width: 100%;
}
.widget-form-group+.widget-form-group {
  margin-top: 20px;
}


.table-condensed>tbody>tr>td, .table-condensed>tbody>tr>th, .table-condensed>tfoot>tr>td, .table-condensed>tfoot>tr>th, .table-condensed>thead>tr>td, .table-condensed>thead>tr>th {
  padding: 10px;
}
thead {
  background: #f2f2f2;
}
.note {
  color: #999;
  display: block;
  font-size: 12px;
  padding: 0 0;
  margin-top: 2px;
}

/*文本颜色*/
.text-info {
  color: #31708f;
}
.text-primary {
  color:#27f;
}
.text-stroke {
  color:#333;
}
.text-success {
  color:#0a0;
}
.text-warn {
  color:#fc0;
}
.text-notice {
  color:#f60;
}
.text-error {
  color:#e33;
}
.text-danger {
  color: #a94442;
}
.text-muted {
  color: #777;
}

.text-handle-orange {
  color:#F3971C;
}

/* 图片样式 */
.img-thumbnail {
  display: inline-block;
  max-width: 100%;
  height: auto;
  padding: 4px;
  line-height: 1.42857143;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  -webkit-transition: all .2s ease-in-out;
  -o-transition: all .2s ease-in-out;
  transition: all .2s ease-in-out;
  vertical-align: middle;
}
.img-rounded {
  border-radius: 3px;
}

/*  */
.media {
  margin-top: 15px
}
.media:first-child {
  margin-top: 0
}
.media,.media-body {
  //overflow: hidden;
  zoom:1
}
.media-body {
  width: 10000px
}
.media-object {
  display: block
}
.media-object.img-thumbnail {
  max-width: none
}
.media-right,.media>.pull-right {
  padding-left: 10px
}
.media-left,.media>.pull-left {
  padding-right: 10px
}
.media-body,.media-left,.media-right {
  display: table-cell;
  vertical-align: top
}
.media-middle {
  vertical-align: middle
}
.media-bottom {
  vertical-align: bottom
}
.media-heading {
  margin-top: 0;
  margin-bottom: 5px
}
.media-list {
  padding-left: 0;
  list-style: none
}

/* 列表上方状态tab模块 */
.panel-nav {
  height: 40px;
  position: relative;
  /*margin: 15px;*/
  margin-left:1px;
  padding: 0;
}
.panel-nav a {
  background: #fff;
  border: 1px solid #ddd;
  box-sizing: border-box;
  color: #333;
  float: left;
  font-size: 13px;
  height: 40px;
  line-height: 40px;
  padding: 0 12px;
  margin-left: -1px;
  list-style: none;
  position: relative;
  z-index: 1;
}
.panel-nav .ivu-tag {
  padding: 0 4px;
  margin: -2px 0px 2px 0;
}
.panel-nav a.active {
  background: #f2f2f2;
  border: 1px solid #ddd;
  border-bottom: 1px solid #f2f2f2;
}

/* 订单详情的表格样式 */
.ks-table, .ks-table th, .ks-table td {
  border: 1px solid #b7b7b7;
}
.ks-table {
  background: #fff;
  border-spacing: 0;
  border-collapse: collapse;
}
.ks-table th {
  background: #e6e6e6;
  padding: 2px 10px;
  text-align: right;
}
.ks-table td {
  padding: 2px 5px;
}
.ks-table-goods {
  min-width:900px;
}
.ks-table-goods th {
  text-align: center;
}
.ks-table .rinput {
  width: 60px;
  height: 19px;
  display: inline;
  text-align: center;
}
.ks-table .table_tt {
  font-weight: 700;
  text-align:center;
  background-color: #ccc;
}
.ks-table .ks-focus {
  padding: 1px 4px;
  border-radius: 2px;
}
.ks-table .ks-focus.active {
  background-color: #f35f5f;
  color: #fff;
}

.fixed-bottom-wrapper {
  background-color: #fff;
  position: fixed;
  bottom: 0;
  z-index: 20;
  right: 0;
  left: 118px;
  padding: 10px;
  text-align: center;
  box-shadow: 0 0 8px #9e9e9e9c;
}

.fixed-bottom-right-wrapper {
  background-color: #fff;
  position: fixed;
  bottom: 0;
  z-index: 10;
  width: 100%;
  left: 0px;
  padding: 10px;
  text-align: right;
}

/* 块标题 */
.app-inner .block-header:nth-of-type(n+2) {
  margin: 20px 0px;
}
.app-inner .block-header {
  background-color: #efefef;
  padding: 10px;
  margin: 0px 0px 20px 0;
  position: relative;
}
.app-inner .block-header .block-header-right {
  position: absolute;
  right: 10px;
  top: 4px;
}
.app-inner .block-header span {
  font-size: 14px;
  padding-left: 6px;
  font-weight: bold;
}

/* 最多两行文字，超出...替代*/
._3U0W {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  position: relative;
  -webkit-box-orient: vertical;
  word-break: break-all;
}

img {
  vertical-align: middle;
}

.form-warpper {
  .form-warpper_left {
    display:inline-block;
  }
  .form-warpper_right {
    display:inline-block;
    float:right;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  background-color: #d8d8d8;
}
::-webkit-scrollbar-button {
  display: none;
}
::-webkit-scrollbar-track {
  border-radius:10px;
}
::-webkit-scrollbar-thumb {
  background-color: #adafb1;
  &:hover{
    background-color: #919394;
  }
}
::-webkit-scrollbar-corner {
  display: none;
}
.el-input.is-disabled .el-input__inner{
  background: #f3f3f3;
  border-color: #bbb;
  font-size: 12px;
}
.empty{
  color: #CCCCCC;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.ranking-wrapper{
  .ivu-table{
    td {
      border-bottom: 1px solid #EFEFEF;
      height: 40px;
    }

    th {
      border-bottom: 1px solid #EFEFEF;
      height: 40px;
    }
  }
}
.ceiling {
  z-index: 100;
  position: sticky;
  top: 56px;
  width: 100%;
  background: rgb(242, 242, 242);
}
.statistics-sel{
  //top: 120px!important;
  z-index: 190!important;
}
.address-com{
  .el-scrollbar__wrap{
    max-height: 340px;
    width: 220px;
  }
}

.slot-l{
  text-align: right;
  min-width: 80px;
}
.slot-v{
}
.address-com{
  .el-cascader-panel{
   height: 300px;
  }
}
//.ivu-table-cell-tooltip-content{
//  display: -webkit-box;
//  -webkit-box-orient: vertical;
//  white-space: normal;
//  -webkit-line-clamp: 2;
//  text-overflow: ellipsis;
//  overflow: hidden;
//  word-break: break-all;
//}
.cell-val{
  color:#999;
}
.el-popover__reference{
  .el-input__inner{
    border-radius: 2px;
    border-color: #bbb;
    &:hover{
      border-color: #447cdd;
    }
  }
}
.el-select .el-input.is-focus .el-input__inner{
  border-color: #1157E5;
  outline: 0;
  box-shadow: 0 0 0 2px fade(#155bd4, 20%);
}
.el-select .el-input__inner:focus{
  border-color: #1157E5;
  outline: 0;
  box-shadow: 0 0 0 2px fade(#155bd4, 20%);
}
.el-select{
  .el-input__inner{
    border-radius: 2px!important;
  }
}
.spin-icon-load{
  animation: ani-demo-spin 1s linear infinite;
}

.list-fn-mb-distance {
  margin-bottom:10px;
}

.cursor-pointer{
  cursor: pointer;
}


// 处理v-show导致table的闪烁问题
.ivu-table-wrapper .ivu-table .ivu-table-header table {
  width: 100% !important;
}

.ivu-table-wrapper .ivu-table .ivu-table-body table {
  width: 100% !important;
}

// 套餐标签标识统一样式
.tc-tag {
  display: inline-block;
  transform: scale(0.8);
  font-size: 12px;
  font-weight: 400;
  color: #e98870;
  background: rgba(243,181,70,0.04);
  border-radius: 3px;
  border: 1px solid #e98870;
  padding: 1px 3px;
  min-width: 45px;
  height: 23px;
  line-height: 20px;
  text-align: center;
}

@tc-border-color: #d7d7d7;
.tc-table-style {
  .ivu-table-border:after {
  }

  .ivu-table-cell-tree {
    display: inline-flex;
    justify-content: center;
    align-items: center;
  }

  .ivu-table-cell-tree-level {
    display: none !important;
  }

  .ivu-table-row-tree {
    td {
      background: #f7f8fc;
      box-shadow: 0 1px 0 0 rgba(239, 239, 239, 0.5);
      &:first-of-type {
      }
      &:last-of-type {
      }
    }
  }

  .ivu-table-row-tree-child {
    td {
      color: #999999;
      &:first-of-type {
        border-left:1px solid @tc-border-color;
      }
      &:last-of-type {
      }
    }
    &:last-of-type {
      td {
        position: relative;
      }
    }
    & + :not(.ivu-table-row-tree-child) {
      td {
      }
    }
  }
}

// 地址选择器样式
.el-cascader {
  .el-cascader-panel{
    font-size: 12px;
  }
  .el-input {
    .el-input__inner{
      border: 1px solid #BCC3D7;
      border-radius: 2px;
      padding-left: 7px;
      font-size: 12px;
    }
  }
}

// 自定义吸顶(top写在style中)
.custom-sticky{
  position: sticky;
  z-index: 2;
  background-color: #fff;
}