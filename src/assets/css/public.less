@properties: ~"margin-right", ~"margin-left", ~"margin-top", ~"margin-bottom", ~"margin", ~"padding-top", ~"padding-bottom", ~"padding-left", ~"padding-right", ~"padding";
@propertiesShort: ~"mr", ~"ml", ~"mt", ~"mb", ~"m", ~"pt", ~"pb", ~"pl", ~"pr", ~"p";

// Loop through each property
.loopProperties(@index) when (@index <= length(@properties)) {
  @property: e(extract(@properties, @index));
  @propertyShort: e(extract(@propertiesShort, @index));

  // Loop through each value for the current property
  .loopValues(@valueIndex) when (@valueIndex <= 50) {
    .@{propertyShort}-@{valueIndex} {
        @{property}: unit(@valueIndex, px)!important;
    } .loopValues(@valueIndex + 1);
  }

  .loopValues(0);
  .loopProperties(@index + 1);
}

.loopProperties(1);

.cursor{
    cursor: pointer;
}
//提示语句，支持换行样式
.custom_white_space {
  .ivu-tooltip-inner {
    white-space: pre-wrap;
  }
}

.el-select{

}
