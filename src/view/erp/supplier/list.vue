<template>
  <div class="supplier-list-wrapper">
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <Col>
          <FormItem>
            <Input v-model="queryFormData.q" placeholder="供应商名称/联系人/联系电话" clearable />
          </FormItem>
        </Col>
        <Col>
          <FormItem style="text-align: left">
            <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
            <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
            <Button type="primary" @click="createSupplier">新增供应商</Button>
          </FormItem>
        </Col>
      </Row>
    </Form>

    <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 240">
      <template slot-scope="{ row }" slot="contact">
        {{ row.contact || '-' }}
      </template>
      <template slot-scope="{ row }" slot="contact_number">
        {{ row.contact_number || '-' }}
      </template>
      <template slot-scope="{ row }" slot="status">
        {{ row.status == 'ENABLE' ? '启用' : '停用' }}
      </template>
      <template slot-scope="{ row }" slot="operator">
        {{ row.operator || '-' }}
      </template>
      <template slot-scope="{ row }" slot="remark">
        {{ row.remark || '-' }}
      </template>
      <template slot-scope="{ row }" slot="create_time">
        {{ row.create_time | date_format }}
      </template>
      <template slot-scope="{ row }" slot="update_time">
        {{ row.update_time | date_format }}
      </template>
      <template slot-scope="{ row }" slot="action">
        <a @click="editSupplier(row)">编辑</a>
        <a class="ml10" @click="goDetail(row)">详情</a>
      </template>
    </Table>

    <div class="block_20"></div>
    <KPage
      :total="total"
      :page-size="+queryFormData.pageSize"
      :current="+queryFormData.page"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
      style="text-align: center"
    />
    <edit-supplier :supplierId="supplierId" :visible.sync="editVisible" @refresh="refresh"></edit-supplier>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'utils/util';
import EditSupplier from '@/view/erp/supplier/compontents/EditSupplier';
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  q: '' //供应商名称/联系人/电话
};

export default {
  name: 'list',
  components: {
    EditSupplier
  },
  mixins: [search],
  data() {
    return {
      apiName: 'getErpSupplierList',
      queryFormData: {
        ...init_query_form_data
      },

      tableCols: [
        { title: '编号', key: 'code', align: 'center' },
        { title: '供应商名称', key: 'name', align: 'center' },
        { title: '联系人', slot: 'contact', align: 'center' },
        { title: '联系电话', slot: 'contact_number', align: 'center' },
        { title: '状态', slot: 'status', align: 'center' },
        { title: '创建人', slot: 'operator', align: 'center' },
        { title: '创建时间', slot: 'create_time', align: 'center' },
        { title: '更新时间', slot: 'update_time', align: 'center' },
        { title: '备注', slot: 'remark', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' }
      ],
      list_count: {},
      supplierId: '',
      editVisible: false
    };
  },
  computed: {},
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },
    createSupplier() {
      this.supplierId = '';
      this.editVisible = true;
    },
    refresh() {
      this.loadList();
    },
    editSupplier(row) {
      this.supplierId = row.id;
      this.editVisible = true;
    },
    goDetail(row) {
      this.$router.push({
        path: '/erp/supplier/detail',
        query: {
          id: row.id
        }
      });
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  }
};
</script>

<style scoped lang="less">
.supplier-list-wrapper {
}
</style>

<style lang="less" scoped>
// current page common less
.ml10 {
  margin-left: 10px;
}
</style>
