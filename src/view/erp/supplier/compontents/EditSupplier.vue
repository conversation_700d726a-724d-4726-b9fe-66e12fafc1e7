<template>
  <Modal
    :value="visible"
    :title="supplierId ? '编辑供应商' : '新增供应商'"
    width="900px"
    @on-cancel="cancelHandle"
    :mask-closable="false"
  >
    <Form
      label-position="top"
      class="common-modal-form"
      :model="formData"
      :rules="formDataValidateRules"
      ref="supplierRef"
    >
      <div class="section-header">
        <div class="section-mark"></div>
        <div class="section-title">基本信息</div>
      </div>

      <div class="create-section">
        <FormItem label="供应商名称" prop="name" class="common-form-item">
          <Input v-model="formData.name" placeholder="请输入供应商名称" maxlength="20"></Input>
        </FormItem>

        <FormItem label="供应商状态" prop="status" class="common-form-item">
          <Select v-model="formData.status" placeholder="请选择供应商状态">
            <Option value="ENABLE">启用</Option>
            <Option value="DISABLE">停用</Option>
          </Select>
        </FormItem>

        <FormItem label="联系人" class="common-form-item">
          <Input v-model="formData.contact" placeholder="请输入联系人"></Input>
        </FormItem>

        <FormItem label="联系方式" class="common-form-item">
          <Input v-model="formData.contact_number" placeholder="请输入联系方式"></Input>
        </FormItem>

        <FormItem label="备注" class="common-form-item">
          <Input
            v-model="formData.remark"
            type="textarea"
            maxlength="50"
            show-word-limit
            :autosize="{ maxRows: 2, minRows: 2 }"
          ></Input>
        </FormItem>
      </div>
    </Form>
    <div slot="footer">
      <Button @click="cancelHandle">取消</Button>
      <Button type="primary" @click="confirmHandle" :loading="submitLoading">确定</Button>
    </div>
  </Modal>
</template>

<script>
import S from 'utils/util';
const initFormData = {
  name: '', //供应商名称
  status: '', //状态
  contact: '', //联系人
  contact_number: '', //手机号
  remark: '' //备注
};

export default {
  name: 'EditProductModal',
  mixins: [],
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    supplierId: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      formData: { ...initFormData },
      formDataValidateRules: {
        name: [{ required: true, message: '请输入供应商名称', trigger: 'blur' }],
        status: [{ required: true, message: '请选择供应商状态', trigger: 'blur' }]
      },
      submitLoading: false // 弹窗确定的loading
    };
  },

  computed: {},

  watch: {
    visible: {
      immediate: true,
      handler(val) {
        if (val && this.supplierId) {
          this.getErpSupplierDetail(this.supplierId);
        }
      }
    }
  },

  created() {},

  mounted() {},

  methods: {
    // 创建
    confirmHandle() {
      this.$refs['supplierRef'].validate(valid => {
        if (valid) {
          this.supplierId ? this.editSalesOrder() : this.createSalesOrder();
        } else {
          this.$Message.error('请填写完整');
        }
      });
    },

    // api - 创建供应商
    createSalesOrder() {
      if (!this.validateMobile(this.formData.contact_number)) {
        return;
      }
      this.submitLoading = true;
      let params = { ...this.formData };
      this.$api
        .createErpSupplier(params)
        .then(res => {
          this.$Message.success('创建成功');
          this.cancelHandle();
          this.$emit('refresh');
          this.submitLoading = false;
        })
        .catch(err => {
          this.submitLoading = false;
        });
    },

    // api - 创建供应商
    editSalesOrder() {
      if (!this.validateMobile(this.formData.contact_number)) {
        return;
      }
      this.submitLoading = true;
      let params = { ...this.formData, id: this.supplierId };
      this.$api
        .editErpSupplier(params)
        .then(res => {
          this.$Message.success('编辑成功');
          this.cancelHandle();
          this.$emit('refresh');
          this.submitLoading = false;
        })
        .catch(err => {
          this.submitLoading = false;
        });
    },

    // 关闭弹窗,清除数据
    cancelHandle() {
      this.formData = { ...initFormData };
      this.$emit('update:visible', false);
      this.$refs.supplierRef.resetFields();
    },

    getErpSupplierDetail(id) {
      let params = { id };
      this.$api.getErpSupplierDetail(params).then(res => {
        this.formData = res;
      });
    },

    // 手机号校验
    validateMobile(mobile, require) {
      console.log('=>(EditSupplier.vue:172) mobile', mobile);
      let flag = false;
      if (!mobile && !require) {
        flag = true;
        return flag;
      }
      const reg = /^1[3456789]\d{9}$/;
      if (!reg.test(mobile)) {
        this.$Message.error('请输入正确的手机号');
        return flag;
      }
      return true;
    }
  },

  destroyed() {}
};
</script>

<style scoped lang="less">
@import url('../../common/modal.less');
</style>
