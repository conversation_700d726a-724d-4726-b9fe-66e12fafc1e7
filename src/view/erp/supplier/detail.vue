<template>
  <div>
    <Tabs :value="tab" @on-click="changeTab" :animated="false">
      <!-- 详细资料 -->
      <TabPane label="详细资料" name="detail">
        <div class="block-header">
          基本信息
          <Button type="primary" class="edit-button" @click="showEditModal">编辑基础信息</Button>
        </div>
        <Form :model="formData" label-position="right" :label-width="80" class="basicInfo">
          <Row :gutter="40">
            <Col span="12">
              <FormItem label="供应商名称:">
                {{ formData.name }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="供应商编码:">
                {{ formData.code }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="供应商状态:">
                <RadioGroup v-model="formData.status">
                  <Radio label="ENABLE" disabled>启用</Radio>
                  <Radio label="DISABLE" disabled>停用</Radio>
                </RadioGroup>
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="备注:">
                {{ formData.remark }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="联系人:">
                {{ formData.contact }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="手机号:">
                {{ formData.contact_number }}
              </FormItem>
            </Col>
          </Row>
        </Form>
        <div class="block-header">系统信息</div>
        <Form :model="formData" label-position="right" :label-width="80" class="basicInfo">
          <Row :gutter="40">
            <Col span="12">
              <FormItem label="创建人:">
                {{ formData.operator }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="创建时间:">
                {{ formData.create_time | date_format }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="更新时间:">
                {{ formData.update_time | date_format }}
              </FormItem>
            </Col>
          </Row>
        </Form>
      </TabPane>

      <!-- 供货产品 -->
      <TabPane label="供货产品" name="suppliedProducts">
        <Table :columns="tableCols" :data="list" height="680">
          <template slot-scope="{ row }" slot="spec">
            {{ row.spec || '-' }}
          </template>

          <!-- 产品编码 -->
          <template slot-scope="{ row, index }" slot="code">
            <KLink v-if="row.code" :to="{ path: '/erp/product/detail', query: { id: row.id } }" target="_blank">{{
              row.code
            }}</KLink>
            <span v-else>{{ row.code }}</span>
          </template>
        </Table>
        <div class="block_20"></div>
        <KPage
          :total="total"
          :page-size="+queryFormData.pageSize"
          :current="+queryFormData.page"
          @on-change="handleCurrentChange"
          @on-page-size-change="handleSizeChange"
          style="text-align: center"
        />
      </TabPane>

      <TabPane label="采购管理" name="purchasingManagement">
        <Table :columns="purchaseTableCols" :data="purchaseList" height="270">
          <!-- 采购单编号 -->
          <template slot-scope="{ row, index }" slot="code">
            <KLink v-if="row.code" :to="{ path: '/erp/purchase/detail', query: { id: row.id } }" target="_blank">{{
              row.code
            }}</KLink>
            <span v-else>{{ row.code }}</span>
          </template>

          <template slot-scope="{ row }" slot="supplier_info">
            {{ row.supplier_info.supplier_name }}
          </template>
          <template slot-scope="{ row }" slot="create_time">
            {{ row.create_time | date_format }}
          </template>
          <template slot-scope="{ row }" slot="update_time" tooltip>
            {{ row.update_time | date_format }}
          </template>
        </Table>
        <div class="block_20"></div>
        <KPage
          :total="purchaseTotal"
          :page-size="+purchaseFormData.pageSize"
          :current="+purchaseFormData.page"
          @on-change="purchaseHandleCurrentChange"
          @on-page-size-change="purchaseHandleSizeChange"
          style="text-align: center"
        />
        <Table :columns="purchaseReturnTableCols" :data="purchaseReturnList" height="270" class="mt20">
          <!-- 采购退货单编码 -->
          <template slot-scope="{ row, index }" slot="code">
            <KLink
              v-if="row.code"
              :to="{ path: '/erp/purchase_return/detail', query: { id: row.id } }"
              target="_blank"
              >{{ row.code }}</KLink
            >
            <span v-else>{{ row.code }}</span>
          </template>
          <template slot-scope="{ row }" slot="supplier_info">
            {{ row.supplier_info.supplier_name }}
          </template>
          <template slot-scope="{ row }" slot="create_time">
            {{ row.create_time | date_format }}
          </template>
          <template slot-scope="{ row }" slot="update_time" tooltip>
            {{ row.update_time | date_format }}
          </template>
        </Table>
        <div class="block_20"></div>
        <KPage
          :total="purchaseReturnTotal"
          :page-size="+purchaseReturnFormData.pageSize"
          :current="+purchaseReturnFormData.page"
          @on-change="purchaseReturnHandleCurrentChange"
          @on-page-size-change="purchaseReturnHandleSizeChange"
          style="text-align: center"
        />
      </TabPane>
      <TabPane label="操作记录" name="operationRecord">
        <operationlog-record :b_type="b_type" :b_id="b_id" :isRecord="isRecord"></operationlog-record>
      </TabPane>
    </Tabs>

    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
    </div>

    <edit-supplier :supplierId="supplierId" :visible.sync="editVisible" @refresh="init"></edit-supplier>
  </div>
</template>

<script>
import EditSupplier from '@/view/erp/supplier/compontents/EditSupplier';
import OperationlogRecord from '../components/operationlog-record';
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  supplier_code: '' // 供应商编号
};

export default {
  data() {
    return {
      tab: 'detail',
      tabList: [
        { label: '详细资料', name: 'detail' },
        { label: '供货产品', name: 'suppliedProducts' },
        { label: '采购管理', name: 'purchasingManagement' },
        { label: '操作记录', name: 'operationRecord' }
      ],
      formData: {
        name: '1'
      },
      supplierId: '',
      editVisible: false,
      tableCols: [
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品编码', slot: 'code', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品状态', key: 'status_text', align: 'center' },
        { title: '市场价', key: 'sales_price', align: 'center' }
      ],
      list: [],
      total: 0,
      queryFormData: { ...init_query_form_data },
      purchaseTableCols: [
        { title: '采购单编号', slot: 'code', align: 'center' },
        { title: '采购日期', key: 'plan_date', align: 'center' },
        { title: '供应商名称', slot: 'supplier_info', align: 'center' },
        { title: '采购金额', key: 'total_price', align: 'center' },
        { title: '备注', key: 'remark', align: 'center' },
        { title: '审核状态', key: 'audit_status_text', align: 'center' },
        { title: '库存状态', key: 'stock_status_text', align: 'center' },
        { title: '创建人', key: 'operator', align: 'center' },
        { title: '创建时间', slot: 'create_time', align: 'center', width: 130 },
        { title: '更新时间', slot: 'update_time', align: 'center', width: 130 }
      ], // 采购单列表
      purchaseList: [], // 采购单列表
      purchaseFormData: { ...init_query_form_data },
      purchaseTotal: 0,
      purchaseReturnTableCols: [
        { title: '采购退货单编码', slot: 'code', align: 'center', width: '110' },
        { title: '采购单编号', key: 'purchase_code', align: 'center', width: '110' },
        { title: '供应商名称', slot: 'supplier_info', align: 'center' },
        { title: '退货金额', key: 'total_price', align: 'center' },
        { title: '退货日期', key: 'total_price', align: 'center' },
        { title: '退货原因', key: 'total_price', align: 'center' },
        { title: '备注', key: 'remark', align: 'center' },
        { title: '审核状态', key: 'audit_status_text', align: 'center' },
        { title: '库存状态', key: 'stock_status_text', align: 'center' },
        { title: '创建人', key: 'operator', align: 'center' },
        { title: '创建时间', slot: 'create_time', align: 'center', width: 130 },
        { title: '更新时间', slot: 'update_time', align: 'center', width: 130 }
      ], // 采购退货单
      purchaseReturnList: [], // 采购退货单
      purchaseReturnFormData: { ...init_query_form_data },
      purchaseReturnTotal: 0,
      b_type: 12,
      b_id: 0,
      isRecord: 0
    };
  },
  mounted() {
    this.init();
  },
  components: {
    EditSupplier,
    OperationlogRecord
  },
  methods: {
    init() {
      if (this.$route.query.id) {
        this.getSupplierDetail(this.$route.query.id);
        this.b_id = Number(this.$route.query.id);
      }
    },
    // tabs事件
    changeTab(name) {
      if (name == 'suppliedProducts') {
        this.getErpSupplierProduct();
      } else if (name == 'purchasingManagement') {
        this.getErpSupplierPurchase();
        this.getErpSupplierPurchaseReturn();
      } else if (name == 'operationRecord') {
        this.isRecord++;
      }
    },
    getSupplierDetail(id) {
      let params = { id };
      this.$api.getErpSupplierDetail(params).then(res => {
        this.formData = res;
        console.log('=>(detail.vue:234) this.formData', this.formData);
        this.queryFormData.supplier_code = res.code;
        this.purchaseFormData.supplier_code = res.code;
        this.purchaseReturnFormData.supplier_code = res.code;
      });
    },

    // 供货产品
    getErpSupplierProduct() {
      let params = { ...this.queryFormData };
      console.log('=>(detail.vue:241) params', params);
      this.$api.getErpSupplierProduct(params).then(res => {
        console.log('=>(detail.vue:242) res', res);
        this.list = res.list;
        this.total = res.total;
      });
    },
    getErpSupplierPurchase() {
      let params = { ...this.purchaseFormData };
      this.$api.getErpSupplierPurchase(params).then(res => {
        this.purchaseList = res.list;
        this.purchaseTotal = res.total;
      });
    },
    getErpSupplierPurchaseReturn() {
      let params = { ...this.purchaseFormData };
      this.$api.getErpSupplierPurchaseReturn(params).then(res => {
        this.purchaseReturnList = res.list;
        this.purchaseReturnTotal = res.total;
      });
    },

    // 分页
    handleSizeChange(val) {
      console.log('-> val', val);
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = val;
      this.getErpSupplierProduct();
    },
    handleCurrentChange(val) {
      console.log('-> val', val);
      this.queryFormData.page = val;
      this.getErpSupplierProduct();
    },
    purchaseHandleSizeChange(val) {
      console.log('-> val', val);
      this.purchaseFormData.page = 1;
      this.purchaseFormData.pageSize = val;
      this.getErpSupplierPurchase();
    },
    purchaseHandleCurrentChange(val) {
      console.log('-> val', val);
      this.purchaseFormData.page = val;
      this.getErpSupplierPurchase();
    },
    purchaseReturnHandleSizeChange(val) {
      console.log('-> val', val);
      this.purchaseReturnFormData.page = 1;
      this.purchaseReturnFormData.pageSize = val;
      this.getErpSupplierPurchaseReturn();
    },
    purchaseReturnHandleCurrentChange(val) {
      console.log('-> val', val);
      this.purchaseReturnFormData.page = val;
      this.getErpSupplierPurchaseReturn();
    },

    // 新建按钮
    showEditModal() {
      this.supplierId = this.$route.query.id;
      this.editVisible = true;
    },
    // createPurchase(){
    //   this.$router.push({
    //     path:'/erp/purchase/list'
    //   })
    // },
    // createPurchaseReturn(){
    //   this.$router.push({
    //     path:'/erp/purchase_return/list'
    //   })
    // },

    back() {
      this.$router.back();
    }
  }
};
</script>

<style lang="less" scoped>
.title {
  padding: 0 0 0 18px;
  margin-bottom: 10px;
}

.basicInfo {
  width: 60%;
  margin-left: 60px;
  .basic-item {
    width: 50%;
  }
  .remark {
    width: 100%;
  }
}

.ml-10 {
  margin-left: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

::v-deep .block-header span {
  font-size: 12px;
  padding: 0;
  font-weight: normal;
}

.edit-button {
  position: absolute;
  top: 3px;
  right: 5px;
}
</style>
