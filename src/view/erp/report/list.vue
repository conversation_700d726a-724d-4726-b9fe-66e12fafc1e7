<template>
  <div class="statements-wrapper">
    <div class="statements-item">
      <Row style="flex: 1">
        <!--				v-eleControl="getAuthCode(item.authType)"-->
        <Col v-for="(item, index) in reportList" :key="item.url" :span="6" class="item-box">
          <div
            class="report-item flex flex-item-align"
            @click="
              changeType({
                type: item.url,
                title: item.title,
                exportType: item.exportType,
                showWarehouse: item.showWarehouse,
                isDirectExport: item.isDirectExport
              })
            "
          >
            <div class="svg-box">
              <svg-icon class="report-svg" :icon-class="'DIRECT_' + item.svgUrl"></svg-icon>
            </div>
            <div class="content-box">
              <h4 class="title">
                {{ item.title }}
              </h4>
              <p class="content">
                {{ item.content }}
              </p>
            </div>
          </div>
        </Col>
      </Row>
    </div>
    <direct-report-export
      v-model="reportModalVisible"
      :apiName="apiName"
      :title="reportTitle"
      :dateType="dateType"
      :report-type="reportType"
      :reportExportType="reportExportType"
      :reportRadioList="reportRadioList"
      :showWarehouse="showWarehouse"
      :isDirectExport="isDirectExport"
    ></direct-report-export>
  </div>
</template>

<script>
import DirectReportExport from './components/DirectReportExport.vue';
import { getEnv } from '@/utils/runtime';
//  trigger ci
export default {
  name: 'DSReport',
  components: {
    DirectReportExport,
  },
  data() {
    const authTypeEnum = {};
    return {
      apiName: 'getErpFinancesReport',
      // 品项出货报表
      reportList: [
        {
          title: '进销存明细表',
          url: 'monthstockbill',
          content: '分别展示每个仓库的进销存明细，例如每次每个货品的入库数据，出库数据，结存数据等。',
          svgUrl: 'STOCK_CHANGE',
          isDown: true,
          authType: 'COST',
          exportType: 'stockChange',
          showWarehouse: true,
        },
        {
          title: '进销存报表',
          url: 'warehouseStockChangeReport',
          content: '分别展示每个货品在所有仓库和每个仓库的进销存汇总数据。',
          svgUrl: 'RECHARGE',
          isDown: true,
          authType: 'COST',
          exportType: 'stockChange',
          showWarehouse: true,
        },
        {
          title: '物流仓明细表',
          url: 'skuSalesStockDetail',
          content: '记录货品从发货到签收的虚拟物流仓的明细数据。',
          svgUrl: 'UN_HIS_DAY',
          isDown: true,
          authType: 'COST',
          exportType: 'stockChange',
          showWarehouse: false,
        },
        {
          title: '物流仓财务报表',
          url: 'skuSalesStock',
          content: '记录货品从发货到签收的虚拟物流仓的数据。',
          svgUrl: 'ADVANCE_COLLECT',
          isDown: true,
          authType: 'COST',
          exportType: 'stockChange',
          showWarehouse: false,
        },
        {
          title: '库存销量报表',
          url: 'stockSalesVolume',
          content: '统计所有产品本周、上周、本月、上月销售出库的销量数据，以及部分仓库的库存信息',
          svgUrl: 'OSTOCK',
          isDown: true,
          authType: 'COST',
          exportType: 'stockChange',
          showWarehouse: false,
          isDirectExport: true,
        },
      ],

      reportRadioMap: new Map([
        [
          'HOSPITAL_DIVIDE',
          [
            { label: '1', desc: '云直通手续费结算表', exportType: 'divide' },
            { label: '2', desc: '云直通服务费结算表', exportType: 'divide' },
          ],
        ],
      ]),

      // 店铺经营报表
      reportModalVisible: false,
      reportTitle: '',
      dateType: '', //  导出表的类型
      reportType: '',
      reportExportType: '',
      reportRadioList: [],
      showWarehouse: false,
      isDirectExport: false
    };
  },
  computed: {
    getAuthCode() {
      return authType => {
        if (authType === 'COST') {
          return 'E8EY6rRGRm';
        }
        if (authType === 'CUSTOMER_ORDER') {
          return 'E7zR1Mp47z';
        }
      };
    },
  },
  watch: {},
  created() {
    // if (getEnv() === 'production') {
    //   this.reportList = this.reportList.filter(item => !item.isSitOnly);
    // }
  },
  mounted() {},
  methods: {
    changeType({ type, title, exportType, reportRadioList, showWarehouse,isDirectExport = false }) {
      this.reportType = type;
      this.reportTitle = title;
      // todo 暂未用到
      this.reportExportType = exportType;
      this.reportRadioList = this.reportRadioMap.get(type);
      this.dateType = 'month'; // todo 第一版 先写死
      this.showWarehouse = showWarehouse;
      this.isDirectExport = isDirectExport;
      // if (type === 'OSTOCK' || type === 'STOCK_CHANGE') {
      //   this.dateType = 'month';
      // } else {
      //   this.dateType = 'date';
      // }
      this.reportModalVisible = true;
    },
  },
  beforeRouteUpdate(to, from, next) {
    // 点击菜单重置时间
    this.dateTimes = '';
    next();
  },
};
</script>

<style lang="less" scoped>
.statements-item {
  display: flex;

  .statements-label {
    display: block;
    width: 90px;
    text-align: right;
    margin-right: 10px;
  }
}

.margin-bottom20 {
  margin-bottom: 20px;
}

.import-content {
  height: 200px;

  .isDown {
    background: #fff8f0;
    color: #e18517;
    padding: 4px 4px 4px 14px;
    border: 1px solid #f2d0a5;
    border-radius: 4px;
  }

  .choose {
    color: rgba(17, 87, 229, 0.5);
  }

  .download {
    margin-left: 20px;
    color: rgb(155, 141, 141);
    border-bottom: 1px solid #ccc;
  }

  .error-report {
    margin-top: 20px;

    .download-error {
      margin-left: 15px;
      color: rgba(17, 87, 229, 0.5);
    }
  }
}
</style>

<style lang="less" scoped>
// common less
.cursor {
  cursor: pointer;
}

.hover {
  &:hover {
    color: #155bd4 !important;
  }
}

p {
  margin-bottom: 0px;
}

.item-box {
  margin-right: 24px;

  .report-item {
    padding: 20px 10px;
    margin-bottom: 24px;
    border: 1px solid #edeefc;
    border-radius: 6px;
    height: 100px;
    cursor: pointer;

    .svg-box {
      margin-right: 14px;

      .report-svg {
        font-size: 30px;
      }
    }

    .content-box {
      .title {
        font-size: 13px;
        font-weight: 800;
        //color: #155BD4;
        line-height: 20px;
        margin-bottom: 8px;
        color: #262626;
      }

      .content {
        color: #999;
      }
    }

    &:hover {
      box-shadow: 0px 0px 8px 0px rgba(21, 91, 212, 0.3);
      border-color: #155bd4 !important;
      border-width: 1.5px !important;

      .title {
        color: #155bd4 !important;
      }

      .content {
        color: #155bd4 !important;
      }
    }
  }
}

.active {
  box-shadow: 0px 0px 8px 0px rgba(21, 91, 212, 0.3);
  border-color: #155bd4 !important;
  border-width: 1.5px !important;

  .title {
    color: #155bd4 !important;
  }

  .content {
    color: #155bd4 !important;
  }
}

.statistics-title {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  margin-bottom: 12px;
}
</style>
