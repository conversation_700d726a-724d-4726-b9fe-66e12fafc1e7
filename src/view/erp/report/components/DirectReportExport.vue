<template>
  <div>
    <Modal :value="visible" :title="title" :mask-closable="false" @on-visible-change="visibleChange" width="600px">
      <div slot="header">
        <div>
          <span>正在导出:</span>
          <span v-if="title" class="titleText">{{ title }}</span>
        </div>
      </div>

      <div v-if="!isDirectExport" class="content">
        <Form ref="formData" label-colon :model="formData" :label-width="100">
          <FormItem prop="st" label="查询时间">
            <el-date-picker
              autocomplete="off"
              key="datePicker"
              ref="dateMonthPicker"
              v-if="dateType === 'date'"
              v-model="dateRange"
              type="daterange"
              value-format="yyyy-MM-dd"
              size="small"
              :picker-options="dateOptions"
              style="width: 403px"
              placeholder="选择日期"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="dateSelect"
            >
            </el-date-picker>
            <el-date-picker
              autocomplete="off"
              key="monthPicker"
              v-else
              v-model="formData.month"
              type="month"
              value-format="yyyy-MM"
              size="small"
              :picker-options="monthOptions"
              style="width: 403px"
              placeholder="选择月份"
              @change="selectMonth"
            >
            </el-date-picker>
          </FormItem>

          <FormItem label="查询仓库" v-if="showWarehouse">
            <Select
              v-model="formData.warehouse_code"
              class="filterable-select"
              placeholder="请选择仓库"
              transfer
              filterable
              clearable
              style="width: 403px"
              :disabled="!formData.month"
              @on-clear="formData.warehouse_code = ''"
            >
              <Option v-for="option in warehouse_list" :key="option.code" :value="option.code">{{
                option.name
              }}</Option>
            </Select>
          </FormItem>

          <div class="flex" v-if="reportRadioList.length">
            <FormItem label="报表类型" prop="typeRaido">
              <RadioGroup v-model="formData.type" @input="changeRadioType">
                <Radio v-for="(item, index) in reportRadioList" :key="index" :label="item.label">
                  <span>{{ item.desc }}</span>
                </Radio>
              </RadioGroup>
            </FormItem>
          </div>
        </Form>
      </div>

      <div slot="footer">
        <Button type="default" @click="cancel">取消</Button>
        <Button class="ml8" type="primary" :loading="loading" @click="success">导出</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import { getEnv } from '@/utils/runtime';
import downloadCenter from '@/mixins/downloadCenter';

let initFormData = {
  // st: '',
  // et: '',
  month: '',
  type: '',
  warehouse_code: '',
};
export default {
  name: 'ReportExport',
  components: {},
  mixins: [downloadCenter],
  model: {
    prop: 'visible',
    event: 'input',
  },
  props: {
    title: {
      type: String,
      default: '报表导出',
    },
    visible: {
      type: Boolean,
      default: () => false,
    },
    // 导出接口
    apiName: {
      type: String,
      default: '',
    },
    /**
     * @description 是否需要显示省公司，诊所
     * @node 同时通过当前属性，操控月组件和日期组件
     * */
    reportType: {
      type: String,
      default: '',
    },
    // 报表类型
    dateType: {
      type: String,
      default: '',
    },
    // 导出Key
    reportExportType: {
      type: String,
      default: '',
    },
    reportRadioList: {
      type: Array,
      default: () => [],
    },
    showWarehouse: {
      type: Boolean,
      default: false,
    },
    isDirectExport: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dateRange: '', // 导出时间范围
      monthTime: '',
      value: '',
      loading: false,
      formData: { ...initFormData },
      dateOptions: {
        disabledDate: times => {
          return times.getTime() > Date.now() - (getEnv() === 'production' ? 86400000 : 0);
        },
        firstDayOfWeek: 1,
      },
      monthOptions: {
        disabledDate: time => {
          const currentDate = new Date();
          const currentMonth = currentDate.getMonth();
          const currentYear = currentDate.getFullYear();

          const selectedMonth = time.getMonth();
          const selectedYear = time.getFullYear();

          // 计算当前年和月的数值
          const isCurrentOrPastMonth = selectedYear === currentYear && selectedMonth === currentMonth;

          // 计算是否在2024年12月之前
          const isBeforeDecember2024 = selectedYear < 2024 || (selectedYear === 2024 && selectedMonth < 11);

          // 禁用当月、历史月份和未来未到的月份
          return time.getTime() > Date.now() || isBeforeDecember2024 || isCurrentOrPastMonth;
        },
      },

      searchLoading: false,
      clinic_list: [],
      query: '',
      modalReportExportType: '',
      warehouse_list: [],
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.openCurrentDay();
  },
  beforeDestroy() {},
  methods: {
    // datePickerBlurEvent() {
    //   this.$refs.dateMonthPicker.picker &&
    //     (this.$refs.dateMonthPicker.picker.$children[0].rangeState.selecting = false);
    // },
    visibleChange(val) {
      if (!val) {
        this.cancel();
      } else {
        this.formData.type = this.reportType;
        // this.modalReportExportType = this.reportExportType;
      }
    },

    // 默认在测试,74环境打开今日
    openCurrentDay() {
      if (getEnv() === 'production') {
        this.maxDate = new Date() - 86400000;
      } else {
        this.maxDate = new Date();
      }
    },
    dateSelect(times) {
      console.log('-> %c times  ===    %o', 'font-size: 15px;color: #fa8c16 ;', times);
      this.formData.st = times?.[0] || '';
      this.formData.et = times?.[1] || '';
    },
    selectMonth(val) {
      if (!val) {
        document.body.click(); // 解决仓库下拉框禁用时卡死
        this.formData.warehouse_code = '';
      }
      this.getErpFinancesWarehouseList();

      console.log('=>(DirectReportExport.vue:222) ...');
    },
    changeRadioType(val) {
      let selected = this.reportRadioList.find(item => item.label === val);
      if (selected) {
        this.modalReportExportType = selected.exportType;
      }
    },
    // 关闭弹窗
    cancel() {
      this.clearData();
      document.body.click(); // 解决仓库下拉框禁用时卡死
      this.$emit('input', false);
    },

    // 点击确定按钮
    success() {
      if (this.dateType === 'date' && !this.formData.st && !this.isDirectExport) {
        this.$Message.error('请选择日期');
        return;
      }
      if (this.dateType !== 'date' && !this.formData.month && !this.isDirectExport) {
        this.$Message.error('请选择月份');
        return;
      }
      let params = { ...this.formData };
      this.loading = true;
      this.$api
        .getErpFinancesReport(params)
        .then(res => {
          console.log('=>(DirectReportExport.vue:239) res', res);
          this.download(res.url);
          this.cancel();
        })
        .finally(() => (this.loading = false));
    },

    // 清除数据
    clearData() {
      this.formData = { ...initFormData };
      this.dateRange = [];
      this.monthTime = '';
      this.warehouse_list = [];
      // 当需要省公司，诊所的时候，进行数据清除
      this.$refs.formData?.resetFields();
    },
    // searchMethod: debounce(function (query) {
    //   console.log('-> %c query  ===    %o', 'font-size: 15px;color: #fa8c16 ;', query);
    //   this.searchLoading = true;
    //   const params = { name: query, is_direct: '1', pageSize: 100 };
    //   this.$api.getCommonList(params).then(res => {
    //     this.searchLoading = false;
    //     this.clinic_list = res.list;
    //   });
    // }, 200),
    search() {},
    getErpFinancesWarehouseList() {
      let params = {
        month: this.formData.month,
      };
      this.$api.getErpFinancesWarehouseList(params).then(res => {
        this.warehouse_list = res.list;
      });
    },
    // 通过a标签下载
    download(url) {
      const downloadLink = document.createElement('a');
      downloadLink.setAttribute('href', url);
      downloadLink.setAttribute('target', '_blank');
      downloadLink.setAttribute('style', 'display:none');
      downloadLink.setAttribute('download', '');
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    },
  },
  filters: {},
};
</script>
<style lang="less" scoped>
.titleText {
  margin-left: 4px;
  font-size: 14px;
  font-weight: bold;
}

.ml8 {
  margin-left: 8px;
}
</style>
<style lang="less" scoped>
::v-deep .ivu-modal-body {
  height: 300px;
  overflow-y: auto;
}
</style>
