<template>
  <div class="purchase-list-wrapper">
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <Col>
          <FormItem>
            <Input v-model="queryFormData.name" placeholder="请输入产品名称或编码" clearable />
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Select v-model="queryFormData.warehouse_code" placeholder="请选择仓库" clearable>
              <Option v-for="item in warehouseList" :value="item.code" :key="item.code">{{ item.name }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Select v-model="queryFormData.type" placeholder="请选择出入库类型" clearable>
              <Option v-for="item in prodTypeDesc" :value="item.id" :key="item.id">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <supplier-search ref="supplierSearchRef" v-model="queryFormData.supplier_id" placeholder="请选择供应商"></supplier-search>
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <!--            <DatePicker-->
            <!--              v-model="queryFormData.plan_date"-->
            <!--              class="time-range"-->
            <!--              clearable-->
            <!--              format="yyyy-MM-dd"-->
            <!--              placeholder="请选择出入库时间"-->
            <!--              type="date"-->
            <!--              @on-change="date => (queryFormData.plan_date = date)"-->
            <!--            ></DatePicker>-->
            <k-date-picker-shortcut
              v-model="timeRange"
              @on-change="times => handleTimeChange(times)"
            ></k-date-picker-shortcut>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col>
          <FormItem style="text-align: left">
            <Button type="primary" @click="onSearch">筛选</Button>
            <Button type="default" class="mr-10 ml-10" @click="onResetSearch">重置</Button>
            <Button type="default" @click="exportTable" :loading="exportLoading">导出</Button>
          </FormItem>
        </Col>
      </Row>
    </Form>

    <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 300">
      <template slot-scope="{ row }" slot="spec">
        {{ row.spec || '-' }}
      </template>

      <template slot-scope="{ row }" slot="unit">
        {{ row.unit || '-' }}
      </template>

      <template slot-scope="{ row }" slot="supplier_name">
        {{ row.supplier_name || '-' }}
      </template>

      <template slot-scope="{ row }" slot="audit_status_text">
        <span :style="{ color: statusColor(row.audit_status) }">{{ row.audit_status_text }}</span>
      </template>

      <!-- 备注 -->
      <template slot-scope="{ row }" slot="remark">
        {{ row.remark || '-' }}
      </template>

      <!-- 创建人 -->
      <template slot-scope="{ row }" slot="operator">
        {{ row.operator || '-' }}
      </template>

      <template slot-scope="{ row }" slot="create_time">
        {{ row.create_time | date_format('YYYY-MM-DD') }}
      </template>

      <template slot-scope="{ row }" slot="update_time">
        {{ row.update_time | date_format('YYYY-MM-DD') }}
      </template>
    </Table>

    <div class="block_20"></div>
    <KPage
      :total="total"
      :page-size="+queryFormData.pageSize"
      :current="+queryFormData.page"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
      style="text-align: center"
    />
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'utils/util';
import downloadExcel from '@/mixins/downloadExcel';
import downloadCenter from '@/mixins/downloadCenter';
import KDatePickerShortcut from '@/components/k-date-picker-shortcut';
import supplierSearch from '@/components/remote-search/supplier-search'
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '', // 产品名称
  warehouse_code: '', // 仓库编号
  type: '', // 出入库类型
  supplier_id: '',
  st: '',
  et: '',
};

export default {
  name: 'list',
  components: { KDatePickerShortcut, supplierSearch },
  mixins: [search, downloadExcel, downloadCenter],
  data() {
    return {
      apiName: 'getErpProductstockBills',
      queryFormData: {
        ...init_query_form_data,
      },

      tableCols: [
        { title: '产品编码', key: 'product_code', align: 'center' },
        { title: '产品条码', key: 'barcode', align: 'center' },
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center', tooltip: true },
        { title: '所属仓库', key: 'warehouse_name', align: 'center' },
        { title: '供应商名称', slot: 'supplier_name', align: 'center' },
        { title: '类型', key: 'type_text', align: 'center' },
        { title: '出入库单号', key: 'type_code', align: 'center' },
        { title: '出入库日期', key: 'plan_date', align: 'center' },
        { title: '出入库数量', key: 'quantity', align: 'center' },
        { title: '库存余额', key: 'stock', align: 'center' },
        { title: '创建人', key: 'operator', align: 'center' },
      ],
      list_count: {},
      createTime: [],
      warehouseList: [], //仓库列表
      prodTypeDesc: [], // 状态列表
      downloadApiName: 'exportProOutOrder',
      exportLoading: false,
      timeRange: [],
    };
  },
  computed: {
    statusColor() {
      return function (status) {
        if (status == '10') {
          return '#2db7f5';
        } else if (status == '90') {
          return '#19be6b';
        } else if (status == '70') {
          return '#ed4014';
        }
      };
    },
  },
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.init();
    this.getErpProductstockBillsOptions();
  },
  mounted() {},
  methods: {
    exportTable() {
      this.exportLoading = true;
      this.createDownloadCenterMission('productStockBills', this.queryFormData);
      setTimeout(() => {
        this.exportLoading = false;
      }, 1000);
    },
    init() {
      this.getErpWarehouseList();
    },

    refresh() {
      this.loadList();
    },

    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.createTime = [];
      this.submitQueryForm();
      this.$refs['supplierSearchRef'].clear();
    },
    goDetail(row) {
      this.$router.push({
        path: '/erp/purchase/detail',
        query: {
          id: row.id,
        },
      });
    },
    getErpWarehouseList() {
      this.$api.getErpWarehouseList({ pageSize: 999 }).then(res => {
        this.warehouseList = res.list;
      });
    },
    getErpProductstockBillsOptions() {
      this.$api.getErpProductstockBillsOptions().then(res => {
        this.prodTypeDesc = S.descToArrHandle(res.prodTypeDesc);
      });
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.loadList();
    next();
  },
};
</script>

<style scoped lang="less">
.supplier-list-wrapper {
}

.mr-10 {
  margin-right: 10px;
}

// ::v-deep .ivu-table-cell .ivu-table-cell-slot {
//     display: block;
//     width: 90px;
//     overflow: hidden;
//     text-overflow: ellipsis;
//     white-space: nowrap;
//     word-break: break-all;
//     box-sizing: border-box;
//   }
</style>
