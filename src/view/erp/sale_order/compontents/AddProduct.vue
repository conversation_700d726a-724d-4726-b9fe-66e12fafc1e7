<template>
  <Modal :value="visible" title="选择商品" width="900px" @on-cancel="cancelHandle" :mask-closable="false">
    <div class="content">
      <div class="flex flex-item-align">
        <span>搜索：</span>
        <Input v-model="queryFormData.q" placeholder="产品名称/编码/条形码" @keyup.enter.native="getList">
          <Button slot="append" icon="ios-search" @click="getList"></Button>
        </Input>
      </div>

      <div class="mt10">
        <Table
          :columns="tableCols"
          :data="list"
          :height="560"
          :loading="tableLoading"
          @on-select-all="selectAll"
          @on-select="select"
          @on-select-all-cancel="selectAllCancel"
          @on-select-cancel="selectCancel"
        >
          <template slot-scope="{ row }" slot="purchase_price">
            {{ row.purchase_price ? `￥${row.purchase_price}` : '-' }}
          </template>
        </Table>
        <div class="block_20"></div>
        <KPage
          :total="total"
          :page-size="+queryFormData.pageSize"
          :current="+queryFormData.page"
          @on-change="handleCurrentChange"
          @on-page-size-change="handleSizeChange"
          style="text-align: center"
          :show-sizer="false"
        />
      </div>
    </div>

    <div slot="footer">
      已选: 商品({{ selectedList.length || 0 }})
      <Button @click="cancelHandle" class="ml10">取消</Button>
      <Button type="primary" @click="confirmHandle" :loading="submitLoading">确定</Button>
    </div>
  </Modal>
</template>

<script>
import S from 'utils/util';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  q: '', // 产品名称/编码/条形码
  status: 'ENABLE',
  r: ''
};

export default {
  name: 'list',
  mixins: [],
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    product_list: {
      type: Array,
      default: () => []
    },
    supplier_code: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      apiName: 'getErpProductList',
      tableLoading: false,
      list: [],
      tableCols: [
        { type: 'selection', align: 'center' },
        { title: '产品编号', key: 'code', align: 'center' },
        { title: '产品条码', key: 'barcode', align: 'center' },
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', key: 'spec', align: 'center' },
        { title: '产品单位', key: 'unit', align: 'center' },
        { title: '市场价', slot: 'sales_price', align: 'center' }
      ],
      queryFormData: {
        ...init_query_form_data
      },
      total: 0,

      submitLoading: false,

      selectedList: [] // 选中的数据
    };
  },

  computed: {},

  watch: {
    visible: {
      immediate: true,
      handler(val) {
        if (val) {
          this.getList();
        }
      }
    },
    product_list: {
      deep: true,
      handler(val) {
        if (Object.prototype.toString.call(val) == '[object array]') {
          this.selectedList = val;
        } else {
          this.selectedList = [];
        }
      }
    }
  },

  created() {},

  mounted() {},

  methods: {
    // 表格的选中
    selectAll(val) {
      this.selectedProduct(val);
    },
    select(val, row) {
      this.selectedProduct([row]);
    },
    selectAllCancel(val) {
      this.selectedProduct(this.list);
    },
    selectCancel(val, row) {
      this.selectedProduct([row]);
    },

    // 添加/取消勾选的商品
    selectedProduct(val) {
      val &&
        val.map(item => {
          let _existIndex = -1;
          this.selectedList.some((selected_item, selected_index) => {
            if (selected_item.id == item.id) {
              _existIndex = selected_index;
              return true;
            }
          });
          if (_existIndex >= 0) {
            this.selectedList.splice(_existIndex, 1);
          } else {
            this.selectedList.push(item);
          }
        });
    },
    // 根据id回显勾选的商品
    echoSelectedProduct() {
      this.list.forEach((item, index) => {
        this.selectedList.forEach(selected_item => {
          if (item.id == selected_item.id) {
            this.$set(this.list, index, { ...item, _checked: true });
          }
        });
      });
    },
    confirmHandle(val) {
      this.$emit('selectedList', this.selectedList);
      this.cancelHandle();
    },
    // 关闭弹窗,清除数据
    cancelHandle() {
      this.queryFormData = { ...init_query_form_data };
      this.$emit('update:visible', false);
    },

    handleSizeChange(val) {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.queryFormData.page = val;
      this.getList();
    },

    // api-获取商品列表
    getList() {
      this.tableLoading = true;
      let params = {
        ...this.queryFormData,
        supplier_code: this.supplier_code
      };
      this.$api[this.apiName](params)
        .then(res => {
          this.tableLoading = false;
          this.list = res.list;
          this.total = res.total;
          this.echoSelectedProduct();
        })
        .catch(err => {
          this.tableLoading = false;
        });
    }
  },

  destroyed() {}
};
</script>

<style scoped lang="less">
@import url('../../common/modal.less');
</style>
