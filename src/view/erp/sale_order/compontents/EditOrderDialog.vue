<template>
  <div>
    <Modal
      :value="visible"
      :title="orderId ? '编辑销售订单' : '新建销售订单'"
      width="900px"
      @on-cancel="cancelHandle"
      :mask-closable="false"
    >
      <Form
        label-position="top"
        class="common-modal-form"
        label-colon
        :model="formData"
        :rules="formDataValidateRules"
        ref="salesOrderForm"
      >
        <div class="section-header">
          <div class="section-mark"></div>
          <div class="section-title">基本信息</div>
        </div>

        <div class="create-section">
          <FormItem label="销售单编号" class="common-form-item">
            <Input v-model="formData.code" placeholder="不填系统将自动生成" maxlength="20" :disabled="isEdit"></Input>
          </FormItem>

          <FormItem label="下单时间" prop="deal_time" class="common-form-item">
            <DatePicker
              type="datetime"
              ref="deal-time"
              format="yyyy-MM-dd HH:mm"
              style="width: 100%"
              placeholder="请输入下单时间"
              :value="formData.deal_time"
              @on-change="date => (formData.deal_time = date)"
            ></DatePicker>
          </FormItem>
          <FormItem label="仓库名称" prop="warehouse_code" class="common-form-item">
            <SelectWarehousePopper
              class="flex"
              ref="selectWarehouse"
              @selectSup="selectWarehouse"
              :code="formData.warehouse_code"
            >
              <el-select
                :multiple="false"
                v-model="formData.warehouse_code"
                :multiple-limit="1"
                style="width: 100%"
                @visible-change="selectChange($event, 'selectWarehouse')"
                size="small"
                @on-change="supplierChange"
                popper-class="rxj-pop-select"
              >
                <el-option
                  v-for="(item, index) in selectWarehouseList"
                  :value="item.code"
                  :label="item.name"
                  :key="index + item.code"
                ></el-option>
              </el-select>
            </SelectWarehousePopper>
          </FormItem>
          <FormItem label="支付时间" prop="pay_time" class="common-form-item">
            <DatePicker
              type="date"
              ref="pay-time"
              style="width: 100%"
              placeholder="请选择支付时间"
              :value="formData.pay_time"
              @on-change="date => (formData.pay_time = date)"
            ></DatePicker>
          </FormItem>
          <FormItem label="销售平台" prop="platform" class="common-form-item">
            <Select
              v-model="formData.platform"
              ref="select-sale-platForm"
              style="width: 100%"
              placeholder="请选择销售平台"
            >
              <Option
                v-for="(item, index) in platFormList"
                :value="item.id"
                :label="item.desc"
                :key="index + item.id"
              ></Option>
            </Select>
          </FormItem>

          <FormItem label="平台单号" class="common-form-item">
            <Input v-model="formData.platform_code" placeholder="请填写平台单号" maxlength="20"></Input>
          </FormItem>

          <!--          <FormItem label="支付金额" prop="payment_fee" class="common-form-item">-->
          <!--            <InputNumber :min="0" v-model="formData.payment_fee" :active-change="false"  placeholder="请输入支付金额" style="width: 100%;"/>-->
          <!--          </FormItem>-->
          <FormItem label="运费" class="common-form-item">
            <InputNumber :min="0" v-model="formData.post_fee" placeholder="请输入运费" style="width: 100%" />
          </FormItem>
          <FormItem label="备注" class="common-form-item">
            <Input
              v-model="formData.remark"
              placeholder="请输入备注"
              type="textarea"
              maxlength="50"
              show-word-limit
              :autosize="{ maxRows: 2, minRows: 2 }"
            ></Input>
          </FormItem>
        </div>
        <div class="section-header mt10">
          <div class="section-mark"></div>
          <div class="section-title">产品信息</div>
        </div>

        <div class="table-wrapper">
          <div class="flex flex-item-end">
            <SelectProductPopper
              class="flex"
              ref="selectProduct"
              v-if="formData.warehouse_code"
              @selectedList="selectedList"
              :product_list="product_list"
              :apiName="'getErpWarehouseProds'"
              :warehouse_code="formData.warehouse_code"
            >
              <Button type="primary" @click="addProductE">添加商品</Button>
            </SelectProductPopper>
            <Button type="primary" v-else @click="addProductE">添加商品</Button>
          </div>
          <Table class="mt10" :loading="tableLoading" :columns="product_tableCols" :data="product_list">
            <template slot-scope="{ row, index }" slot="spec">
              {{ row.spec || '-' }}
            </template>
            <template slot-scope="{ row, index }" slot="unit">
              {{ row.unit || '-' }}
            </template>
            <!-- 采购数量 -->
            <template slot-scope="{ row, index }" slot="quantity">
              <InputNumber
                style="width: 100%"
                :min="0"
                v-model="product_list[index].quantity"
                :precision="0"
                :active-change="false"
                placeholder="销售数量"
                @on-focus="removeZero($event, 'quantity', index)"
                @on-change="val => changeUnitNum(val, index)"
              ></InputNumber>
            </template>

            <!-- 采购单价 -->
            <template slot-scope="{ row, index }" slot="purchase_price">
              <InputNumber
                style="width: 100%"
                :precision="2"
                :active-change="false"
                :min="0"
                v-model="product_list[index].purchase_price"
                placeholder="销售单价"
                @on-focus="removeZero($event, 'purchase_price', index)"
                @on-change="val => changeUnitPrice(val, index)"
              ></InputNumber>
            </template>

            <!-- 合计 -->
            <template slot-scope="{ row, index }" slot="total_price"> ￥{{ row.total_price }}</template>

            <template slot-scope="{ row, index }" slot="action">
              <a @click="deleteProduct(index)">删除</a>
            </template>
          </Table>
          <div class="mt10 flex flex-item-end">
            <span class="custom-label mr10">已选中产品：{{ product_list.length }}</span>
            <span class="custom-label">总金额：{{ getTotal }} 元</span>
          </div>
        </div>
        <div class="address-wrapper">
          <div class="section-header mt10">
            <div class="section-mark"></div>
            <div class="section-title">收货地址</div>
          </div>
          <div class="create-section">
            <FormItem label="客户姓名" prop="cg_name" class="common-form-item">
              <Input v-model="formData.cg_name" placeholder="请输入客户姓名" />
            </FormItem>
            <FormItem label="手机号" prop="cg_mobile" class="common-form-item">
              <Input v-model="formData.cg_mobile" placeholder="请输入手机号" />
            </FormItem>

            <div class="flex" style="width: 100%">
              <FormItem prop="cg_county" label="客户地址" class="common-form-item" style="width: 300px">
                <div class="addWrap">
                  <div class="addressBox">
                    <!--								<v-region v-model="selectedAddress"-->
                    <!--								          @values="regionChange" :disabled="!$route.query.isEdit &&!!$route.query.id"></v-region>-->
                    <el-cascader
                      v-model="selectedAddress"
                      :options="options"
                      placeholder="请选择联系地址"
                      size="small"
                      clearable
                      popper-class="address-com"
                      style="width: 300px"
                      @change="regionChange"
                    >
                    </el-cascader>
                  </div>
                </div>
              </FormItem>

              <div class="addressInput ml10" style="width: 100%">
                <FormItem label="详细地址" prop="cg_address" class="common-form-item" style="width: 100%">
                  <Input v-model="formData.cg_address" placeholder="详细地址" />
                </FormItem>
              </div>
            </div>
          </div>
        </div>
      </Form>

      <div slot="footer">
        <Button @click="cancelHandle">取消</Button>
        <Button type="primary" @click="confirmHandle" :loading="submitLoading">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import S from 'utils/util';
import { $operator } from '@/utils/operation';
import SelectPopper from '@/components/select-popper/select-popper';
import SelectWarehousePopper from '@/components/select-warehouse-popper/select-warehouse-popper';
import SelectProductPopper from '@/components/select-product-popper/select-product-popper';
import moment from 'moment';

import { CodeToText, regionData, TextToCode } from '@/utils/chinaMap';

const initFormData = {
  code: '', //销售单编号；如果不填写，创建时系统会自动生成
  warehouse_code: '', //仓库编号
  remark: '', //备注
  platform: '', //销售平台
  deal_time: '', //拍单时间
  pay_time: '', //支付时间
  platform_code: '', // 平台单号
  payment_fee: 0, //支付金额(包含运费)
  detail: '', //销售产品清单
  post_fee: 0, //运费
  cg_name: '', //客户姓名
  cg_mobile: '', //客户手机号
  cg_province: '', //省
  cg_city: '', //市
  cg_county: '', //区
  cg_address: '', //详细地址
};
const validateMobile = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入正确的手机号码'));
  } else {
    const reg = /^1[3456789]\d{9}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入正确的手机号码'));
    }
    callback();
  }
};

export default {
  name: 'list',
  mixins: [],
  components: { SelectPopper, SelectProductPopper, SelectWarehousePopper },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    orderId: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      formData: { ...initFormData },
      formDataValidateRules: {
        warehouse_code: [{ required: true, message: '请选择仓库', trigger: 'blur,change' }],
        platform: [{ required: true, message: '请选择销售平台', trigger: 'change' }],
        deal_time: [{ required: true, message: '请选择下单时间', trigger: 'change' }],
        pay_time: [{ required: true, message: '请选择支付时间', trigger: 'change' }],
        // payment_fee: [{ required: true,type: 'number', message: '请输入支付金额', trigger: 'change' },],
        cg_name: [{ required: true, message: '请输入客户姓名', trigger: 'change' }],
        // cg_mobile: [{ required: true, message: '请输入客户手机号', trigger: 'change' },],
        cg_mobile: [{ required: true, trigger: 'change', validator: validateMobile }],
        cg_county: [{ required: true, message: '请选择收货地址', trigger: 'change' }],
        cg_address: [{ required: true, message: '请填写详细地址', trigger: 'change' }],
      },
      submitLoading: false, // 弹窗确定的loading
      selectWarehouseList: [], // 选中的仓库
      tableLoading: false,
      product_tableCols: [
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '销售数量', slot: 'quantity', align: 'center' },
        { title: '销售单价', slot: 'purchase_price', align: 'center' },
        { title: '合计', slot: 'total_price', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' },
      ],
      product_list: [], // 商品数据
      platFormList: [], //采购平台列表
      selectedAddress: [], //用于回显的地址
      options: this.$lodash.cloneDeep(regionData),
      isEdit: false,
      validateErrorList: [],
    };
  },

  computed: {
    getTotalAmount() {
      return this.product_list.reduce((pre, cur) => {
        return $operator.add(pre, cur.total_price);
      }, 0);
    },
    getTotal() {
      return $operator.add(this.formData.post_fee, this.getTotalAmount);
    },
  },

  watch: {
    visible: {
      immediate: true,
      handler(val) {
        if (val) {
          this.getErpOrderPlatform();
          if (this.orderId) {
            this.getSalesOrderInfo();
            this.isEdit = true;
          }
        } else {
          this.isEdit = false;
          this.formData.deal_time = moment(new Date()).format('YYYY-MM-DD HH:mm');
        }
      },
    },
  },

  created() {},

  mounted() {},

  methods: {
    selectChange(e, node) {
      // poppver的ref
      let nodeList = ['selectWarehouse', 'selectProduct'];
      // select date 的ref
      let selectNodeList = ['select-sale-platForm', 'deal-time', 'pay-time'];
      if (e) {
        let hideList = nodeList.filter(item => item !== node);
        hideList.forEach(item => {
          this.$refs[item] && (this.$refs[item].showPop = false);
        });
        selectNodeList.forEach(item => {
          this.$refs[item] && (this.$refs[item].visible = false);
        });
      }
    },
    //改变单价
    changeUnitPrice(val, index) {
      console.log('-> %c val  === %o ', 'font-size: 15px;color: green;', val);
      if (!val) {
        val = 0;
      }
      this.product_list[index].purchase_price = val;
      this.calcPrice(index);
    },
    //改变单价
    changeUnitNum(val, index) {
      if (!val) {
        this.product_list[index].quantity = 0;
      }
      this.calcPrice(index);
    },

    // 去零
    removeZero(val, key, index) {
      console.log('-> %c val  === %o ', 'font-size: 15px;color: green;', val);
      if (Number(val) == 0) {
        this.product_list[index].key = null;
      }
    },
    // 计算价格
    calcPrice(index) {
      const { purchase_price, quantity } = this.product_list[index];
      if (purchase_price && quantity) {
        this.product_list[index].total_price = $operator.multiply(purchase_price, quantity);
      }
    },
    // 校验产品是否存在数量，单价，折扣未填写的情况
    validProductCalc() {
      let isUnValid = false;
      if (!this.product_list.length) {
        this.$Message.error('请选择商品');
        isUnValid = true;
        return true;
      }
      this.product_list &&
        this.product_list.some(item => {
          // || !item.purchase_price
          if (!item.quantity) {
            this.$Message.error(`商品${item.name}信息请填写完整`);
            isUnValid = true;
            return true;
          }
        });
      return isUnValid;
    },

    // 添加商品
    addProductE() {
      if (this.formData && this.formData.warehouse_code == '') {
        this.$Message.error('请先选择仓库');
        return;
      }
      this.selectChange(true);
    },
    // 删除商品
    deleteProduct(index) {
      this.product_list.splice(index, 1);
    },
    // 获取勾选的商品
    selectedList(list) {
      console.log(this.product_list);
      const selectedIds = this.product_list.map(item => item.id);
      list = list.filter(item => !selectedIds.includes(item.id));
      list &&
        list.forEach((item, index) => {
          this.product_list.push({
            ...item,
            quantity: null,
            purchase_price: null,
            total_price: 0,
          });
        });
    },
    // 创建
    confirmHandle() {
      const { deal_time } = this.formData;
      this.$refs.salesOrderForm.validate(valid => {
        if (valid) {
          // 商品计算未完成,不允许提交
          if (this.validProductCalc()) {
            return;
          }
          this.editSalesOrder();
        } else {
          this.validateShowErrorMessage();
        }
      });
    },

    //抛出单个form错误
    validateShowErrorMessage(ref = 'salesOrderForm') {
      let errorList =
        this.$refs[ref].$children &&
        this.$refs.salesOrderForm.$children.filter(error => error.validateState == 'error');
      this.$Message.error(`${errorList[0].validateMessage}`);
    },

    // 选中仓库
    selectWarehouse(val) {
      this.selectWarehouseList = [];
      this.formData.warehouse_code = '';
      if (!S.isEmptyObject(val)) {
        this.selectWarehouseList.push(val);
        this.formData.warehouse_code = val.code;
      }
      this.$refs.salesOrderForm.validateField('warehouse_code');
    },

    supplierChange(val) {
      console.log('val', val);
    },

    // 处理商品的价格数据，作为参数
    handlerProductDetails() {
      let detail = this.product_list.map(item => {
        return {
          product_code: item.code || item.product_code,
          quantity: item.quantity,
          price: item.purchase_price,
        };
      });
      return detail;
    },

    // api - 创建采购订单
    editSalesOrder() {
      this.submitLoading = true;
      let params = {
        id: this.orderId,
        ...this.formData,
        // deal_time: S.moment(this.formData.deal_time).valueOf(),
        // pay_time: S.moment(this.formData.pay_time).valueOf(),
        deal_time: this.formData.deal_time,
        pay_time: this.formData.pay_time,
        detail: this.handlerProductDetails(),
      };

      let apiName = this.orderId ? 'editSalesOrder' : 'createSalesOrder';
      this.$api[apiName](params)
        .then(res => {
          console.log('-> %c res  === %o ', 'font-size: 15px;color: green;', res);
          this.$Message.success(`${this.id ? '编辑' : '创建'}销售订单成功`);
          this.cancelHandle();
          this.$emit('refresh');
          this.submitLoading = false;
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
          this.submitLoading = false;
        });
    },

    // 关闭弹窗,清除数据
    cancelHandle() {
      this.formData = { ...initFormData };
      this.selectedAddress = [];
      this.$emit('update:visible', false);
      this.$refs.salesOrderForm.resetFields();
      this.product_list = [];
    },
    regionChange(address) {
      console.log('-> %c address  === %o ', 'font-size: 15px;color: green;', address);
      if (address.length) {
        const province = {
          name: CodeToText[address[0]],
          code: address[0],
        };
        const city = {
          name: CodeToText[address[1]],
          code: address[1],
        };
        const area = {
          name: CodeToText[address[2]],
          code: address[2],
        };
        console.log(province, city, area);
        this.formData.cg_province = CodeToText[address[0]];
        this.formData.cg_city = city.name;
        this.formData.cg_county = area.code ? area.name : '';
      } else {
        this.formData.cg_province = '';
        this.formData.cg_city = '';
        this.formData.cg_county = '';
      }
    },
    //订单回显
    getSalesOrderInfo() {
      console.log(this.orderId);
      this.$api.getErpOrderInfo({ id: this.orderId }).then(res => {
        console.log('-> %c res  === %o ', 'font-size: 15px;color: green;', res);
        this.formData.code = res.code;
        this.formData.platform = res.platform;
        this.formData.platform_code = res.platform_code;

        this.selectWarehouseList = [{ code: res.warehouse.code, name: res.warehouse.name }];
        this.formData.warehouse_code = res.warehouse_code;
        this.formData.deal_time = S.moment.unix(res.deal_time).format('YYYY-MM-DD HH:mm');
        // this.formData.payment_fee = S.moment(res.payment_fee).format("YYYY-MM-DD HH:mm")
        this.formData.pay_time = S.moment.unix(res.pay_time).format('YYYY-MM-DD');

        this.formData.post_fee = Number(res.post_fee);
        this.formData.post_fee = Number(res.post_fee);
        this.formData.remark = res.remark;
        this.formData.cg_name = res.cg_name;
        this.formData.cg_mobile = res.cg_mobile;
        this.formData.cg_address = res.cg_address;
        this.formData.cg_province = res.cg_province;
        this.formData.cg_city = res.cg_city;
        this.formData.cg_county = res.cg_county;
        console.log(TextToCode[res.cg_province]);
        const prov_code = TextToCode[res.cg_province].code;
        console.log('-> %c prov_code  === %o ', 'font-size: 15px;color: green;', prov_code);
        const city_code = TextToCode[res.cg_province][res.cg_city].code;
        console.log('-> %c city_code  === %o ', 'font-size: 15px;color: green;', city_code);
        const county_code = TextToCode[res.cg_province][res.cg_city][res.cg_county].code;
        console.log('-> %c county_code  === %o ', 'font-size: 15px;color: green;', county_code);
        this.selectedAddress = [prov_code, city_code, county_code];
        this.getErpOrderProductDetail();
      });
    },
    getErpOrderProductDetail() {
      this.$api.getErpOrderProductDetail({ id: this.orderId }).then(res => {
        console.log('-> %c res  === %o ', 'font-size: 15px;color: green;', res);
        const products = res.products;
        res.list.map(item => {
          item.name = products[item.product_id].name;
          item.spec = products[item.product_id].spec;
          item.unit = products[item.product_id].unit;
          item.purchase_price = Number(item.price);
          item.total_price = $operator.multiply(item.quantity, item.purchase_price);
          item.quantity = +item.quantity;
        });
        this.product_list = res.list;
      });
    },

    // api-获取销售平台配置
    getErpOrderPlatform() {
      this.$api.getErpOrderPlatform().then(res => {
        this.platFormList = S.descToArrHandle(res.platform);
      });
    },
  },

  destroyed() {},
};
</script>

<style scoped lang="less">
@import url('../../common/modal.less');
</style>
