<template>
  <div>
    <Modal
      :value="value"
      title="温馨提示"
      @on-visible-change="visibleChange"
      width="450"
      lock-scroll
      footer-hide
      class="error-info-modal"
      :mask-closable="false"
    >
      <p style="line-height: 30px; margin: 20px">
        本次共有<span class="error_msg">{{ fail_list.length }}条</span>{{ actionTypeText }}审核失败，
        <a @click="checkErrorTable">点击查看详情</a>
      </p>
    </Modal>
    <Modal v-model="errTableVisible" :title="title" width="950" lock-scroll :mask-closable="false">
      <div class="content">
        <Table :columns="tableCols" :data="fail_list" :height="450" class="mt10">
          <template v-slot:order_code="{ row }">
            {{ row.order_code }}
          </template>
          <template v-slot:error_msg="{ row }">
            <span class="error_msg">{{ row.fail_msg }}</span>
          </template>
        </Table>
      </div>

      <div slot="footer">
        <Button type="default" @click="close">关闭</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
export default {
  name: 'error-table',
  components: {},
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '错误报告'
    },
    // 当前批量选中的小程序
    fail_list: {
      type: Array,
      default: () => []
    },
    actionTypeText: {
      type: String,
      default: '审核'
    }
  },
  data() {
    return {
      tableCols: [
        { title: '序号', type: 'index', align: 'center', width: 60 },
        { title: '订单编号', slot: 'order_code', align: 'center', width: 300 },
        { title: '失败原因', slot: 'error_msg', align: 'center' }
      ],
      errTableVisible: false
    };
  },
  computed: {},
  watch: {
    fail_list(val) {
      console.log('-> %c val  === %o', 'font-size: 15px;color: green;', val);
    }
  },
  created() {},
  mounted() {},
  methods: {
    close() {
      this.errTableVisible = false;
    },
    visibleChange(val) {
      !val && this.$emit('input', val);
    },
    checkErrorTable() {
      console.log(this.fail_list);
      this.$emit('input', false);
      this.errTableVisible = true;
      console.log('-> %c this.errTableVisible  === %o', 'font-size: 15px;color: green;', this.errTableVisible);
    }
  },
  filters: {}
};
</script>

<style lang="less" scoped>
.content {
  min-height: 480px;
}

.mt10 {
  margin-top: 10px;
}
.error-info-modal {
  .ivu-modal-body {
    height: auto;
  }
}
</style>
