<template>
  <div class="container">
    <div v-if="detailLoading" class="demo-spin-container">
      <Spin fix />
    </div>

    <Tabs v-else :value="tab" :animated="false" @on-click="changeTab">
      <!-- 详细资料 -->
      <TabPane label="详细资料" name="detail">
        <div class="block-header">
          基本信息
          <Button
            v-if="formData.audit_status == '70' && invalid_status === '2'"
            type="primary"
            class="edit-button"
            @click="editOrder"
          >
            编辑基础信息
          </Button>
        </div>
        <div class="flex flex-between">
          <Form :model="formData" label-position="right" :label-width="80" class="basicInfo">
            <Row :gutter="40">
              <Col span="12">
                <FormItem label="销售单编号:">
                  {{ formData.code }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="下单时间 :">
                  {{ formData.deal_time | date_format('YYYY-MM-DD HH:mm') }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="平台单号 :">
                  {{ formData.platform_code || '-' }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="支付时间 :">
                  {{ formData.pay_time | date_format('YYYY-MM-DD') }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="仓库名称:">
                  <span v-if="formData.warehouse">{{ formData.warehouse.name }}</span>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="销售金额:"> ￥{{ formData.payment_fee }}</FormItem>
              </Col>
              <Col span="12">
                <FormItem label="备注:">
                  {{ formData.remark || '-' }}
                </FormItem>
              </Col>
            </Row>
          </Form>
        </div>
        <div class="block-header">收货信息</div>
        <Form :model="formData" label-position="right" :label-width="80" class="basicInfo">
          <Row :gutter="40">
            <Col span="12">
              <FormItem label="客户姓名:">
                {{ formData.cg_name }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="电话:">
                {{ formData.cg_mobile }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="收货地址:">
                {{ formData.cg_province }}{{ formData.cg_city }}{{ formData.cg_county }}{{ formData.cg_address }}
              </FormItem>
            </Col>
          </Row>
        </Form>

        <div class="block-header">系统信息</div>
        <Form :model="formData" label-position="left" :label-width="80" class="basicInfo">
          <Row :gutter="40">
            <Col span="12">
              <FormItem label="创建人:">
                {{ formData.operator }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="创建时间:">
                {{ formData.create_time | date_format }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="更新时间:">
                {{ formData.update_time | date_format }}
              </FormItem>
            </Col>
          </Row>
        </Form>
        <div class="block-header">产品</div>
        <Table :columns="tableCols" :data="list">
          <template slot="code" slot-scope="{ row, index }">
            <KLink v-if="row.code" :to="{ path: '/erp/product/detail', query: { id: row.id } }" target="_blank">
              {{ row.code }}
            </KLink>
            <span v-else>-</span>
          </template>
          <template slot="barcode" slot-scope="{ row, index }">
            {{ row.barcode || '-' }}
          </template>
          <template slot="spec" slot-scope="{ row, index }">
            {{ row.spec || '-' }}
          </template>
          <template slot="unit" slot-scope="{ row, index }">
            {{ row.unit || '-' }}
          </template>
          <template slot="note" slot-scope="{ row, index }">
            {{ row.note || '-' }}
          </template>
        </Table>
        <div class="total mt10">
          已选中产品：<span class="total-num">{{ this.list.length }}</span> 总金额：{{ this.formData.total_amount }}元
        </div>
        <div class="block_20" />
        <div class="block_20" />
      </TabPane>
      <!-- 出库记录 -->
      <TabPane label="出库记录" name="inboundDetail">
        <div class="mb-10 inbound-title flex flex-item-between">
          <span>待出库产品</span>
          <Button
            v-if="formData.audit_status === '90' && waitList.length !== 0 && invalid_status === '2'"
            type="primary"
            @click="productOut"
          >
            出库
          </Button>
        </div>
        <div v-if="waitList.length === 0" class="empty">暂无数据</div>
        <Table v-else :columns="waitTableCols" :data="waitList" height="300">
          <template slot="code" slot-scope="{ row }">
            <KLink
              v-if="row.product && row.product.code"
              :to="{ path: '/erp/product/detail', query: { code: row.product.code } }"
              target="_blank"
            >
              {{ row.product.code }}
            </KLink>
            <span v-else>-</span>
          </template>

          <template slot="barcode" slot-scope="{ row }">
            {{ row.product.barcode || '-' }}
          </template>

          <template slot="name" slot-scope="{ row }">
            {{ row.product.name }}
          </template>
          <template slot="spec" slot-scope="{ row }">
            {{ row.product.spec || '-' }}
          </template>
          <template slot="unit" slot-scope="{ row }">
            {{ row.product.unit }}
          </template>
        </Table>

        <div class="mb-10 mt10 inbound-title">出库记录</div>
        <div v-for="item in outboundList" :key="item.code" class="inboundTable">
          <Row class="mb-10">
            <Col :span="6" class="flex flex-item-align">
              <span class="info-title">出库信息:</span>
              出库单号：
              <KLink
                v-if="item.code"
                :to="{ path: '/erp/product-out/detail', query: { code: item.code } }"
                target="_blank"
              >
                {{ item.code }}
              </KLink>
              <span v-else>-</span>
            </Col>
            <Col :span="6">出库仓库：{{ item.warehouse_name }}</Col>
            <Col :span="6">出库时间：{{ item.actual_time | date_format('YYYY-MM-DD HH:mm:ss') }}</Col>
            <Col :span="6">操作人：{{ item.operator }}</Col>
          </Row>
          <Row v-if="item.show_sign_info === '1'" class="flex mb-10">
            <Col :span="6">
              <span class="info-title">签收信息:</span>
              <span>签收状态：{{ item.sign_status_text }}</span>
            </Col>
            <Col :span="6">签收时间：{{ item.sign_time | date_format }}</Col>
            <Col :span="6" class="mr20 flex-inline" style="margin-left: -14px">
              <Tooltip :max-width="360">
                <template v-slot:content>
                  <p>物流签收：物流快递单签收。</p>
                  <p>超时签收：商品出库后7天，系统自动签收。</p>
                  <p>无需签收：当商品的发货方式为自提或无需发货时，无需签收。</p>
                </template>
                <svg-icon iconClass="tip" style="color: #333; font-size: 13px" class="cursor mr-2"></svg-icon>
              </Tooltip>
              签收方式：{{ item.sign_way_text || '-' }}
            </Col>
          </Row>
          <Row class="flex flex-item-align" type="flex" :wrap="true">
            <Col :span="6" v-for="(express_info, idx) in item.express_detail" :key="idx + 'express'" class="flex mb-12">
              <span v-if="idx % 4 === 0" class="info-title" :style="{ visibility: idx !== 0 ? 'hidden' : 'unset' }"
                >物流信息:</span
              >
              <div>
                <div class="mr20">物流方式：{{ express_info.express_name }}</div>
                <div v-if="express_info.express_code !== 'ZTCK'">物流单号：{{ express_info.express_no }}</div>
                <div v-if="express_info?.status_text" class="mr20">物流状态：{{ express_info?.status_text }}</div>
              </div>
            </Col>
          </Row>

          <Table :columns="outboundTableCols" :data="item.detail" class="mb20">
            <template slot="code" slot-scope="{ row }">
              <KLink
                v-if="row.product && row.product.code"
                :to="{ path: '/erp/product/detail', query: { code: row.product.code } }"
                target="_blank"
              >
                {{ row.product.code }}
              </KLink>
              <span v-else>-</span>
            </template>
            <template slot="barcode" slot-scope="{ row }">
              {{ row.product.barcode || '-' }}
            </template>
            <template slot="name" slot-scope="{ row }">
              {{ row.product.name }}
            </template>
            <template slot="spec" slot-scope="{ row }">
              {{ row.product.spec || '-' }}
            </template>
            <template slot="unit" slot-scope="{ row }">
              {{ row.product.unit || '-' }}
            </template>
          </Table>
        </div>
        <div v-if="outboundList.length === 0" class="empty">暂无数据</div>
      </TabPane>

      <!-- 采购退货记录 -->
      <TabPane label="销售退货记录" name="returnDetail">
        <!--          <div  class="flex mb-10" style="justify-content: flex-end; align-items: center;" v-if="formData.audit_status == '90'">-->
        <!--            <Button type="primary">新建退货单</Button>-->
        <!--          </div>-->
        <Table :columns="returnTableCols" :data="returnList" height="578">
          <template slot="code" slot-scope="{ row }">
            <KLink :to="{ path: '/erp/sale_return/detail', query: { id: row.id } }" target="_blank">
              {{ row.code }}
            </KLink>
          </template>
          <template slot="reason" slot-scope="{ row }">
            {{ row.reason || '-' }}
          </template>
          <template slot="remark" slot-scope="{ row }">
            {{ row.remark || '-' }}
          </template>
          <template slot="create_time" slot-scope="{ row }">
            {{ row.create_time | date_format }}
          </template>
          <template slot="update_time" slot-scope="{ row }">
            {{ row.update_time | date_format }}
          </template>
        </Table>
      </TabPane>

      <TabPane label="操作记录" name="operationRecord">
        <operationlog-record :b_type="b_type" :b_id="b_id" :is-record="isRecord" />
      </TabPane>
    </Tabs>

    <div class="fixed-bottom-wrapper">
      <back-button />
      <Button
        v-if="stock_status === 'WAIT' && invalid_status === '2'"
        type="primary"
        style="margin: 0 10px"
        :disabled="formData.has_purchase === '1'"
        @click="invalidModalVisible = true"
      >
        作废
      </Button>
      <Button
        v-if="formData.audit_status === '10' && invalid_status === '2'"
        style="margin-right: 10px"
        type="error"
        @click="showRefuseModal"
      >
        审核驳回
      </Button>
      <Button
        v-if="formData.audit_status === '10' && invalid_status === '2'"
        type="primary"
        @click="passCheck"
        style="margin-right: 10px"
      >
        审核通过
      </Button>
      <Button type="primary" @click="oneClickPurchase" v-if="formData.show_one_key_purchase_button === '1'">
        一键采购常繁产品
      </Button>
    </div>

    <EditOrderDialog :order-id="orderId" :visible.sync="editVisible" @refresh="init" />
    <EditProductOut
      :code="order_code"
      :type="'20'"
      :is-detail-create="isDetailCreate"
      :visible.sync="productVisible"
      @refresh="getErpOrderOutboundDetail"
      @changeCreate="changeCreate"
    />
    <refuse-reason-modal :visible.sync="refuseModalVisible" :audit-sales-order="review" />
    <k-logistics-progress
      v-model="logisticsVisible"
      :is-logistics-detail="false"
      :progress_no="progress_no"
      :progress_code="progress_code"
      :express_detail="progress_express_detail"
    />
    <refuse-reason-modal :visible.sync="invalidModalVisible" :invalid-sales-order="invalidConfirm" :is-audit="false" />
    <error-table v-model="errorTableVisible" :fail_list="fail_list" />
    <!--	销售订单 - 一键采购（默认为true）	-->
    <EditPurchaseOrder
      :visible.sync="editPurchaseVisible"
      :isOneClickPurchase="true"
      :saleOrderCode="order_code"
      @refresh="init"
    ></EditPurchaseOrder>

    <Modal
      :value="reviewVisible"
      title="通过审核"
      width="580px"
      :mask-closable="false"
      @on-cancel="setReviewVisible(false)"
    >
      <span> 您确定要通过审核吗? </span>
      <div style="margin-top: 20px">
        <RadioGroup v-model="payMendMethod">
          <!-- <Radio label="1">
                <span>寄付</span>
            </Radio> -->
          <Radio label="2">
            <span>到付</span>
          </Radio>
          <Radio label="3">
            <span>寄付月结</span>
          </Radio>
        </RadioGroup>
      </div>
      <div slot="footer">
        <Button @click="setReviewVisible(false)">取消</Button>
        <Button type="primary" @click="reviewConfirm">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import OperationlogRecord from '../components/operationlog-record';
import EditProductOut from '@/view/erp/product-out/compontents/EditProductOut';
import EditOrderDialog from './compontents/EditOrderDialog';
import KLogisticsProgress from '@/components/k-logistics-progress';
import RefuseReasonModal from '@/components/RefuseReasonModal';
import EditPurchaseOrder from '@/view/erp/purchase/compontents/EditPurchaseOrder';
import util from '@/utils/util';
import { $operator } from '@/utils/operation';
import ErrorTable from './compontents/ErrorTable.vue';
import { date_format } from '../../../utils/filters';

const init_query_form_data = {
  // page: 1,
  // pageSize: 20,
  id: '', // 采购订单id
  code: '', // 采购订单编号
};

export default {
  components: {
    OperationlogRecord,
    EditOrderDialog,
    EditProductOut,
    RefuseReasonModal,
    KLogisticsProgress,
    ErrorTable,
    EditPurchaseOrder,
  },
  data() {
    return {
      invalidModalVisible: false,
      tab: 'detail',
      formData: {
        name: '1',
      },
      orderId: '',
      editVisible: false,
      productVisible: false,
      tableCols: [
        { title: '产品编码', slot: 'code', align: 'center' },
        { title: '产品条码', slot: 'barcode', align: 'center' },
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '销售数量', key: 'quantity', align: 'center' },
        { title: '销售单价', key: 'price', align: 'center' },
        { title: '合计', key: 'total_price', align: 'center' },
      ],
      list: [],
      total: 0,
      queryFormData: { ...init_query_form_data },
      productsList: [],
      b_type: 17,
      b_id: 0,
      isRecord: 0,
      showCard: false,
      refuseModalVisible: false,
      reason: '',
      waitTableCols: [
        { title: '产品编码', slot: 'code', align: 'center' },
        { title: '产品条码', slot: 'barcode', align: 'center' },
        { title: '产品名称', slot: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '出库数量', key: 'quantity', align: 'center' },
        { title: '已出库', key: 'p_qty', align: 'center' },
        { title: '待出库', key: 'w_qty', align: 'center' },
      ],
      waitList: [],
      outboundTableCols: [
        { title: '产品编码', slot: 'code', align: 'center' },
        { title: '产品条码', slot: 'barcode', align: 'center' },
        { title: '产品名称', slot: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '本次出库', key: 'quantity', align: 'center' },
        { title: '备注', key: 'note', align: 'center' },
      ],
      outboundList: [],
      returnTableCols: [
        { title: '销售退货单编码', slot: 'code', align: 'center', width: 120 },
        { title: '客户姓名', key: 'cg_name', align: 'center' },
        { title: '退货金额', key: 'total_price', align: 'center' },
        { title: '退货日期', key: 'plan_date', align: 'center' },
        { title: '退货原因', slot: 'reason', align: 'center', tooltip: true },
        { title: '备注', slot: 'remark', align: 'center', tooltip: true },
        { title: '审核状态', key: 'audit_status_text', align: 'center' },
        { title: '库存状态', key: 'stock_status', align: 'center' },
        { title: '创建人', key: 'operator', align: 'center' },
        { title: '创建时间', slot: 'create_time', align: 'center' },
        { title: '更新时间', slot: 'update_time', align: 'center' },
      ],
      returnList: [],
      detailLoading: false,
      order_code: '',
      isDetailCreate: false,
      stock_status: '',
      invalid_status: '',
      fail_list: [], // 错误列表
      errorTableVisible: false,
      logisticsVisible: false,
      progress_no: '',
      progress_code: '',
      progress_express_detail: [],

      reviewVisible: false,
      payMendMethod: '3',
      // signInfo: {
      //   show_sign_info: '',
      //   sign_status: '',
      //   sign_status_text: '',
      //   sign_time: '',
      //   sign_way_text: '',
      // },
      editPurchaseVisible: false,
    };
  },
  computed: {
    total_price() {
      return function (row) {
        return $operator.multiply(Number(row.quantity), Number(row.price));
      };
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    date_format,
    invalidConfirm(action, reason = '') {
      const params = {
        id: this.$route.query.id,
        reason,
      };
      this.$api.invalidErpSalesOrder(params).then(
        res => {
          this.fail_list = res.fail_data;
          console.log('-> %c this.fail_list  === %o', 'font-size: 15px;color: green;', this.fail_list);
          if (this.fail_list.length) {
            this.errorTableVisible = true;
          } else {
            this.$Message.success(`作废销售订单成功`);
          }
          this.getErpOrderInfo();
        },
      );
    },
    init() {
      if (this.$route.query.id || this.$route.query.code) {
        this.getErpOrderInfo();
      }
    },
    // tabs事件
    changeTab(name) {
      if (name == 'inboundDetail') {
        this.getErpOrderOutboundDetail();
      } else if (name == 'returnDetail') {
        this.getErpOrderReturnList();
      } else if (name == 'operationRecord') {
        this.isRecord++;
      }
    },
    getErpOrderInfo() {
      console.log(21321);
      this.detailLoading = true;
      const id = this.$route.query.id;
      const code = this.$route.query.code;
      let params = { id, code };
      this.$api.getErpOrderInfo(params).then(res => {
        this.formData = res;
        this.order_code = res.code;
        this.stock_status = res.stock_status;
        this.invalid_status = res.invalid_status;
        this.$router.replace({
          query: {
            ...this.$route.query,
            id: res.id,
          },
        });
        this.queryFormData.id = this.$route.query.id;
        this.b_id = Number(this.$route.query.id);
        this.detailLoading = false;
        this.getErpOrderProductDetail();
      });
    },
    getErpOrderProductDetail() {
      let params = { ...this.queryFormData };
      this.$api.getErpOrderProductDetail(params).then(res => {
        this.handlerDetail(res.list, res.products);
      });
    },

    // 处理明细数据，回显采购订单
    handlerDetail(list, products) {
      console.log('-> %c list, products  === %o', 'font-size: 15px;color: green;', list, products);
      let productDetailList = [];
      list &&
        list.forEach(item => {
          productDetailList.push({
            ...products[item.product_id],
            quantity: item.quantity,
            price: item.price,
            total_price: $operator.multiply(Number(item.quantity), Number(item.price)),
          });
        });
      console.log('-> %c productDetailList  === %o', 'font-size: 15px;color: green;', productDetailList);
      this.list = productDetailList;
      this.handlerTotalPrice(this.list);
    },

    handlerTotalPrice(list) {
      let sum = 0;
      list.forEach(item => {
        sum = $operator.add(sum, Number(item.total_price));
      });
      this.formData.total_amount = sum;
    },

    getErpOrderOutboundDetail() {
      let params = { id: this.$route.query.id };
      // let params ={id: '1'}
      this.$api.getErpOrderOutboundDetail(params).then(res => {
        this.waitList = res.wait_detail;
        this.outboundList = res.outbound_detail;
        // this.signInfo.show_sign_info = res.outbound_detail.show_sign_info;
        // this.signInfo.sign_status = res.outbound_detail.sign_status;
        // this.signInfo.sign_status_text = res.outbound_detail.sign_status_text;
        // this.signInfo.sign_time = res.outbound_detail.sign_time;
        // this.signInfo.sign_way_text = res.outbound_detail.sign_way_text;
      });
    },
    getErpOrderReturnList() {
      let params = { id: this.$route.query.id };
      this.$api.getErpOrderReturnList(params).then(res => {
        this.returnList = res.returns;
        this.purchaseReturnTotal = res.total;
      });
    },
    editOrder() {
      this.orderId = this.$route.query.id;
      this.editVisible = true;
    },
    // 审核按钮
    passCheck() {
      this.setReviewVisible(true);
      // this.$Modal.confirm( {
      //   title: '通过审核',
      //   content: '您确定要通过该审核吗？',
      //   onOk: () => {
      //     this.review( 'PASS' )
      //   },
      // } )
    },

    // 审核驳回
    showRefuseModal() {
      this.refuseModalVisible = true;
    },

    refuseCancel() {
      this.refuseModalVisible = false;
    },

    /**
     * @description 审核/驳回的接口
     * @param { action } 审核的状态
     * @param { reason } 驳回的原因
     * */
    review(action, reason) {
      const params = {
        id: this.$route.query.id,
        action,
        reason: '',
        freight_payment_method: this.payMendMethod,
      };
      let isPass = true;
      if (reason) {
        params.reason = reason;
        isPass = false;
      }
      this.$api.changeSalesOrderStatus(params).then(
        res => {
          this.fail_list = res.fail_data;
          console.log('-> %c this.fail_list  === %o', 'font-size: 15px;color: green;', this.fail_list);
          if (this.fail_list.length) {
            this.errorTableVisible = true;
          } else {
            this.$Message.success(`${isPass ? '通过审核成功' : '驳回审核成功'}`);
          }
          this.payMendMethod = '3';
          this.init();
        },
      );
    },

    submitRefuseReason() {
      if (this.reason == '') {
        this.$Message.error('请输入驳回原因');
        return;
      }
      this.refuseCancel();
      this.review('REJECT', this.reason);
    },

    showCardHandle() {
      this.showCard = !this.showCard;
    },

    goReturnDetail(row) {
      window.open(`/erp/sale_return/detail?id=${row.id}`);
    },
    back() {
      this.$router.back();
    },

    // 出库按钮
    productOut() {
      this.isDetailCreate = true;
      this.productVisible = true;
    },

    // 出库弹窗关闭时间
    changeCreate(val) {
      this.isDetailCreate = val;
    },

    showLogistics(item) {
      this.logisticsVisible = true;
      console.log('=>(detail.vue:435) item', item);
      this.progress_express_detail = item.express_detail;
      this.progress_no = item.express_info.express_no;
      this.progress_code = item.express_info.express_code;
    },

    setReviewVisible(bool) {
      this.reviewVisible = bool;
    },
    reviewConfirm() {
      this.review('PASS');
      this.setReviewVisible(false);
    },

    // 一键采购
    oneClickPurchase() {
      this.editPurchaseVisible = true;
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  //position: relative;
  .card {
    position: absolute;
    top: 0;
    right: 0;
    width: 350px;
    // height: 200px;
  }

  .card-show {
    position: absolute;
    top: 14px;
    right: 16px;
  }

  .total {
    text-align: right;

    .total-num {
      margin-right: 15px;
      color: #fd715a;
    }
  }

  .inbound-title {
    position: relative;
    padding-left: 10px;
    line-height: 32px;

    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      display: block;
      width: 3px;
      height: 14px;
      content: '';
      background: #0052cc;
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);
    }
  }
}

.info-title {
  font-size: 12px;
  font-weight: bolder;
  color: #000;
  margin-right: 24px;
}

// ::v-deep .ivu-card-body{
//     padding: 0;
// }

.basicInfo {
  width: 60%;
  margin-left: 60px;

  .basic-item {
    width: 50%;
  }

  .remark {
    width: 100%;
  }
}

.buttonGroup {
  text-align: center;
}

.ml-10 {
  margin-left: 10px;
}

.mr-10 {
  margin-right: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

::v-deep .block-header span {
  font-size: 12px;
  padding: 0;
  font-weight: normal;
}

.edit-button {
  position: absolute;
  top: 3px;
  right: 5px;
}

.demo-spin-container {
  display: inline-block;
  width: 100%;
  height: 100%;
}

.customList {
  overflow-y: auto;

  > div {
    min-width: 200px;
  }
}
</style>
