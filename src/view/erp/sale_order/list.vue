<template>
  <div class="purchase-list-wrapper">
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch" label-colon>
      <div>
        <div class="no-wrap">
          <FormItem>
            <Input v-model="queryFormData.code" placeholder="销售订单编号" clearable />
          </FormItem>
          <FormItem>
            <Input v-model="queryFormData.cg_name" placeholder="客户姓名" clearable />
          </FormItem>
          <FormItem>
            <Input v-model="queryFormData.platform_code" placeholder="平台单号" clearable />
          </FormItem>
          <FormItem>
            <Input v-model="queryFormData.product_keyword" placeholder="产品名称/编码/条形码" clearable />
          </FormItem>
          <FormItem>
            <Select v-model="queryFormData.list_status" style="width: 180px" placeholder="状态" clearable>
              <Option v-for="item in tabStatus" :value="item.id" :key="item.id">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </div>
        <Row>
          <FormItem>
            <Select v-model="queryFormData.push_status" style="width: 180px" placeholder="推送状态" clearable>
              <Option v-for="item in pushStatus" :value="item.code" :key="item.code">{{ item.name }}</Option>
            </Select>
          </FormItem>
          <FormItem>
            <Select v-model="queryFormData.stock_status" style="width: 180px" placeholder="出库状态" clearable>
              <Option v-for="item in stockStatus" :value="item.code" :key="item.code">{{ item.name }}</Option>
            </Select>
          </FormItem>
          <FormItem>
            <Select v-model="queryFormData.warehouse_code" placeholder="仓库" clearable>
              <Option v-for="item in warehouseList" :value="item.code" :key="item.code">{{ item.name }}</Option>
            </Select>
          </FormItem>
          <FormItem>
            <Select v-model="queryFormData.platform" style="width: 180px" placeholder="销售平台" clearable>
              <Option v-for="item in platFormList" :value="item.id" :key="item.id">{{ item.desc }}</Option>
            </Select>
          </FormItem>
          <FormItem>
            <Input v-model="queryFormData.remark" placeholder="备注" clearable />
          </FormItem>
        </Row>
        <Row>
          <FormItem style="text-align: left">
            <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
            <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
            <Button type="primary" class="mr10" @click="createOrder" v-eleControl="'E8EOBJBobo'">新建销售订单</Button>
            <Button type="primary" class="mr10" @click="exportOrder">导出待出库订单列表</Button>
          </FormItem>
        </Row>
      </div>
    </Form>
    <div class="table-wrapper">
      <div class="panel-nav">
        <a class="nav" :class="{ active: !queryFormData.list_status }" @click.prevent.capture="onStatusChange('')">
          全部
        </a>
        <a
          class="nav"
          v-for="item in tabStatus"
          :key="item.id"
          :class="{ active: $route.query.list_status == item.id }"
          @click.prevent.capture="onStatusChange(item.id)"
        >
          {{ item.desc }}
          <Tag :color="getTagColor(item.id)">{{ list_count[item.id] }}</Tag>
        </a>
      </div>
      <Table
        :loading="tableLoading"
        :columns="tableCols"
        ref="salesTable"
        @on-select="handleSelect"
        @on-select-cancel="handleSelectCancel"
        @on-select-all="handleSelectAll"
        @on-select-all-cancel="handleCancelAll"
        :data="list"
        :height="$store.state.app.clientHeight - 397"
      >
        <template slot-scope="{ row }" slot="sales_order_number">
          {{ row.code }}
        </template>

        <!--      <template slot-scope="{row}" slot="payment_fee">-->
        <!--        ￥{{ row.payment_fee }}-->
        <!--      </template>-->
        <template slot-scope="{ row }" slot="warehouse_name">
          {{ (row.warehouse_info && row.warehouse_info.warehouse_name) || '-' }}
        </template>
        <template slot-scope="{ row }" slot="cg_name">
          {{ row.cg_name || '-' }}
        </template>
        <template slot-scope="{ row }" slot="remark">
          {{ row.remark || '-' }}
        </template>
        <template slot-scope="{ row }" slot="audit_status_text">
          <!--        <span :style="getAuditColor(row.audit_status)">{{ row.audit_status_text }}</span>-->
          <status-text :status="row.audit_status"
            ><span>{{ row.audit_status_text }}</span></status-text
          >
        </template>

        <template slot-scope="{ row }" slot="invalid_status_text">
          <!--        <span :style="getAuditColor(row.audit_status)">{{ row.audit_status_text }}</span>-->
          <p style="font-size: 13px; color: #000000; font-weight: 800; font-family: Microsoft YaHei">
            {{ row.invalid_status_text || '-' }}
          </p>
        </template>
        <template slot-scope="{ row }" slot="freight_payment_method_text">
          {{ row.freight_payment_method_text || '-' }}
        </template>

        <template slot-scope="{ row }" slot="push_status">
          <span v-if="row.push_status === '4'"
            >{{ row.push_status_text }}
            <Tooltip placement="bottom" max-width="200" :content="row.hangup_text">
              <Icon type="md-help-circle" style="font-size: 16px" class="cursor" />
            </Tooltip>
          </span>
          <span v-else>{{ row.push_status_text }}</span>
        </template>

        <template slot-scope="{ row }" slot="pay_time">
          {{ row.pay_time | date_format('YYYY-MM-DD') }}
        </template>
        <template slot-scope="{ row }" slot="deal_time">
          {{ row.deal_time | date_format('YYYY-MM-DD HH:mm') }}
        </template>
        <template slot-scope="{ row }" slot="create_time">
          {{ row.create_time | date_format }}
        </template>
        <template slot-scope="{ row }" slot="update_time">
          {{ row.update_time | date_format }}
        </template>
        <template slot-scope="{ row }" slot="action">
          <a v-if="row.audit_status == '70' && row.invalid_status === '2'" class="mr10" @click="editOrder(row)">编辑</a>
          <a @click="goDetail(row)">详情</a>
        </template>
      </Table>

      <div class="block_20"></div>
      <div class="page-box">
        <div class="batch-audit-box">
          <span
            >已选中<span class="select-num">{{ getSelectedItemsLen }}</span
            >项</span
          >
          <div class="action-box">
            <Dvd />
            <Dvd />
            <Dvd />
            <Button type="primary" size="small" @click="SelectAll">全选</Button>
            <Dvd />
            <Button type="primary" size="small" @click="cancelSelectAll">重置</Button>
            <Dvd />
            <Button type="primary" size="small" :disabled="!getSelectedItemsLen" @click="passHandler">
              审核通过
            </Button>
            <Dvd />
            <Button type="primary" size="small" :disabled="!getSelectedItemsLen" @click="refuseModalVisible = true">
              审核驳回
            </Button>
            <Dvd />
            <Dvd />
            <Button type="primary" size="small" :disabled="!getSelectedItemsLen" @click="invalidOrder">
              作废订单
            </Button>
          </div>
        </div>
        <KPage
          :total="total"
          :page-size="+queryFormData.pageSize"
          :current="+queryFormData.page"
          @on-change="handleCurrentChange"
          @on-page-size-change="handleSizeChange"
          style="text-align: right"
        />
      </div>

      <edit-order-dialog :orderId="orderId" :visible.sync="editVisible" @refresh="onSearch"></edit-order-dialog>
      <refuse-reason-modal :visible.sync="refuseModalVisible" :auditSalesOrder="auditSalesOrder"></refuse-reason-modal>
      <refuse-reason-modal
        :visible.sync="invalidModalVisible"
        :invalidSalesOrder="invalidConfirm"
        :is-audit="false"
      ></refuse-reason-modal>
      <export-order-modal
        :export-visible.sync="exportOrderVisible"
        :warehouse-list="warehouseList"
      ></export-order-modal>
      <error-table v-model="errorTableVisible" :fail_list="fail_list"></error-table>

      <Modal
        :value="reviewVisible"
        title="通过审核"
        width="580px"
        @on-cancel="setReviewVisible(false)"
        :mask-closable="false"
      >
        <span> 您确定要通过审核吗? </span>
        <div style="margin-top: 20px">
          <RadioGroup v-model="payMendMethod">
            <!-- <Radio label="1">
                <span>寄付</span>
            </Radio> -->
            <Radio label="2">
              <span>到付</span>
            </Radio>
            <Radio label="3">
              <span>寄付月结</span>
            </Radio>
          </RadioGroup>
        </div>
        <div slot="footer">
          <Button @click="setReviewVisible(false)">取消</Button>
          <Button type="primary" @click="reviewConfirm">确定</Button>
        </div>
      </Modal>
    </div>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'utils/util';
import EditOrderDialog from './compontents/EditOrderDialog';
import RefuseReasonModal from '@/components/RefuseReasonModal';
import ExportOrderModal from './compontents/ExportOrderModal';
import ErrorTable from './compontents/ErrorTable';
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  code: '', // 名称
  r: '',
  // audit_status: '',//审核状态
  push_status: '', //推送状态
  cg_name: '', //客户姓名
  platform: '', // 销售平台
  platform_code: '', // 销售单号
  remark: '',
  list_status: '', // 订单状态
  warehouse_code: '',
  product_keyword: '',
};

export default {
  name: 'list',
  mixins: [search],
  components: { EditOrderDialog, RefuseReasonModal, ExportOrderModal, ErrorTable },
  data() {
    return {
      apiName: 'getErpOrderList',
      queryFormData: {
        ...init_query_form_data,
      },
      tableCols: [
        { type: 'selection', align: 'center', fixed: 'left', width: 50 },
        { title: '销售单编号', slot: 'sales_order_number', align: 'center' },
        { title: '销售平台', key: 'platform_text', align: 'center' },
        { title: '平台单号', key: 'platform_code', align: 'center' },
        { title: '客户姓名', slot: 'cg_name', align: 'center' },
        { title: '销售单仓库', slot: 'warehouse_name', align: 'center' },
        // { title: '销售单金额', slot: 'payment_fee', align: 'center' },
        { title: '下单时间', slot: 'deal_time', align: 'center', width: 80 },
        { title: '支付时间', slot: 'pay_time', align: 'center', width: 80 },
        { title: '备注', slot: 'remark', align: 'center' },
        { title: '审核状态', slot: 'audit_status_text', align: 'left', width: 80 },
        { title: '作废状态', slot: 'invalid_status_text', align: 'center', width: 80 },
        { title: '运费状态', slot: 'freight_payment_method_text', align: 'center', width: 80 },
        { title: '推送状态', slot: 'push_status', align: 'center', width: 80 },
        { title: '出库状态', key: 'stock_status_text', align: 'center', width: 80 },
        { title: '创建人', key: 'operator', align: 'center', width: 80 },
        { title: '创建时间', slot: 'create_time', align: 'center', width: 80 },
        // { title: '更新时间', slot: 'update_time', align: 'center', width: 80 },
        { title: '操作', slot: 'action', align: 'center', width: 80 },
        // { title: '负责人', key: 'operator', align: 'center' },
      ],
      list_count: {},
      auditStatus: [
        { code: '10', name: '审核中' },
        { code: '90', name: '审核完成' },
        { code: '70', name: '已拒绝' },
      ], //审核状态选项
      orderId: '', //当前编辑的销售订单ID
      editVisible: false, ///编辑弹窗flag
      platFormList: [], //采购平台列表
      pushStatus: [
        { name: '不推送', code: '0' },
        { name: '待推送', code: '1' },
        { name: '挂起', code: '4' },
        { name: '已推送', code: '9' },
      ],
      selectedItems: {}, //选中的订单
      refuseModalVisible: false, //审核驳回弹窗
      invalidModalVisible: false, //作废订单弹窗
      stockStatus: [
        { name: '未发货', code: 'WAIT' },
        { name: '部分发货', code: 'PART' },
        { name: '全部发货', code: 'COMPLETE' },
      ],
      tabStatus: {},
      warehouseList: [],
      exportOrderVisible: false, // 导出待出库订单列表
      fail_list: [], //审核失败的订单列表
      errorTableVisible: false, //审核失败的订单列表
      reviewVisible: false,
      payMendMethod: '3',
    };
  },
  computed: {
    getAuditColor() {
      return status => {
        switch (status) {
          case '10':
            return { color: '#2db7f5' };
          case '90':
            return { color: '#19be6b' };
          case '70':
            return { color: '#ed4014' };
        }
      };
    },
    getTagColor() {
      return type => {
        switch (type) {
          case 'TO_BE_EXAMINED': // 待审核
          case 'TO_BE_DELIVERED': // 诊所待付款
          case 'COM_W_PAY': // 省公司待付款
            return 'warning';
          case 'WAIT_SHIP': // 待发货
            return 'primary';
          case 'REJECTED': // 待发货
            return 'error';
          case 'DELIVERED': // 已发货
          case 'FINISHED': // 已完成
            return 'success';
          default: // 已取消
            return 'default';
        }
      };
    },
    getSelectedItemsLen() {
      return Object.keys(this.selectedItems).length;
    },
  },
  watch: {},
  created() {
    this.getErpWarehouseList();
    this.getOptions();
  },
  mounted() {
    this.getErpOrderPlatform();
  },
  methods: {
    getErpWarehouseList() {
      this.$api.getErpWarehouseList().then(res => {
        this.warehouseList = res.list;
      });
    },
    invalidOrder() {
      this.invalidModalVisible = true;
    },
    invalidConfirm(action, reason = '') {
      const params = {
        id: this.getAuditOrderIds(),
        reason,
      };
      this.$api.invalidErpSalesOrder(params).then(res => {
        this.$Message.success(`作废销售订单成功`);
        this.fail_list = res.fail_data;
        console.log('-> %c this.fail_list  === %o', 'font-size: 15px;color: green;', this.fail_list);
        if (this.fail_list.length) {
          this.errorTableVisible = true;
        }
        this.selectedItems = {};
        this.onSearch();
      });
    },
    getOptions() {
      this.$api
        .getErpSalesOrderOption()
        .then(res => {
          this.tabStatus = S.descToArrHandle(res.listStatusDesc);
        })
        .finally(() => {
          this.queryFormData = S.merge(this.queryFormData, this.$route.query);
          this.submitQueryForm(true);
        });
    },
    onStatusChange(status) {
      this.queryFormData.page = 1;
      this.queryFormData.list_status = status;
      this.submitQueryForm();
    },
    SelectAll() {
      this.$refs.salesTable.selectAll(true);
      this.handleSelectAll(this.list);
    },
    cancelSelectAll() {
      this.$refs.salesTable.selectAll(false);
      this.selectedItems = {};
    },
    //选中项发生变化时就会触发
    handleSelect(selection, row) {
      this.$set(this.selectedItems, row.id, row);
    },
    //选中项发生变化时就会触发
    handleSelectCancel(selection, row) {
      this.$delete(this.selectedItems, row.id);
    },
    //点击全选时触发
    handleSelectAll(selection) {
      selection.forEach(item => {
        this.$set(this.selectedItems, item.id, item);
      });
    },
    //点击全选时触发
    handleCancelAll(selection) {
      for (let k in this.list) {
        if (this.list[k].id in this.selectedItems) {
          this.$delete(this.selectedItems, this.list[k].id);
        }
      }
    },
    //反选全部触发
    editOrder(row) {
      this.orderId = row.id;
      this.editVisible = true;
    },
    // 新建采购订单
    createOrder() {
      this.orderId = '';
      this.editVisible = true;
    },

    refresh() {
      this.loadList();
    },

    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    goDetail(row) {
      this.$router.push({
        path: '/erp/sale_order/detail',
        query: {
          id: row.id,
        },
      });
    },

    // api-获取销售平台配置
    getErpOrderPlatform() {
      this.$api.getErpOrderPlatform().then(res => {
        this.platFormList = S.descToArrHandle(res.platform);
      });
    },
    loadList() {
      this.tableLoading = true;
      this.$api[this.apiName](this.queryFormData)
        .then(
          data => {
            this.total = +data.total;
            this.list = this.handler(data.list);
            this.list_count = data.status_count;
          },
          error => {}
        )
        .finally(() => {
          this.tableLoading = false;
        });
    },
    handler(list) {
      for (let k in list) {
        for (let j in this.selectedItems) {
          if (list[k].id == this.selectedItems[j].id) {
            list[k]['_checked'] = true; // 选中已选项
          }
        }
        // if (S.inArray(Number(list[k].id), this.disabledItemIds)) {
        //   list[k]['_disabled'] = true // 选中已选项
        // }
      }
      return list;
    },
    passHandler() {
      this.setReviewVisible(true);
      // this.$Modal.confirm( {
      //   title: '通过审核',
      //   content: '您确定要通过该审核吗？',
      //   onOk: () => {
      //     this.auditSalesOrder( 'PASS' )
      //   },
      // } )
    },
    auditSalesOrder(action, reason = '') {
      const params = {
        id: this.getAuditOrderIds(),
        action,
        reason: reason,
        freight_payment_method: this.payMendMethod,
      };
      let isPass = action === 'PASS' ? true : false;
      this.$api.changeSalesOrderStatus(params).then(res => {
        this.fail_list = res.fail_data;
        console.log('-> %c this.fail_list  === %o', 'font-size: 15px;color: green;', this.fail_list);
        if (isPass && this.fail_list.length) {
          this.errorTableVisible = true;
        } else {
          this.$Message.success(`${isPass ? '通过审核成功' : '驳回审核成功'}`);
        }
        this.selectedItems = {};
        !isPass && (this.refuseModalVisible = false);
        this.setReviewVisible(false);
        this.payMendMethod = '3';
        this.onSearch();
      });
    },
    getAuditOrderIds() {
      const ids = Object.values(this.selectedItems).map(item => item.id);
      return ids.join(',');
    },
    exportOrder() {
      this.exportOrderVisible = true;
    },
    setReviewVisible(bool) {
      this.reviewVisible = bool;
    },
    reviewConfirm() {
      this.auditSalesOrder('PASS');
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  },
};
</script>

<style scoped lang="less">
.page-box {
  position: relative;

  .batch-audit-box {
    position: absolute;
    line-height: 33px;
    display: flex;

    .select-num {
      color: #1157e5;
      margin: 0 2px;
    }

    .action-box {
      .btn-pass {
        margin: 0 12px;
      }
    }
  }
}

a.active {
  border-bottom: 1px solid #dddddd;
}
</style>
