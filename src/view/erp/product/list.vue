<template>
  <div class="product-list-wrapper">
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <Col>
          <FormItem>
            <Input v-model="queryFormData.q" placeholder="产品名称/产品编码/产品条码" clearable />
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Select v-model="queryFormData.status" placeholder="请选择状态" clearable>
              <Option value="ENABLE">上架</Option>
              <Option value="DISABLE">下架</Option>
            </Select>
          </FormItem>
        </Col>
        <Col>
          <FormItem style="text-align: left">
            <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
            <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
            <Button type="primary" @click="createProduct('')">新建产品</Button>
          </FormItem>
        </Col>
      </Row>
      <!-- <FormItem label="产品编码：">
          <Input v-model="queryFormData.code" placeholder="请输入产品编码" clearable/>
        </FormItem>
        <FormItem label="产品条码：">
          <Input v-model="queryFormData.barcode" placeholder="请输入产品条码" clearable/>
        </FormItem> -->
    </Form>

    <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 240">
      <template slot-scope="{ row }" slot="spec">
        {{ row.spec || '-' }}
      </template>
      <template slot-scope="{ row }" slot="operator">
        {{ row.operator || '-' }}
      </template>
      <template slot-scope="{ row }" slot="create_time">
        {{ row.create_time | date_format }}
      </template>
      <template slot-scope="{ row }" slot="update_time">
        {{ row.update_time | date_format }}
      </template>
      <template slot-scope="{ row }" slot="action">
        <a href="javascript:;" class="mr10" @click="editProduct(row)">编辑</a>
        <a @click="goDetail(row)">详情</a>
      </template>
    </Table>

    <div class="block_20"></div>
    <KPage
      :total="total"
      :page-size="+queryFormData.pageSize"
      :current="+queryFormData.page"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
      style="text-align: center"
    />
    <EditProductModal :productId="productId" :visible.sync="editVisible" @refresh="refresh"></EditProductModal>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'utils/util';
import EditProductModal from './compontents/EditProductModal';
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '', // 名称
  r: ''
};

export default {
  name: 'list',
  mixins: [search],
  components: {
    EditProductModal
  },
  data() {
    return {
      apiName: 'getErpProductList',
      queryFormData: {
        ...init_query_form_data
      },

      tableCols: [
        { title: '产品编号', key: 'code', align: 'center' },
        {
          title: '产品条码',
          key: 'barcode',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.barcode || '-')
        },
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', key: 'unit', align: 'center' },
        { title: '产品类型', key: 'type_text', align: 'center' },
        { title: '备注', key: 'remark', render: (h, { row }) => h('span', {}, row.remark || '-'), align: 'center' },
        { title: '状态', key: 'status_text', align: 'center' },
        { title: '供应商', key: 'supplier_name', align: 'center' },
        { title: '成本价', key: 'purchase_price', align: 'center' },
        { title: '市场价', key: 'sales_price', align: 'center' },
        { title: '创建人', slot: 'operator', align: 'center' },
        { title: '创建时间', slot: 'create_time', align: 'center' },
        { title: '更新时间', slot: 'update_time', align: 'center' },
        { title: '操作', slot: 'action', align: 'center', width: 120 }
      ],
      list_count: {},
      editVisible: false,
      productId: '' // 编辑的商品id
    };
  },
  computed: {},
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    createProduct() {
      this.productId = '';
      this.editVisible = true;
    },
    // 编辑
    editProduct(row) {
      this.productId = row.id;
      this.editVisible = true;
    },

    refresh() {
      this.loadList();
    },
    checkDetail(row) {
      this.$router.push({
        path: '/erp/product/detail',
        query: {
          id: row.id
        }
      });
    },
    goDetail(row) {
      this.$router.push({
        path: '/erp/product/detail',
        query: {
          id: row.id
        }
      });
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  }
};
</script>

<style scoped lang="less">
.supplier-list-wrapper {
}
</style>
