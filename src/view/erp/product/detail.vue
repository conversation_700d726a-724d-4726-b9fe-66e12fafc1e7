<template>
  <div class="create-wrapper">
    <div class="demo-spin-container" v-if="detailLoading">
      <Spin fix></Spin>
    </div>

    <Tabs v-else :value="tab" @on-click="changeTab" :animated="false">
      <TabPane label="详细资料" name="detail">
        <Form label-position="right" :model="formData" :label-width="80" ref="productRef">
          <div class="block-header">
            基本信息
            <Button type="primary" class="edit-button" @click="showEditModal">编辑基础信息</Button>
          </div>
          <div class="common-form-item">
            <Row :gutter="40">
              <Col span="12">
                <FormItem label="产品名称:">
                  {{ formData.name }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="产品编码:" prop="code">
                  {{ formData.code }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="产品条码:" prop="code">
                  {{ formData.barcode || '-' }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="产品规格:">
                  {{ formData.spec || '-' }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="状态:">
                  <RadioGroup v-model="formData.status">
                    <Radio label="ENABLE" disabled>上架</Radio>
                    <Radio label="DISABLE" disabled>下架</Radio>
                  </RadioGroup>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="产品单位:">
                  {{ formData.unit }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="成本价:">
                  {{ formData.purchase_price }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="市场价:">
                  {{ formData.sales_price }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="供应商:">
                  {{ formData.supplier_name }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="备注:">
                  {{ formData.remark || '-' }}
                </FormItem>
              </Col>
            </Row>
          </div>
          <div class="block-header">系统信息</div>
          <Form :model="formData" label-position="right" :label-width="80" class="basicInfo">
            <div class="common-form-item">
              <Row :gutter="40">
                <Col span="12">
                  <FormItem label="创建人:">
                    {{ formData.operator }}
                  </FormItem>
                </Col>
                <Col span="12">
                  <FormItem label="创建时间:">
                    {{ formData.create_time | date_format }}
                  </FormItem>
                </Col>
                <Col span="12">
                  <FormItem label="更新时间:">
                    {{ formData.update_time | date_format }}
                  </FormItem>
                </Col>
              </Row>
            </div>
          </Form>
          <div class="block-header">图片信息</div>
          <div class="common-form-item">
            <Row :gutter="40">
              <Col span="12">
                <FormItem label="产品主图">
                  <Picture
                    v-model="formData.main_image"
                    :limit="1"
                    :isQueryDetail="isQuery"
                    v-if="formData.main_image"
                  />
                  <!-- <div class="note">提示：图片大小不超过3.0M</div> -->
                </FormItem>
              </Col>
            </Row>
          </div>
        </Form>
      </TabPane>
      <TabPane label="操作记录" name="operationRecord">
        <operationlog-record :b_type="b_type" :b_id="b_id" :isRecord="isRecord"></operationlog-record>
      </TabPane>
    </Tabs>

    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
    </div>

    <EditProductModal :productId="productId" :visible.sync="editVisible" @refresh="init"></EditProductModal>
  </div>
</template>

<script>
import Picture from '@/components/upload/picture';
import OperationlogRecord from '../components/operationlog-record';
import EditProductModal from './compontents/EditProductModal';
import S from '@/utils/util';
export default {
  name: 'create-product',
  mixins: [],

  components: { Picture, OperationlogRecord, EditProductModal },

  props: {},

  data() {
    const initFormData = {
      name: '', //产品名称
      unit: '', //单位
      spec: '', //规格
      main_image: '', //主图
      supplier_code: '', //供应商编码
      purchase_price: '', //成本价
      sales_price: '', //销售价
      status: '' //状态
    };
    return {
      isQuery: true,
      formData: { ...initFormData },
      selectSupplierList: [],
      tab: 'detail',
      b_type: 10,
      b_id: 0,
      isRecord: 0,
      productId: '',
      editVisible: false,
      detailLoading: false
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {
    this.init();
  },

  methods: {
    selectSup(val) {
      console.log('-> %c val  === %o ', 'font-size: 15px', val);
      if (!S.isEmptyObject(val)) {
        this.selectSupplierList = [];
        this.selectSupplierList.push(Object.values(val)[0]);
        console.log('-> %c this.selectSupplierList  === %o ', 'font-size: 15px', this.selectSupplierList);
        this.formData.supplier_code = [Object.keys(val)[0]];
        this.$forceUpdate();
      }
    },
    handleCreateUnit(val) {
      if (!this.unitList.includes(val)) {
        this.unitList.unshift(val);
      }
    },
    chooseSupplier() {
      console.log(21312);
    },
    handleSubmit(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.$Message.success('Success!');
        } else {
          this.$Message.error('Fail!');
        }
      });
    },

    init() {
      if (this.$route.query.id || this.$route.query.code) {
        this.getErpProductDetail(this.$route.query.id, this.$route.query.code);
      }
    },
    back() {
      this.$router.back();
    },
    changeTab(name) {
      if (name == 'operationRecord') {
        this.isRecord++;
      }
    },
    getErpProductDetail(id, code) {
      this.detailLoading = true;
      let params = { id, code };
      this.$api.getErpProductDetail(params).then(res => {
        this.formData = res;
        this.$router.replace({
          query: {
            ...this.$route.query,
            id: res.id
          }
        });
        this.b_id = Number(this.$route.query.id);
        this.detailLoading = false;
      });
    },

    showEditModal() {
      this.productId = this.$route.query.id;
      this.editVisible = true;
    }
  },

  destroyed() {}
};
</script>

<style scoped lang="less">
.create-wrapper {
  .f-title {
    margin-bottom: 12px;
  }

  .common-form-item {
    margin-left: 60px;
    width: 60%;
  }
  :deep(.ivu-icon-ios-close) {
    font-size: 14px;
    margin-top: 1px;
  }
}

:deep(.el-select .el-input.is-focus .el-input__inner) {
  border-color: #447cdd;
  box-shadow: 0 0 0 2px rgba(20, 89, 209, 0.2);
}

::v-deep .block-header span {
  font-size: 12px;
  padding: 0;
  font-weight: normal;
}

.edit-button {
  position: absolute;
  top: 3px;
  right: 5px;
}
</style>
