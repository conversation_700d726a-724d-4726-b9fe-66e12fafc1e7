<template>
  <div v-if="visible">
    <Modal
      :value="visible"
      :title="productId ? '编辑产品' : '新增产品'"
      width="900px"
      @on-cancel="cancelHandle"
      :mask-closable="false"
    >
      <Form
        label-position="top"
        class="common-modal-form"
        :model="formData"
        :rules="formDataValidateRules"
        ref="productRef"
      >
        <div class="section-header">
          <div class="section-mark"></div>
          <div class="section-title">基本信息</div>
        </div>
        <div class="create-section">
          <FormItem label="产品名称" prop="name" class="common-form-item">
            <Input v-model="formData.name" placeholder="请输入产品名称"></Input>
          </FormItem>
          <!--          prop="code"-->
          <FormItem label="产品编码" class="common-form-item">
            <Input v-model="formData.code" placeholder="不填系统将自动生成" :disabled="!!productId"></Input>
          </FormItem>

          <FormItem label="产品条码" prop="barcode" class="common-form-item">
            <Input v-model="formData.barcode" placeholder="请输入产品条码"></Input>
          </FormItem>

          <FormItem label="产品单位" prop="unit" class="common-form-item">
            <Select
              v-model="formData.unit"
              ref="select-unit"
              filterable
              allow-create
              @on-create="handleCreateUnit"
              placeholder="请选择产品单位"
            >
              <Option v-for="item in unitList" :value="item" :key="item">{{ item }}</Option>
            </Select>
          </FormItem>

          <FormItem label="产品类型" prop="type" class="common-form-item">
            <Select
              v-model="formData.type"
              ref="select-unit"
              filterable
              allow-create
              @on-create="handleCreateUnit"
              placeholder="请选择产品类型"
              :disabled="!!productId"
            >
              <Option v-for="item in typeOptions" :value="item.id" :key="item.id">{{ item.desc }}</Option>
            </Select>
          </FormItem>
          <FormItem label="产品属性" prop="attr" class="common-form-item">
            <Select
              v-model="formData.attr"
              ref="select-unit"
              filterable
              allow-create
              @on-create="handleCreateUnit"
              placeholder="请选择产品属性"
            >
              <Option v-for="item in attrOptions" :value="item.id" :key="item.id">{{ item.desc }}</Option>
            </Select>
          </FormItem>
          <FormItem label="成本价" class="common-form-item">
            <InputNumber
              style="width: 100%"
              ref="purchase_price"
              :precision="2"
              :active-change="false"
              :min="0"
              v-model="formData.purchase_price"
              placeholder="请输入产品成本价"
              @on-blur="removeZero('purchase_price')"
            ></InputNumber>
          </FormItem>

          <FormItem label="市场价" class="common-form-item">
            <InputNumber
              ref="sales_price"
              style="width: 100%"
              :precision="2"
              :active-change="false"
              :min="0"
              :max="100000"
              v-model="formData.sales_price"
              placeholder="请输入产品市场价"
              @on-blur="removeZero('sales_price')"
            ></InputNumber>
          </FormItem>

          <FormItem label="状态" prop="status" class="common-form-item">
            <Select v-model="formData.status" ref="select-status" placeholder="请选择状态">
              <Option value="ENABLE">上架</Option>
              <Option value="DISABLE">下架</Option>
            </Select>
          </FormItem>

          <FormItem label="供应商" prop="supplier_code" class="common-form-item">
            <SelectPopper class="flex" ref="selectSupplier" @selectSup="selectSup" :code="formData.supplier_code">
              <el-select
                :multiple="false"
                v-model="formData.supplier_code"
                :multiple-limit="1"
                style="width: 100%"
                @visible-change="selectChange($event, 'selectSupplier')"
                size="medium"
                popper-class="rxj-pop-select"
              >
                <el-option
                  v-for="(item, index) in selectSupplierList"
                  :value="item.code"
                  :label="item.name"
                  :key="index + item.code"
                ></el-option>
              </el-select>
            </SelectPopper>
          </FormItem>

          <FormItem label="备注" class="common-form-item">
            <Input
              v-model="formData.remark"
              type="textarea"
              maxlength="50"
              show-word-limit
              :autosize="{ maxRows: 2, minRows: 2 }"
            ></Input>
          </FormItem>
        </div>

        <!-- 产品规格 -->
        <div>
          <div class="section-header mt8">
            <div class="section-mark"></div>
            <div class="section-title">产品规格</div>
          </div>

          <div class="create-section">
            <div class="common-form-item">
              <FormItem label="产品规格">
                <Input v-model="formData.spec" placeholder="请输入产品规格"></Input>
              </FormItem>
            </div>
          </div>
        </div>

        <!-- 图片信息 -->
        <div>
          <div class="section-header mt8">
            <div class="section-mark"></div>
            <div class="section-title">图片信息</div>
          </div>

          <div class="create-section">
            <div class="common-form-item">
              <FormItem label="产品主图">
                <Picture v-model="formData.main_image" :limit="1" :isQueryDetail="false" />
                <div class="note">提示：图片大小不超过3.0M</div>
              </FormItem>
            </div>
          </div>
        </div>
      </Form>
      <div slot="footer">
        <Button @click="cancelHandle">取消</Button>
        <Button type="primary" @click="confirmHandle">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import S from 'utils/util';
import Picture from '@/components/upload/picture';
import SelectPopper from '@/components/select-popper/select-popper';
const initFormData = {
  name: '', //产品名称
  code: '', // 产品编码
  barcode: '', // 条码
  unit: '', //单位
  purchase_price: null, // 成本价
  sales_price: null, // 市场价
  status: '', //状态
  supplier_code: '', //供应商编码
  remark: '', // 备注
  spec: '', // 产品规格
  main_image: '', //主图
  type: '', //类型
  attr: '' //属性
};
export default {
  name: 'EditProductModal',
  mixins: [],
  components: { Picture, SelectPopper },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    productId: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      formData: { ...initFormData },
      unitList: ['个', '片', '粒', '袋', '盒', '瓶', '只', '箱', 'ml'],
      formDataValidateRules: {
        name: [{ required: true, message: '请输入产品名称', trigger: 'change' }],
        code: [{ required: true, message: '请输入产品编码', trigger: 'change' }],
        barcode: [{ required: true, message: '请输入产品条码', trigger: 'change' }],
        unit: [{ required: true, message: '请选择/输入单位', trigger: 'change' }],
        type: [{ required: true, message: '请选择产品类型', trigger: 'change' }],
        attr: [{ required: true, message: '请选择产品属性', trigger: 'change' }],
        status: [{ required: true, message: '请选择产品状态', trigger: 'change' }],
        supplier_code: [{ required: true, message: '请选择供应商', trigger: 'blur, change' }]
      },
      submitLoading: false, //提交按钮加载状态
      selectSupplierList: [], // 选中的供应商
      attrOptions: [],
      typeOptions: []
    };
  },

  computed: {},

  watch: {
    visible: {
      immediate: true,
      handler(val) {
        if (val && this.productId) {
          this.getErpProductDetail();
        }
      }
    }
  },

  created() {
    this.getProductOptions();
  },

  mounted() {},

  methods: {
    selectChange(e, node) {
      // poppver的ref
      let nodeList = ['selectSupplier'];
      // select date 的ref
      let selectNodeList = ['select-unit', 'select-status'];
      if (e) {
        let hideList = nodeList.filter(item => item !== node);
        hideList.forEach(item => {
          this.$refs[item] && (this.$refs[item].showPop = false);
        });
        selectNodeList.forEach(item => {
          this.$refs[item] && (this.$refs[item].visible = false);
        });
      }
    },
    removeZero(key) {
      let currentValue = this.$refs[key].currentValue;
      if (Number(currentValue) == 0) {
        this.formData[key] = null;
        this.$refs[key].currentValue = null;
      }
    },
    // 点击确定
    confirmHandle() {
      this.$refs['productRef'].validate(valid => {
        if (valid && this.formData.supplier_code !== '') {
          this.productId ? this.updateErpProduct() : this.createErpProduct();
        } else {
          this.$Message.error('请填写完整');
        }
      });
    },
    // 弹窗关闭
    cancelHandle() {
      this.formData = { ...initFormData };
      this.$emit('update:visible', false);
      this.$refs.productRef.resetFields();
    },
    // 选中供应商
    selectSup(val) {
      this.selectSupplierList = [];
      this.formData.supplier_code = '';
      if (!S.isEmptyObject(val)) {
        this.selectSupplierList.push(val);
        this.formData.supplier_code = val.code;
      }
      this.$forceUpdate();
      this.$refs.productRef.validateField('supplier_code');
    },
    handleCreateUnit(val) {
      if (!this.unitList.includes(val)) {
        this.unitList.unshift(val);
      }
    },

    // api - 创建产品
    createErpProduct() {
      this.submitLoading = true;
      let params = {
        ...this.formData,
        supplier_code: this.formData.supplier_code
      };
      this.$api
        .createErpProduct(params)
        .then(res => {
          this.$Message.success('创建成功');
          this.cancelHandle();
          this.$emit('refresh');
          this.submitLoading = false;
        })
        .catch(err => {
          this.submitLoading = false;
        });
    },

    // api - 编辑产品
    updateErpProduct() {
      this.submitLoading = true;
      let params = {
        ...this.formData,
        supplier_code: this.formData.supplier_code,
        id: this.productId
      };
      this.$api
        .updateErpProduct(params)
        .then(res => {
          this.$Message.success('编辑成功');
          this.cancelHandle();
          this.$emit('refresh');
          this.submitLoading = false;
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
          this.submitLoading = false;
        });
    },

    // api - 产品详情
    getErpProductDetail() {
      let params = {
        id: this.productId
      };
      this.$api
        .getErpProductDetail(params)
        .then(res => {
          this.formData.name = res.name;
          this.formData.code = res.code;
          this.formData.barcode = res.barcode;

          // 单位可能是自建,如果是自建，handleCreateUnit做首位追加
          this.handleCreateUnit(res.unit);
          this.formData.unit = res.unit;

          this.formData.purchase_price = Number(res.purchase_price);
          this.formData.sales_price = Number(res.sales_price);
          this.formData.status = res.status;
          this.formData.type = res.type;
          this.formData.attr = res.attr;

          // 回显供应商数据
          this.selectSup({
            name: res.supplier_name,
            code: res.supplier_code
          });
          this.formData.supplier_code = res.supplier_code;

          this.formData.remark = res.remark;
          this.formData.spec = res.spec;
          this.formData.main_image = res.main_image;
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
        });
    },
    getProductOptions() {
      this.$api.getErpProductOptions().then(res => {
        console.log('-> %c res  === %o', 'font-size: 15px;color: green;', res);
        this.typeOptions = S.descToArrHandle(res.prodTypeDesc);
        console.log('=>(EditProductModal.vue:339) this.typeOptions', this.typeOptions);
        this.attrOptions = S.descToArrHandle(res.prodAttrDesc);
      });
    }
  },

  destroyed() {}
};
</script>

<style scoped lang="less">
@import url('../../common/modal.less');
</style>
