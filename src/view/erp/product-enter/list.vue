<template>
  <div class="purchase-list-wrapper">
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <Col>
          <FormItem>
            <GoodsSearch v-model="queryFormData.product_code" :onSearch="onSearch"></GoodsSearch>
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Input v-model="queryFormData.code" placeholder="请输入入库单号" clearable />
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Select v-model="queryFormData.type" placeholder="请选择入库类型" clearable>
              <Option v-for="item in typeList" :value="item.id" :key="item.id">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </Col>

        <Col>
          <FormItem>
            <Select v-model="queryFormData.audit_status" placeholder="请选择审核状态" clearable>
              <Option v-for="item in statusList" :value="item.id" :key="item.id">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </Col>

        <Col>
          <FormItem style="text-align: left">
            <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
            <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
            <Button type="primary" @click="createProductEnter">创建入库单</Button>
          </FormItem>
        </Col>
      </Row>
    </Form>

    <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 240">
      <!-- 关联单号 -->
      <template slot-scope="{ row }" slot="relate_code">
        <KLink
          v-if="row.type == 20"
          :to="{ path: '/erp/purchase/detail', query: { code: row.relate_code } }"
          target="_blank"
          >{{ row.relate_code }}</KLink
        >
        <KLink
          v-else-if="row.type == 30"
          :to="{ path: '/erp/sale_return/detail', query: { code: row.relate_code } }"
          target="_blank"
          >{{ row.relate_code }}</KLink
        >
        <span v-else-if="row.relate_code.includes(',')">
          <div v-for="(item, index) in row.relate_code.split(',')" :key="index">{{ item || '-' }}</div>
        </span>
        <span v-else>{{ row.relate_code || '-' }}</span>
      </template>
      <template slot-scope="{ row }" slot="actual_time">
        {{ row.actual_time | date_format }}
      </template>
      <template slot-scope="{ row }" slot="remark">
        {{ row.remark || '-' }}
      </template>

      <!-- 审核状态 -->
      <template slot-scope="{ row }" slot="audit_status_text">
        <status-text :status="row.audit_status"
          ><span>{{ row.audit_status_text }}</span></status-text
        >
      </template>

      <template slot-scope="{row}" slot="push_status_text">
        <span v-if="row.push_status === '4'">{{ row.push_status_text }}
          <Tooltip placement="bottom" max-width="200" :content="row.hangup_text">
            <Icon type="md-help-circle" style="font-size: 16px;" class="cursor"/>
          </Tooltip>
        </span>
        <span v-else>{{ row.push_status_text }}</span>
      </template>

      <template slot-scope="{ row }" slot="create_time">
        {{ row.create_time | date_format }}
      </template>
      <template slot-scope="{ row }" slot="update_time">
        {{ row.update_time | date_format }}
      </template>

      <template slot-scope="{ row }" slot="action">
        <a @click="edit(row)" v-if="row.audit_status == '70'">编辑</a>
        <Dvd v-if="row.audit_status == '70'"></Dvd>
        <a @click="goDetail(row)">详情</a>
      </template>
    </Table>

    <div class="block_20"></div>
    <KPage
      :total="total"
      :page-size="+queryFormData.pageSize"
      :current="+queryFormData.page"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
      style="text-align: center"
    />
    <EditProductEnter :id="productId" :visible.sync="editVisible" @refresh="refresh"></EditProductEnter>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'utils/util';
import EditProductEnter from '@/view/erp/product-enter/compontents/EditProductEnter';
import GoodsSearch from "@/components/GoodsSearch/index.vue";
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  code: '', // 入库单号
  type: '', // 类型
  audit_status: '', // 审核状态
  r: '',
  product_code: '', // 商品编码
};

export default {
  name: 'list',
  mixins: [search],
  components: { EditProductEnter,GoodsSearch },
  data() {
    return {
      apiName: 'getErpInboundList',
      queryFormData: {
        ...init_query_form_data,
      },

      tableCols: [
        { title: '入库类型', key: 'type_text', align: 'center' },
        { title: '入库单号', key: 'code', align: 'center' },
        { title: '关联单号', slot: 'relate_code', align: 'center', width: 130 },
        { title: '入库仓库', key: 'warehouse_name', align: 'center' },
        { title: '实际入库时间', slot: 'actual_time', align: 'center' },
        { title: '备注', slot: 'remark', align: 'center' },
        { title: '审核状态', slot: 'audit_status_text' },
        { title: '库存状态', key: 'final_in_status_text' },
        { title: '推送状态', slot: 'push_status_text' },
        { title: '创建人', key: 'operator', align: 'center' },
        { title: '创建时间', slot: 'create_time', align: 'center' },
        { title: '更新时间', slot: 'update_time', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' },
      ],
      list_count: {},

      productId: '', // 入库单id
      editVisible: false, // 新建/编辑采购订单弹窗
      typeList: [
        // {code:'10', name:'初始化入库'},
        // {code:'20', name:'采购入库'},
        // {code:'30', name:'销售退货入库'},
        // {code:'40', name:'其他入库'},
      ],
      statusList: [], // 审核状态类型
    };
  },
  computed: {},
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.getErpInboundOptions();
  },
  mounted() {},
  methods: {
    // 新建采购订单
    createProductEnter() {
      this.productId = '';
      this.editVisible = true;
    },

    refresh() {
      this.loadList();
    },

    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    /**
     * 编辑
     * */
    edit(row) {
      this.productId = row.id;
      this.editVisible = true;
    },

    goDetail(row) {
      this.$router.push({
        path: '/erp/product-enter/detail',
        query: {
          id: row.id,
        },
      });
    },

    // api - 获取枚举值
    getErpInboundOptions() {
      this.$api.getErpInboundOptions().then(res => {
        // 获取入库类型
        this.typeList = S.descToArrHandle(res.prodTypeDesc);
        // 获取审核状态类型
        this.statusList = S.descToArrHandle(res.prodAuditStatusDesc);
      });
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  },
};
</script>

<style scoped lang="less">
.supplier-list-wrapper {
}
</style>
