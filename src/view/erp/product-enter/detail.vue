<template>
  <div class="container">
    <div class="demo-spin-container" v-if="detailLoading">
      <Spin fix></Spin>
    </div>

    <Tabs v-else :value="tab" @on-click="changeTab" :animated="false">
      <!-- 详细资料 -->
      <TabPane label="详细资料" name="detail">
        <div class="block-header">基础信息</div>
        <div class="flex flex-between">
          <Form :model="formData" label-position="right" :label-width="100" class="basicInfo">
            <Row :gutter="40">
              <Col span="12">
                <FormItem label="入库单编号:">
                  {{ formData.code }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="预计入库时间:">
                  {{ formData.inbound_date }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="实际入库时间:">
                  {{ formData.actual_time | date_format('YYYY-MM-DD HH:mm:ss') }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="入库类型:">
                  <span>{{ formData.type_text }}</span>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label=" 入库仓库:">
                  <span v-if="formData.warehouse_info">{{ formData.warehouse_info.warehouse_name }}</span>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="关联单号:">
                  {{ formData.relate_code || '-' }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="备注:">
                  {{ formData.remark || '-' }}
                </FormItem>
              </Col>
            </Row>
          </Form>
        </div>
        <div class="block-header">系统信息</div>
        <Form :model="formData" label-position="right" :label-width="100" class="basicInfo">
          <Row :gutter="40">
            <Col span="12">
              <FormItem label="创建人:">
                {{ formData.operator }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="创建时间:">
                {{ formData.create_time | date_format }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="更新时间:">
                {{ formData.update_time | date_format }}
              </FormItem>
            </Col>
          </Row>
        </Form>
        <div class="block-header">产品</div>
        <Table :columns="tableCols" :data="list">
          <template slot-scope="{ row, index }" slot="code">
            <KLink v-if="row.code" :to="{ path: '/erp/product/detail', query: { id: row.id } }" target="_blank">{{
              row.code
            }}</KLink>
            <span v-else>{{ row.code }}</span>
          </template>

          <template slot-scope="{ row, index }" slot="spec">
            {{ row.spec || '-' }}
          </template>
          <template slot-scope="{ row, index }" slot="unit">
            {{ row.unit || '-' }}
          </template>

          <!-- 含税金额 -->
          <template slot-scope="{ row, index }" slot="amount">
            {{ row.amount ? `￥${row.amount}` : '-' }}
          </template>

          <!-- 税率 -->
          <template slot-scope="{ row, index }" slot="tax_rate">
            {{ row.tax_rate || '-' }}
          </template>

          <!-- 不含税金额 -->
          <template slot-scope="{ row, index }" slot="amount_without_tax">
            {{ row.amount_without_tax ? `￥${row.amount_without_tax}` : '-' }}
          </template>

          <!-- 单价 -->
          <template slot-scope="{ row, index }" slot="price">
            {{ row.price ? `￥${row.price}` : '-' }}
          </template>
          <template slot-scope="{ row, index }" slot="note">
            {{ row.note || '-' }}
          </template>
        </Table>
        <!-- 总计 -->
        <div class="mt10 flex flex-item-end total-show" v-if="list.length">总计: {{ product_totalMoeny }}元</div>
        <div class="block_20"></div>
        <div class="block_20"></div>
      </TabPane>

      <!-- 操作记录 -->
      <TabPane label="操作记录" name="operationRecord">
        <operationlog-record :b_type="b_type" :b_id="b_id" :isRecord="isRecord"></operationlog-record>
      </TabPane>
    </Tabs>

    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Button
        v-if="formData.audit_status == '10'"
        v-eleControl="'EB147oG9Yq'"
        style="margin: 0 20px"
        type="error"
        @click="showRefuseModal"
        >审核驳回
      </Button>
      <Button v-if="formData.audit_status == '10'" type="primary" @click="passCheck">审核通过 </Button>
    </div>

    <Modal v-model="refuseModalVisible" :mask-closable="false" title="审核驳回">
      <div style="width: 100%">
        <p class="label">请输入驳回原因：</p>
        <Input v-model="reason" :autosize="{ minRows: 3, maxRows: 6 }" style="max-width: 1000px" type="textarea" />
      </div>

      <div slot="footer">
        <Button @click="refuseCancel">取消</Button>
        <Button type="primary" @click="submitRefuseReason">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import OperationlogRecord from '../components/operationlog-record';
const init_query_form_data = {
  // page: 1,
  // pageSize: 20,
  id: '', // 采购订单id
  code: '', // 采购订单编号
};

export default {
  data() {
    return {
      tab: 'detail',
      formData: {
        name: '1',
      },
      editVisible: false,
      tableCols: [
        { title: '产品编码', slot: 'code', align: 'center' },
        { title: '产品条码', key: 'barcode', align: 'center' },
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '入库数量', key: 'quantity', align: 'center' },
        { title: '单价', slot: 'price', align: 'center' },
        { title: '含税总金额', slot: 'amount', align: 'center' },
        { title: '税率', slot: 'tax_rate', align: 'center' },
        { title: '不含税总金额', slot: 'amount_without_tax', align: 'center' },
      ],
      list: [],
      total: 0,
      queryFormData: { ...init_query_form_data },
      b_type: 15,
      b_id: 0,
      isRecord: 0,
      detailLoading: false,
      product_totalMoeny: 0, // 产品总计

      refuseModalVisible: false, // 审核弹窗
      reason: '', // 驳回原因
    };
  },
  watch: {
    refuseModalVisible(val) {
      if (!val) {
        this.reason = '';
      }
    },
  },
  created() {},
  mounted() {
    this.init();
  },
  components: {
    OperationlogRecord,
  },
  methods: {
    init() {
      if (this.$route.query.id || this.$route.query.code) {
        this.getErpInboundInfo(this.$route.query.id, this.$route.query.code);
      }
    },
    // tabs事件
    changeTab(name) {
      if (name === 'operationRecord') {
        this.isRecord++;
      }
    },
    getErpInboundInfo(id, code) {
      this.detailLoading = true;
      let params = { id, code };
      this.$api.getErpInboundInfo(params).then(res => {
        this.formData = res;
        this.$router.replace({
          query: {
            ...this.$route.query,
            id: res.id,
          },
        });
        this.queryFormData.id = this.$route.query.id;
        this.b_id = Number(this.$route.query.id);
        this.detailLoading = false;
        this.getErpInboundDetail();
      });
    },
    getErpInboundDetail() {
      let params = { ...this.queryFormData };
      this.$api.getErpInboundDetail(params).then(res => {
        this.handlerDetail(res.detail, res.products);
        // 产品总计
        this.product_totalMoeny = res.total_amount;
      });
    },
    // 处理明细数据，回显采购订单
    handlerDetail(list, products) {
      console.log('-> %c list, products  === %o', 'font-size: 15px;color: green;', list, products);
      let productDetailList = [];
      list &&
        list.forEach(item => {
          productDetailList.push({
            ...products[item.product_id],
            quantity: item.quantity,
            price: item.price,
            total_price: item.total_price,
            amount: item.amount,
            tax_rate: item.tax_rate,
            amount_without_tax: item.amount_without_tax,
          });
        });
      console.log('-> %c productDetailList  === %o', 'font-size: 15px;color: green;', productDetailList);
      this.list = productDetailList;
    },

    // 编辑
    showEditModal() {
      this.supplierId = this.$route.query.id;
      this.editVisible = true;
    },

    back() {
      this.$router.back();
    },

    /**
     * @description: 审核相关
     * */
    // 审核按钮
    passCheck() {
      this.$Modal.confirm({
        title: '通过审核',
        content: '您确定要通过该审核吗？',
        onOk: () => {
          this.review('PASS');
        },
      });
    },

    // 审核驳回
    showRefuseModal() {
      this.refuseModalVisible = true;
    },

    refuseCancel() {
      this.refuseModalVisible = false;
    },

    /**
     * @description 审核/驳回的接口
     * @param { action } 审核的状态
     * @param { reason } 驳回的原因
     * */
    review(action, reason) {
      const params = {
        id: this.$route.query.id,
        action,
        reason: '',
      };
      let isPass = true;
      if (reason) {
        params.reason = reason;
        isPass = false;
      }
      this.$api.erpInboundStatus(params).then(
        res => {
          this.$Message.success(`${isPass ? '通过审核成功' : '驳回审核成功'}`);
          this.init();
        },
      );
    },

    submitRefuseReason() {
      if (this.reason == '') {
        this.$Message.error('请输入驳回原因');
        return;
      }
      this.refuseCancel();
      this.review('REJECT', this.reason);
    },
  },
};
</script>

<style lang="less" scoped>
.container {
  //position: relative;
  .total {
    text-align: right;
    .total-num {
      margin-right: 15px;
      color: #fd715a;
    }
  }
}

.basicInfo {
  width: 60%;
  margin-left: 60px;
  .basic-item {
    width: 50%;
  }
  .remark {
    width: 100%;
  }
}

.buttonGroup {
  text-align: center;
}

.ml-10 {
  margin-left: 10px;
}

.mr-10 {
  margin-right: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mt30 {
  margin-top: 30px;
}
// 总计展示
.total-show {
  font-size: 12px;
  font-weight: 400;
  color: #000000;
  line-height: 17px;
  .amount {
    margin: 0 4px;
    font-size: 13px;
    font-weight: 600;
  }
}
</style>
