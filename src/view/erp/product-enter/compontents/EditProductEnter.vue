<template>
  <div>
    <Modal
      :value="visible"
      :title="id ? '编辑入库单' : '新建入库单'"
      width="900px"
      @on-cancel="cancelHandle"
      :mask-closable="false"
    >
      <Form
        label-position="top"
        class="common-modal-form"
        :model="formData"
        :rules="formDataValidateRules"
        ref="purchaseRef"
      >
        <div class="section-header">
          <div class="section-mark"></div>
          <div class="section-title">基本信息</div>
        </div>

        <!-- <div class=""> -->
        <Row :gutter="20">
          <Col :span="formData.type === '60' ? 12 : 24">
            <Row :gutter="20">
              <Col :span="getFormateSpan">
                <FormItem label="入库类型 " prop="type">
                  <Select
                    v-model="formData.type"
                    ref="enter-type"
                    placeholder="请选择入库类型"
                    :disabled="isDetailCreate || isDisabled"
                  >
                    <Option v-for="item in typeList" :value="item.type" :key="item.type">{{ item.name }}</Option>
                  </Select>
                </FormItem>
              </Col>

              <Col :span="getFormateSpan" v-if="formData.type == '20'">
                <FormItem label="采购单号" prop="purchase_code">
                  <SelectPurchasePopper
                    class="flex"
                    ref="selectPurchase"
                    @selectSup="selectPurchase"
                    :code="formData.purchase_code"
                    :isDetail="isDetailCreate"
                  >
                    <el-select
                      :multiple="false"
                      v-model="formData.purchase_code"
                      :multiple-limit="1"
                      style="width: 100%"
                      @visible-change="selectChange($event, 'selectPurchase')"
                      size="small"
                      popper-class="rxj-pop-select"
                      :disabled="isDetailCreate || isDisabled"
                    >
                      <el-option
                        v-for="(item, index) in selectPurchaseList"
                        :value="item.code"
                        :label="item.name"
                        :key="index + item.code"
                      ></el-option>
                    </el-select>
                  </SelectPurchasePopper>
                </FormItem>
              </Col>

              <Col :span="getFormateSpan" v-if="formData.type == '30'">
                <FormItem label="销售退货单" prop="refund_code">
                  <SelectReturnPopper
                    class="flex"
                    ref="selectPurchaseReturn"
                    @selectSup="selectPurchaseRefund"
                    :code="formData.refund_code"
                    :isDetail="isDetailCreate"
                  >
                    <el-select
                      :multiple="false"
                      v-model="formData.refund_code"
                      :multiple-limit="1"
                      style="width: 100%"
                      @visible-change="selectChange($event, 'selectPurchaseReturn')"
                      size="small"
                      popper-class="rxj-pop-select"
                      :disabled="isDetailCreate"
                    >
                      <el-option
                        v-for="(item, index) in selectPurchaseRefundList"
                        :value="item.code"
                        :label="item.name"
                        :key="index + item.code"
                      ></el-option>
                    </el-select>
                  </SelectReturnPopper>
                </FormItem>
              </Col>

              <Col :span="getFormateSpan">
                <FormItem label="入库仓库" prop="warehouse_code">
                  <select-warehouse-popper
                    class="flex"
                    ref="selectWarehouse"
                    @selectSup="selectWarehouse"
                    status="ENABLE"
                    :code="formData.warehouse_code"
                    :isDetail="formData.type == '20' || formData.type == '30' || isDisabled"
                    :inputType="formData.type"
                  >
                    <el-select
                      :multiple="false"
                      v-model="formData.warehouse_code"
                      :multiple-limit="1"
                      style="width: 100%"
                      @visible-change="selectChange($event, 'selectWarehouse')"
                      size="small"
                      popper-class="rxj-pop-select"
                      :disabled="formData.type == '20' || formData.type == '30' || isDisabled"
                    >
                      <el-option
                        v-for="(item, index) in selectWarehouseList"
                        :value="item.code"
                        :label="item.name"
                        :key="index + item.code"
                      ></el-option>
                    </el-select>
                  </select-warehouse-popper>
                </FormItem>
              </Col>

              <Col :span="getFormateSpan">
                <FormItem label="入库单号 " prop="code">
                  <Input
                    v-model="formData.code"
                    placeholder="不填系统将自动生成"
                    maxlength="20"
                    :disabled="true"
                  ></Input>
                </FormItem>
              </Col>

              <Col :span="getFormateSpan">
                <FormItem label="预计入库时间" prop="inbound_date">
                  <DatePicker
                    :disabled="isDisabled"
                    type="date"
                    ref="enter-time"
                    :value="formData.inbound_date"
                    style="width: 100%"
                    placeholder="请选择入库时间"
                    @on-change="date => (formData.inbound_date = date)"
                  ></DatePicker>
                </FormItem>
              </Col>

              <Col :span="getFormateSpan">
                <FormItem label="备注">
                  <Input
                    v-model="formData.remark"
                    :disabled="isDisabled"
                    type="textarea"
                    maxlength="50"
                    show-word-limit
                    :autosize="{ maxRows: 2, minRows: 2 }"
                  ></Input>
                </FormItem>
              </Col>
            </Row>
          </Col>

          <Col :span="12" v-if="formData.type === '60'">
            <Col :span="24" v-for="(item, index) in formData.storeHouseList" :key="index">
              <FormItem
                style="position: relative"
                label="原材料单据号"
                :prop="`storeHouseList[${index}].code`"
                :rules="[
                  {
                    required: true,
                    validator: (rule, value, callback) => validateHouseList(rule, value, callback, index),
                    trigger: 'blur,change',
                  },
                ]"
              >
                <div
                  v-if="!(isDetailCreate || isDisabled)"
                  style="
                    position: absolute;
                    right: 0;
                    top: -30px;
                    z-index: 100;
                    display: flex;
                    width: 70%;
                    justify-content: space-between;
                    align-items: center;
                  "
                >
                  <a style="color: red" type="error" size="small" @click="deleteListFunc(index)">删除</a>
                  <a size="small" v-if="index === 0" @click="addList">继续添加关联单据</a>
                </div>
                <!-- <div -->
                <!--   v-if="!(isDetailCreate || isDisabled)" -->
                <!--   style="position: absolute; right: 0; top: 30px; z-index: 100" -->
                <!-- > -->
                <!--   <a size="small" v-if="index === 0">继续添加关联单据</a> -->
                <!-- </div> -->
                <SelectProductPopperList
                  class="flex flex-item-align"
                  ref="selectProductPopperReturn"
                  @selectBatchList="list => selectedListArr(list, index)"
                  title="选择产品"
                  type="50"
                  :batchCodeList="[formData.storeHouseList[index]]"
                  :isDetail="isDetailCreate || isDisabled"
                >
                  <!-- <a style="font-weight: normal; font-size: 12px" class="space6" @click="addCount">添加盘点产品</a> -->
                  <!-- <Input v-model="storeHouseList[index]" /> -->
                  <el-select
                    :multiple="false"
                    v-model="formData.storeHouseList[index].code"
                    :multiple-limit="1"
                    style="width: 100%"
                    @visible-change="selectChange($event, 'selectProductPopperReturn', index)"
                    size="small"
                    popper-class="rxj-pop-select"
                    :disabled="formData.type == '20' || formData.type == '30' || isDisabled"
                  >
                    <el-option
                      v-for="(item, index) in formData.storeHouseList[index]"
                      :value="item.code"
                      :label="item.name"
                      :key="index + item.code"
                    ></el-option>
                  </el-select>
                </SelectProductPopperList>
                <div style="line-height: 20px">
                  <p v-for="(item_list, index) in formData.storeHouseList[index].product_desc_list" :key="index">
                    {{ item_list }}
                  </p>
                </div>
              </FormItem>
            </Col>
          </Col>
        </Row>
      </Form>

      <div class="section-header mt10">
        <div class="section-mark"></div>
        <div class="section-title">产品信息</div>
      </div>
      <div class="flex flex-item-end" v-if="formData.type == '10' || formData.type == '40' || formData.type === '60'">
        <SelectProductPopper class="flex" ref="selectProduct" @selectedList="selectedList" :product_list="product_list">
          <Button type="primary" @click="addProductE">添加商品</Button>
        </SelectProductPopper>
      </div>

      <Table class="mt10" :loading="tableLoading" :columns="product_tableCols" :data="product_list">
        <!-- 产品规格 -->
        <template slot-scope="{ row, index }" slot="spec">
          {{ row.spec || '-' }}
        </template>

        <!-- 产品单位 -->
        <template slot-scope="{ row, index }" slot="unit">
          {{ row.unit || '-' }}
        </template>

        <template slot-scope="{ row, index }" slot="surplus_quantity">
          {{ row.surplus_quantity || '-' }}
        </template>

        <!-- 入库数量 -->
        <template slot-scope="{ row, index }" slot="quantity">
          <InputNumber
            :ref="'quantity' + index"
            style="width: 100%"
            :min="0"
            :precision="0"
            :active-change="false"
            v-model="product_list[index].quantity"
            placeholder="请输入"
            :disabled="is_api"
            @on-blur="removeZero('quantity', index)"
            @on-change="quantityChange(index)"
          ></InputNumber>
        </template>

        <!-- 含税金额 -->
        <template slot-scope="{ row, index }" slot="amount">
          <InputNumber
            :ref="'amount' + index"
            style="width: 100%"
            :min="0"
            :precision="2"
            :active-change="false"
            v-model="product_list[index].amount"
            placeholder="请输入"
            @on-blur="purchaseBlur(index)"
          ></InputNumber>
        </template>

        <!-- 税率 -->
        <template slot-scope="{ row, index }" slot="tax_rate">
          <Select v-model="product_list[index].tax_rate" style="width: 100px" @on-change="rateChange($event, index)">
            <Option v-for="item in rate_enum" :value="item.kw" :key="item.kw">{{ item.desc }}</Option>
            <Option v-if="taxInList(index) !== null" :value="taxInList(index) === null ? 0 : taxInList(index)"
              >{{ `${(Number(taxInList(index)) * 100).toFixed(4)}%` }}
            </Option>
          </Select>
        </template>

        <!-- 不含税金额 -->
        <template slot-scope="{ row, index }" slot="amount_without_tax">
          <div style="position: relative; color: #155bd4" v-if="!row.isEdit">
            <InputNumber
              style="width: 100%"
              :min="0"
              :precision="2"
              :active-change="false"
              v-model="product_list[index].amount_without_tax"
              placeholder="请输入"
              :disabled="true"
            ></InputNumber>
            <!-- <Icon type="md-add-circle"  /> -->

            <svg-icon
              iconClass="edit"
              style="position: absolute; right: 5px; font-size: 16px; top: 8px"
              @click="editTaxInputNumber(index, true)"
            ></svg-icon>
          </div>
          <div v-else>
            <!-- {{ String(row.isEdit) }} -->
            <InputNumber
              :ref="'amount_without_tax' + index"
              style="width: 100%"
              :min="0"
              :precision="2"
              :active-change="false"
              v-model="product_list[index].amount_without_tax"
              placeholder="请输入"
              @on-blur="taxBlur(index)"
            ></InputNumber>
          </div>
        </template>

        <!-- 单价 -->
        <template slot-scope="{ row, index }" slot="price">
          <InputNumber
            :disabled="formData.type !== '40' && formData.type !== '20'"
            :ref="'price' + index"
            style="width: 100%"
            :precision="4"
            :active-change="false"
            :min="0"
            v-model="product_list[index].price"
            placeholder="请输入单价"
            @on-blur="removeZero('price', index)"
            @on-change="changePrice(index)"
          ></InputNumber>
        </template>

        <!-- 备注 -->
        <!-- <template slot-scope="{row, index}" slot="note">
          <Input style="width: 100%" v-model="product_list[index].note" placeholder="请输入备注"></Input>
        </template> -->

        <template slot-scope="{ row, index }" slot="action">
          <a @click="deleteProduct(index)">删除</a>
        </template>
      </Table>

      <!-- 总计 -->
      <div class="mt30 flex flex-item-end total-show" v-if="product_list.length">
        总计:<span class="amount">{{ totalMoeny | number_format }}</span> 元
      </div>

      <div slot="footer">
        <Button @click="cancelHandle" class="mr-12">取消</Button>
        <Poptip confirm title="是否确定创建入库单" @on-ok="confirmHandle" width="200">
          <Button type="primary" :loading="submitLoading">确定</Button>
        </Poptip>
      </div>
    </Modal>
  </div>
</template>

<script>
import S from 'utils/util';
import { $operator } from '@/utils/operation';
// import { cloneDeep, flatMap } from 'lodash';
import SelectPopper from '@/components/select-popper/select-popper';
import SelectWarehousePopper from '@/components/select-warehouse-popper/select-warehouse-popper';
import SelectPurchasePopper from '@/components/select-purchase-popper/select-purchase-popper';
import SelectReturnPopper from '@/components/select-order-return-popper/select-order-return-popper';
import SelectProductPopper from '@/components/select-product-popper/select-product-popper';
import moment from 'moment';
import SelectProductPopperList from './productSelect.vue';

const initFormData = {
  type: '', // 入库类型
  code: '', // 入库单号
  purchase_code: '', // 采购单号
  refund_code: '', // 退货单号
  inbound_date: '', // 入库日期
  warehouse_code: '', // 仓库编号
  relate_code: '', // 关联单号
  remark: '', //备注
  storeHouseList: [{}],
};

export default {
  name: 'list',
  mixins: [],
  components: {
    SelectPopper,
    SelectPurchasePopper,
    SelectReturnPopper,
    SelectWarehousePopper,
    SelectProductPopper,
    SelectProductPopperList,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: '',
    },
    code: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    },
    isDetailCreate: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      formData: { ...initFormData },
      formDataValidateRules: {
        type: [{ required: true, message: '请选择入库类型', trigger: 'blur,change' }],
        inbound_date: [{ required: true, message: '请选择入库时间', trigger: 'blur,change' }],
        warehouse_code: [{ required: true, message: '请选择仓库', trigger: 'blur,change' }],
        // storeHouseList: [{ required: true, validator: validateHouseList, trigger: 'blur,change' }],
      },
      typeList: [
        { type: '10', name: '初始化入库' },
        { type: '20', name: '采购入库' },
        { type: '30', name: '销售退货入库' },
        { type: '60', name: '产成品入库' },
        { type: '40', name: '其他入库' },
      ],
      submitLoading: false, // 弹窗确定的loading
      selectWarehouseList: [], // 选中的仓库
      is_api: false, // 是否是接口推送过来的
      showWarehouseList: [], // 禁用时回显使用
      selectPurchaseList: [], // 选中的采购订单
      selectPurchaseRefundList: [], // 选中的采购退货单

      tableLoading: false,

      // 税率枚举
      rate_enum: [],
      product_tableCols: [],

      product_10_tableCols: [
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '入库数量', slot: 'quantity', align: 'center' },
        { title: '含税金额', slot: 'amount', align: 'center' },
        { title: '税率', slot: 'tax_rate', align: 'center', width: '110' },
        { title: '不含税金额', slot: 'amount_without_tax', align: 'center' },
        { title: '单价', slot: 'price', align: 'center' },
        // { title: '备注', slot: 'note', align: 'center'},
        { title: '操作', slot: 'action', align: 'center' },
      ],

      product_20_tableCols: [
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '待入库数量', slot: 'surplus_quantity', align: 'center' },
        { title: '入库数量', slot: 'quantity', align: 'center' },
        { title: '含税金额', slot: 'amount', align: 'center' },
        { title: '税率', slot: 'tax_rate', align: 'center', width: '110' },
        { title: '不含税金额', slot: 'amount_without_tax', align: 'center' },
        { title: '单价', slot: 'price', align: 'center' },
        // { title: '备注', slot: 'note', align: 'center'},
        { title: '操作', slot: 'action', align: 'center' },
      ],

      product_30_tableCols: [
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '待入库数量', slot: 'surplus_quantity', align: 'center' },
        { title: '入库数量', slot: 'quantity', align: 'center' },
        { title: '单价', slot: 'price', align: 'center' },
        // { title: '备注', slot: 'note', align: 'center'},
        { title: '操作', slot: 'action', align: 'center' },
      ],

      product_40_tableCols: [
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '入库数量', slot: 'quantity', align: 'center' },
        { title: '含税金额', slot: 'amount', align: 'center' },
        { title: '税率', slot: 'tax_rate', align: 'center', width: '110' },
        { title: '不含税金额', slot: 'amount_without_tax', align: 'center' },
        { title: '单价', slot: 'price', align: 'center' },
        // { title: '备注', slot: 'note', align: 'center'},
        { title: '操作', slot: 'action', align: 'center' },
      ],

      product_60_tableCols: [
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        // { title: '待入库数量', slot: 'surplus_quantity', align: 'center' },
        { title: '入库数量', slot: 'quantity', align: 'center' },
        { title: '含税金额', slot: 'amount', align: 'center' },
        { title: '税率', slot: 'tax_rate', align: 'center', width: '110' },
        { title: '不含税金额', slot: 'amount_without_tax', align: 'center' },
        { title: '单价', slot: 'price', align: 'center' },
        // { title: '备注', slot: 'note', align: 'center'},
        { title: '操作', slot: 'action', align: 'center' },
      ],

      product_list: [], // 商品数据
      // product_listAllSelect: [], // 选中的所有商品数据

      // 选择商品
      editProductVisible: false, // 选择商品弹窗
      // storeHouseProductList: [],
    };
  },

  computed: {
    // 是否禁用表单项
    isDisabled() {
      if (this.id) {
        return true;
      } else {
        return false;
      }
    },
    // 计算产品总计
    totalMoeny() {
      let total = 0;
      this.product_list.forEach(item => {
        total = $operator.add(Number(total), Number(item.amount));
      });
      return total || 0;
    },
    getFormateSpan() {
      return this.formData.type === '60' ? 24 : 12;
    },
    taxInList() {
      return function (index) {
        if (!this.product_list[index].tax_rate) {
          return null;
        }
        let enumList = this.rate_enum.map(v => v.kw);
        if (!enumList.includes(this.product_list[index].tax_rate)) {
          return this.product_list[index].tax_rate;
        }
        return null;
      };
    },
  },

  watch: {
    'formData.type': {
      deep: true,
      handler(newVal, oldVal) {
        // 产品信息切换列表栏
        this.tableColChange(newVal);
        if (this.id || this.code) {
          return;
        }
        // 非详情创建
        if (!this.isDetailCreate) {
          this.clearPoppverInfo();
        }
        if ((newVal != '10' && newVal !== '40') || oldVal == '20' || oldVal == '30') {
          this.product_list = [];
          this.selectWarehouseList = [];
          this.formData.warehouse_code = '';
        }
      },
    },
    visible: {
      immediate: true,
      handler(val) {
        if (val) {
          console.log(21312321, this.id);
          this.formData.inbound_date = moment(new Date()).format('YYYY-MM-DD');
          // 如果有id，表示回显入库单数据
          if (this.id) {
            this.getErpInboundInfo();
            this.getErpInboundDetail();
          }
        }
      },
    },
    isDetailCreate: {
      immediate: true,
      handler(val) {
        if (val) {
          this.createDetailEnter();
        }
      },
    },
  },

  created() {
    // 获取枚举值
    this.getErpInboundOptions();
  },

  mounted() {},

  methods: {
    validateHouseList(rule, value, callback, index) {
      try {
        if (!this.formData.storeHouseList[index]?.code) {
          callback(new Error('请选择销售退货单'));
        } else {
          callback();
        }
      } catch (error) {}
    },
    selectChange(e, node, index) {
      // poppver的ref
      let nodeList = [
        'selectWarehouse',
        'selectProduct',
        'selectPurchase',
        'selectPurchaseReturn',
        'selectProductPopperReturn',
      ];
      // select date 的ref
      let selectNodeList = ['enter-type', 'enter-time'];
      if (e) {
        let hideList = nodeList.filter(item => item !== node);
        if (node === 'selectProductPopperReturn') {
          this.$refs[node].forEach((comp, ind) => {
            if (ind !== index) {
              comp.showPop = false;
            }
          });
        }
        hideList.forEach(item => {
          if (item === 'selectProductPopperReturn') {
            this.$refs[item] &&
              this.$refs[item].forEach((comp, ind) => {
                comp.showPop = false;
              });
          } else {
            this.$refs[item] && (this.$refs[item].showPop = false);
          }
        });
        selectNodeList.forEach(item => {
          this.$refs[item] && (this.$refs[item].visible = false);
        });
      }
    },
    /**
     * @description:计算单价，计算公式：含税金额 / 入库数量 ，支持小数点后四位展示；
     * @param { number } index 商品索引
     */
    calcPurchasePrice(index) {
      // 含税金额
      let price = this.product_list[index].price;
      // 入库数量
      let quantity = this.product_list[index].quantity;
      if (this.isDetailCreate) {
        this.product_list[index].amount = $operator.multiply(quantity, price);
      } else {
        let price = 0;
        let amount = this.product_list[index].amount;

        if (Number(quantity) !== 0) {
          price = $operator.divide(Number(amount), Number(quantity), 4);
        }
        this.product_list[index].price = price;
      }
    },
    // 采购数量发生变化时
    quantityChange(index) {
      // 计算采购单价
      if (this.formData.type === '20') {
        this.changePrice(index);
      } else {
        this.calcPurchasePrice(index);
      }
    },
    // 含税金额失焦
    purchaseBlur(index, calcPrice = true) {
      let _purchase_price = this.product_list[index].amount;
      let _rate = this.product_list[index].tax_rate;

      // 当含税金额和税率均有值时，才可以计算不含税金额的数据
      if (_purchase_price && _rate) {
        this.rateChange('', index);
      }

      // 计算单价
      calcPrice && this.calcPurchasePrice(index);
    },
    taxBlur(index) {
      let inTaxRate = Number(this.product_list[index].amount);
      let unTaxRate = Number(this.product_list[index].amount_without_tax);
      if (inTaxRate && unTaxRate) {
        this.getTaxRateFunc(index, inTaxRate, unTaxRate);
      }
    },
    getTaxRateFunc(index, inTaxRate, unTaxRate) {
      let taxRate = $operator.divide($operator.subtract(inTaxRate, unTaxRate), unTaxRate, 6);
      taxRate = taxRate.toFixed(6);
      this.product_list[index].tax_rate = String(taxRate);
      // this.product_list[index].tax_rate = taxRate;
    },
    changePrice(index) {
      let price = this.product_list[index].price || 0;
      let quantity = this.product_list[index].quantity || 0;
      let amount = $operator.multiply(Number(price), Number(quantity));
      this.product_list[index].amount = amount;
      this.purchaseBlur(index, false);
    },
    // 税率变更
    rateChange(e, index) {
      this.editTaxInputNumber(index, false);
      let _purchase_price = this.product_list[index].amount;
      let _rate = Number(this.product_list[index].tax_rate);
      if (_purchase_price && (_rate || _rate == 0)) {
        this.product_list[index].amount_without_tax = $operator.divide(Number(_purchase_price), Number(1 + _rate));
      }
    },
    // 去零
    removeZero(key, index) {
      let currentValue = this.$refs[key + index].currentValue;
      if (Number(currentValue) == 0) {
        this.product_list[index].key = null;
        this.$refs[key + index].currentValue = null;
      }
    },

    // 校验产品是否存在数量，单价，折扣未填写的情况
    validProductCalc() {
      let isUnValid = false;
      if (!this.product_list.length) {
        this.$Message.error('商品不可为空');
        isUnValid = true;
        return true;
      }
      this.product_list &&
        this.product_list.some(item => {
          // 入库数量,含税金额,税率,不含税金额均为必填,销售退货入库时，只有入库数量字段
          if (this.formData.type == 30 && !item.quantity) {
            this.$Message.error(`请输入【${item.name}】入库数量`);
            isUnValid = true;
            return true;
          }
          if (
            this.formData.type != 30 &&
            (!item.quantity || !item.amount || !item.tax_rate || !item.amount_without_tax)
          ) {
            if (!item.quantity) {
              this.$Message.error(`请输入【${item.name}】入库数量`);
              isUnValid = true;
              return true;
            }
            if (!item.amount) {
              this.$Message.error(`请【${item.name}】含税金额`);
              isUnValid = true;
              return true;
            }
            if (!item.tax_rate) {
              this.$Message.error(`请选择【${item.name}】税率`);
              isUnValid = true;
              return true;
            }

            if (!item.amount_without_tax) {
              this.$Message.error(`请输入【${item.name}】不含税金额`);
              isUnValid = true;
              return true;
            }
          }

          if (this.formData.type !== '30') {
            if (item.amount_without_tax) {
              if (Number(item.amount_without_tax) > Number(item.amount)) {
                this.$Message.error(`【${item.name}】的不含税金额不得大于含税金额`);
                isUnValid = true;
                return true;
              }
            }
          }
        });
      return isUnValid;
    },

    // 选中采购订单
    selectPurchase(val) {
      this.selectPurchaseList = [];
      this.formData.purchase_code = '';
      if (!S.isEmptyObject(val)) {
        this.selectPurchaseList.push(val);
        this.formData.purchase_code = val.code;
        this.selectWarehouseList = [
          {
            code: val.warehouse_info && val.warehouse_info.warehouse_code,
            name: val.warehouse_info && val.warehouse_info.warehouse_name,
          },
        ];
        this.formData.warehouse_code = val.warehouse_info.warehouse_code;
        // 获取产品信息
        this.getErpPurchaseDetail(val.id, val.code);
      }
      this.$forceUpdate();
      this.$refs.purchaseRef.validateField('purchase_code');
      this.$refs.purchaseRef.validateField('warehouse_code');
    },

    // 选中退货单订单
    selectPurchaseRefund(val) {
      this.selectPurchaseRefundList = [];
      this.formData.refund_code = '';
      if (!S.isEmptyObject(val)) {
        this.selectPurchaseList.push(val);
        this.formData.refund_code = val.code;
        this.selectWarehouseList = [
          {
            code: val.warehouse_info && val.warehouse_info.warehouse_code,
            name: val.warehouse_info && val.warehouse_info.warehouse_name,
          },
        ];
        this.formData.warehouse_code = val.warehouse_info.warehouse_code;
        // 获取产品信息
        this.getErpReturnOrderProductDetail(val.id, val.code);
        this.is_api = val.is_api === '1';
      }
      this.$forceUpdate();
      this.$refs.purchaseRef.validateField('refund_code');
      this.$refs.purchaseRef.validateField('warehouse_code');
    },

    tableColChange(newVal) {
      this.product_tableCols = this[`product_${newVal}_tableCols`];
    },

    // type切换，删除其他类型带出来的数据
    clearPoppverInfo() {
      this.formData.purchase_code = '';
      this.selectPurchaseList = [];
      this.refund_code = '';
      this.selectPurchaseRefundList = [];
      this.formData.storeHouseList = [{}];
      // this.storeHouseProductList = [];
      this.formData.relate_code = '';
    },

    // 添加商品
    addProductE() {
      this.selectChange(true);
      this.editProductVisible = true;
    },
    // 删除商品
    deleteProduct(index) {
      this.product_list.splice(index, 1);
    },
    // 获取勾选的商品
    selectedList(list) {
      this.product_list = [];
      list &&
        list.forEach((item, index) => {
          this.$set(this.product_list, index, {
            ...item,
            quantity: item.quantity || null,
            price: Number(item.price) || null,
            total_price: item.total_price || 0,
            amount: null, // 含税金额
            tax_rate: null, // 税率
            amount_without_tax: null, // 不含税金额
          });
        });
    },
    // 创建
    confirmHandle() {
      this.$refs['purchaseRef'].validate(valid => {
        if (valid) {
          // 商品计算未完成,不允许提交
          if (this.validProductCalc()) {
            return;
          }
          this.id ? this.erpInboundEdit() : this.erpInboundCreate();
        } else {
          this.$Message.error('请填写完整');
        }
      });
    },

    // 选中仓库
    selectWarehouse(val) {
      this.selectWarehouseList = [];
      this.formData.warehouse_code = '';
      if (!S.isEmptyObject(val)) {
        this.selectWarehouseList.push(val);
        this.formData.warehouse_code = val.code;
      }
      this.$forceUpdate();
      this.$refs.purchaseRef.validateField('warehouse_code');
    },

    // 处理商品的价格数据，作为参数
    handlerProductDetails() {
      let detail = [];
      this.product_list &&
        this.product_list.forEach(item => {
          detail.push({
            detail_id: item.detail_id,
            product_id: item.id,
            product_code: item.code,
            quantity: item.quantity,
            note: item.note,
            amount: item.amount,
            tax_rate: item.tax_rate,
            amount_without_tax: item.amount_without_tax,
            price: item.price, // 单价
          });
        });
      return detail || [];
    },

    // api - 获取采购单明细
    getErpPurchaseDetail(id, code) {
      let params = {
        id,
        code,
        is_inbound: 1, // 后端过滤无待入库商品
      };
      this.$api
        .getErpPurchaseDetail(params)
        .then(res => {
          this.product_list = this.handlerPurchaseDetail(res.list, res.products);
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
        });
    },

    // api - 获取采购退货单明细
    getErpReturnOrderProductDetail(id, code) {
      let params = {
        id,
        code,
      };
      this.$api
        .getErpReturnOrderProductDetail(params)
        .then(res => {
          this.product_list = this.handlerDetail(res.list, res.products);
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
        });
    },

    handlerDetail(list, products) {
      let productDetailList = [];
              console.log('-> %c this.is_api  ===    %o', 'font-size: 15px;color: #fa8c16 ;', this.is_api)
      list &&
        list.forEach(item => {
          if (products[item.product_id] && $operator.subtract(Number(item.quantity), Number(item.p_qty))) {
            productDetailList.push({
              ...products[item.product_id],
              detail_id: this.formData.type === '20' ? item.id : '',
              surplus_quantity: $operator.subtract(Number(item.quantity), Number(item.p_qty)),
              quantity: this.is_api ? $operator.subtract(Number(item.quantity), Number(item.p_qty)) : null,
              amount: Number(item.amount) || null, // 含税金额
              tax_rate: item.tax_rate_value || null, // 税率
              amount_without_tax: Number(item.amount_without_tax) || null, // 不含税金额
              price: Number(item.price) || null, // 单价
              total_price: 0,
            });
          }
        });
      return productDetailList || [];
    },
    handlerPurchaseDetail(list, products) {
      let productDetailList = [];
      list &&
        list.forEach(item => {
          if (products[item.product_id] && $operator.subtract(Number(item.quantity), Number(item.p_qty))) {
            productDetailList.push({
              ...products[item.product_id],
              detail_id: this.formData.type === '20' ? item.id : '',
              surplus_quantity: Number(item.wait_qty),
              quantity: Number(item.wait_qty),
              amount: Number(item.wait_amount) || null, // 含税金额
              tax_rate: item.tax_rate_value || null, // 税率
              amount_without_tax: Number(item.wait_amount_without_tax) || null, // 不含税金额
              price: Number(item.price) || null, // 单价
              total_price: 0,
            });
          }
        });
      return productDetailList || [];
    },

    // api - 创建入库单
    erpInboundCreate() {
      this.submitLoading = true;
      let params = {
        type: this.formData.type,
        code: this.formData.code,
        warehouse_code: this.formData.warehouse_code,
        inbound_date: this.formData.inbound_date,
        remark: this.formData.remark,
        relate_code:
          this.formData.type == '20'
            ? this.formData.purchase_code
            : this.formData.type == '30'
            ? this.formData.refund_code
            : this.formData.type === '60'
            ? this.formData.storeHouseList.map(item => item.code).join(',')
            : '',
        detail: JSON.stringify(this.handlerProductDetails()),
      };
      this.$api
        .erpInboundCreate(params)
        .then(res => {
          this.$Message.success('创建成功');
          this.cancelHandle();
          this.$emit('refresh');
          this.submitLoading = false;
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
          this.submitLoading = false;
        });
    },

    // api - 获取枚举值
    getErpInboundOptions() {
      this.$api.getErpInboundOptions().then(res => {
        // 获取税率的枚举值
        this.rate_enum = S.descToArrHandle(res.prodTaxRateDesc);
      });
    },

    // 关闭弹窗,清除数据
    cancelHandle() {
      this.formData = { ...initFormData };
      this.$refs.purchaseRef.resetFields();
      this.product_list = [];
      this.$emit('update:visible', false);
      this.$emit('changeCreate', false);
    },

    // 采购详情创建时,获取采购仓库
    getPurchaseWareHouse(code) {
      let params = { code };
      this.$api.getErpPurchaseInfo(params).then(res => {
        this.selectWarehouseList = [
          {
            code: res.warehouse.code,
            name: res.warehouse.name,
          },
        ];
        this.formData.warehouse_code = res.warehouse.code;
      });
    },

    // 采购详情创建时,获取采购仓库
    getRefundWareHouse(code) {
      let params = { code };
      this.$api.getErpReturnOrderInfo(params).then(res => {
        this.is_api = res.is_api === '1';
        this.selectWarehouseList = [
          {
            code: res.warehouse.code,
            name: res.warehouse.name,
          },
        ];
        this.formData.warehouse_code = res.warehouse.code;
        this.getErpReturnOrderProductDetail('', this.code);
      });
    },

    // 详情入库单创建
    async createDetailEnter() {
      this.formData.type = this.type;
      console.log('-> %c this.type  ===    %o', 'font-size: 15px;color: #fa8c16 ;', this.type);
      // 采购订单入库创建
      if (this.type === '20') {
        this.selectPurchaseList = [];
        // this.selectPurchaseList.push({
        //   code: this.code,
        // })
        this.formData.purchase_code = this.code;
        this.getPurchaseWareHouse(this.code);
        this.getErpPurchaseDetail('', this.code);
      } else if (this.type === '30') {
        this.selectPurchaseRefundList = [];
        // this.selectPurchaseRefundList.push({
        //   code: this.code
        // })
        this.formData.refund_code = this.code;
        await this.getRefundWareHouse(this.code);
      }
    },

    // 回显入库数据进行编辑
    getErpInboundInfo() {
      let params = { id: this.id };
      this.$api.getErpInboundInfo(params).then(res => {
        console.log('-> %c res  ===    %o', 'font-size: 15px;color: #fa8c16 ;', res);
        this.formData.type = res.type; // 入库类型
        if (this.formData.type == '20') {
          this.formData.purchase_code = res.relate_code;
        } else if (this.formData.type === '60') {
          this.formData.storeHouseList = res.relate_code.split(',').map(item => {
            return {
              code: item,
            };
          });
          // this.formData.storeHouseList.forEach(item => {
          //   this.storeHouseProductList.push([
          //     {
          //       code: item.code,
          //       product_list: [],
          //     },
          //   ]);
          // });

          // console.log(this.storeHouseProductList);
        } else {
          this.formData.refund_code = res.relate_code;
        }
        this.formData.code = res.code;
        this.selectWarehouseList = [
          {
            code: res.warehouse_info && res.warehouse_info.warehouse_code,
            name: res.warehouse_info && res.warehouse_info.warehouse_name,
          },
        ];
        this.formData.warehouse_code = res.warehouse_info && res.warehouse_info.warehouse_code;
        this.formData.remark = res.remark;
      });
    },

    // 获取回显的产品
    getErpInboundDetail() {
      let params = { id: this.id };
      this.$api.getErpInboundDetail(params).then(res => {
        this.product_list = [];
        res.detail.forEach(detail_item => {
          this.product_list.push({
            ...detail_item,
            ...res.products[detail_item.product_id],
            amount: Number(detail_item.amount),
            amount_without_tax: Number(detail_item.amount_without_tax),
            quantity: Number(detail_item.quantity),
            tax_rate: detail_item.tax_rate_value,
            price: Number(detail_item.price), // 单价
          });
        });
      });
    },

    // api-入库单编辑
    erpInboundEdit() {
      this.submitLoading = true;
      let params = {
        id: this.id,
        detail: JSON.stringify(this.handlerProductDetails()),
      };
      this.$api
        .erpInboundEdit(params)
        .then(res => {
          this.$Message.success('编辑成功');
          this.cancelHandle();
          this.$emit('refresh');
          this.submitLoading = false;
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
          this.submitLoading = false;
        });
    },

    selectedListArr(list, index) {
      if (!list.length) {
        try {
          // this.$set(this.storeHouseProductList, index, []);
          this.$set(this.formData.storeHouseList, index, []);
          // this.storeHouseProductList[index] = [];
          // this.storeHouseList[index] = [];
          // console.log(this.storeHouseProductList);
          // console.log(this.storeHouseList);
        } catch (error) {}
        return;
      }

      if (
        this.formData.storeHouseList.find(item => {
          return item.code === list[0].code;
        })
      ) {
        this.$Message.error('该单据号已选择');
        return;
      }
      // this.formData.storeHouseList[index] = { ...list[0] };
      this.$set(this.formData.storeHouseList, index, { ...list[0] });

      // 多产品的处理
      // this.product_listAllSelect;
      // let { product_list } = cloneDeep(this.formData.storeHouseList[index]);
      // let arr = [];
      // if (product_list.length) {
      //   product_list.forEach(v => {
      //     let { name, code } = v;
      //     let obj = {
      //       name,
      //       code,
      //     };
      //     arr.push(obj);
      //   });
      //   console.log(arr);
      //   this.$set(this.storeHouseProductList, index, arr);
      //   // this.storeHouseProductList[index] = arr;
      //   console.log(this.storeHouseProductList);
      //   let deepCopy = product_list;
      //   this.$set(this.product_listAllSelect, index, deepCopy);
      //   // this.selectedList(flatMap(this.product_listAllSelect));
      // }
      this.$refs.purchaseRef.validateField(`storeHouseList[${index}].code`);
    },
    addList() {
      if (this.formData.storeHouseList.length === 5) {
        this.$Message.error('最多只能添加5条数据');
        return;
      }
      this.formData.storeHouseList.push({});
    },
    deleteListFunc(index) {
      if (this.formData.storeHouseList.length === 1) {
        this.$Message.error('至少保留一条数据');
        return;
      }

      this.formData.storeHouseList.splice(index, 1);
      // this.storeHouseProductList.splice(index, 1);
      // this.product_listAllSelect.splice(index, 1);

      // this.selectedList(flatMap(this.product_listAllSelect));
    },
    editTaxInputNumber(index, bool) {
      console.log(index);
      this.$set(this.product_list[index], 'isEdit', bool);
    },
  },

  destroyed() {},
};
</script>

<style scoped lang="less">
@import url('../../common/modal.less');

.in-side {
}
</style>
