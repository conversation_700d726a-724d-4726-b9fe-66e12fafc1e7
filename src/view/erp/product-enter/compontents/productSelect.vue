<template>
  <el-popover
    placement="bottom"
    width="800"
    transfer
    :visible-arrow="false"
    :poper-options="popOptions"
    popper-class="pop-common"
    @show="onShow"
    v-model="showPop"
    :disabled="isDetail"
    trigger="click"
  >
    <slot slot="reference" style="width: 100%"></slot>
    <div class="popover-content-box">
      <div class="header-box">
        <h4 class="title">选择出库订单</h4>
        <div class="search-box">
          <span>搜索：</span>
          <Input v-model="queryFormData.q" placeholder="请输入产品名称/编码/条形码" @keyup.enter.native="onSearch">
            <Button slot="append" icon="ios-search" @click="onSearch"></Button>
          </Input>
        </div>
      </div>
      <div class="table-wrapper">
        <Table :columns="tableCols" :data="list" ref="section-table" :height="300" :loading="tableLoading">
          <!-- 勾选 -->
          <template slot-scope="{ row, index }" slot="checkBox">
            <Checkbox :value="list[index].checked" @on-change="changeSelectOrder(row, index)"></Checkbox>
          </template>

          <!-- <template slot-scope="{ row }" slot="product_name"> -->
          <!--   <svg-icon iconClass="toxic" className="poison-flag" v-if="row.toxic_drugs == 'true'"></svg-icon> -->
          <!--   {{ row.product_name || '-' }} -->
          <!-- </template> -->

          <!-- 质检报告 -->
          <!-- <template slot-scope="{ row }" slot="quality_inspection_report"> -->
          <!--            {{ row.quality_inspection_report || '-' }}-->
          <!--   <Picture -->
          <!--     style="margin: 10px 0 0 10px" -->
          <!--     accept="image/jpg,image/jpeg,image/png,.pdf" -->
          <!--     :format="['jpg', 'jpeg', 'png', 'pdf']" -->
          <!--     v-model="row.quality_inspection_report" -->
          <!--     :isQueryDetail="true" -->
          <!--   ></Picture> -->
          <!-- </template> -->
          <!-- 出库数量（输入后，默认选中） -->
          <!--          <template slot-scope="{row, index}" slot="quantity">-->
          <!--            <InputNumber-->
          <!--              :value="list[index].quantity"-->
          <!--              :ref="'quantity'+index"-->
          <!--              style="width: 100%"-->
          <!--              :min="0"-->
          <!--              :max="Number(row.batch_code_stock)"-->
          <!--              :precision="0"-->
          <!--              placeholder="请输入"-->
          <!--              @on-change="changeQuantity(row, index)"-->
          <!--            ></InputNumber>-->
          <!--          </template>-->
        </Table>
        <div class="block_20"></div>
        <div style="position: relative">
          <KPage
            :total="total"
            :page-size="+queryFormData.pageSize"
            :current="+queryFormData.page"
            @on-change="handleCurrentChange"
            @on-page-size-change="handleSizeChange"
            style="text-align: center"
            :show-sizer="false"
          />
          <!-- <div style="position: absolute; right: 15px; top: 8px">已选中：{{ quantityTotal }}</div> -->
        </div>
      </div>
      <div class="bottom-btn-wrapper">
        <Button @click="onCancel">取消</Button>
        <Dvd />
        <Dvd />
        <Dvd />
        <Button type="primary" @click="onConfirm">确定</Button>
      </div>
    </div>
  </el-popover>
</template>

<script>
// import S from '@/utils/util'
import Picture from '@/components/upload/picture';
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  product_code: '', // 产品编码
  type: '', // 仓库编码
  q: '',
};
export default {
  name: 'select-batch-popper',
  mixins: [],

  components: {
    Picture,
  },

  props: {
    title: {
      type: String,
      default: '选择批次产品',
    },
    apiName: {
      type: String,
      default: 'getErpOutboundList',
    },
    code: {
      type: String,
      default: '',
    },
    isDetail: {
      type: Boolean,
      default: () => false,
    },
    product_code: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    },
    // product_list: {
    //   type: Array,
    //   default: () => []
    // },
    batchCodeList: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      popOptions: { boundariesElement: 'body', gpuAcceleration: false },
      tableLoading: false,
      list: [],
      tableCols: [
        { slot: 'checkBox', align: 'center', fixed: 'left' },
        { title: '出库编号', key: 'code', align: 'center' },
        { title: '出库类型', key: 'type_text', align: 'center' },
        { title: '出库仓库', key: 'warehouse_name', align: 'center' },
        { title: '出库日期', key: 'outbound_date', align: 'center' },
        { title: '产品', key: 'product_desc', align: 'center' },
        // { title: '生产日期', key: 'production_date', align: 'center', width: 100 },
        // { title: '失效日期', key: 'expiration_date', align: 'center', width: 100 },
        // { title: '批号', key: 'batch_code', align: 'center' },
        // { title: '供应商', key: 'supplier_name', align: 'center' },
        // { title: '质检报告', slot: 'quality_inspection_report', align: 'center', width: 100 },
        // { title: '批次库存', key: 'batch_code_stock', align: 'center', width: 100 }
        // { title: '出库数量', slot: 'quantity', align: 'center', fixed: 'right',width: 100, },
      ],
      queryFormData: {
        ...init_query_form_data,
      },
      total: 0,
      selected_items: {},
      selectedList: [], // 选中的数据
      showPop: false,
    };
  },

  computed: {
    quantityTotal() {
      return this.selectedList.length;
    },
  },

  watch: {
    showPop: {
      immediate: true,
      handler(val) {
        if (val) {
          this.getList();
          console.log(this.batchCodeList, 'batchCodeList');
          this.selectedList = this._.cloneDeep(this.batchCodeList);
          if (this.selectedList.length) {
            this.selected_items = this._.cloneDeep(this.batchCodeList[0]);
          }
        } else {
          this.queryFormData = { ...init_query_form_data };
          this.selectedList = [];
        }
      },
    },
  },

  created() {},

  mounted() {},

  methods: {
    changeSelectOrder(row, index) {
      let isChecked = this.list[index].checked;
      this.list.map(item => (item.checked = false));
      this.list[index].checked = !isChecked;
      if (isChecked) {
        this.selectedList = [];
      } else {
        this.selectedList = [this.list[index]];
      }
    },
    onCancel() {
      this.showPop = false;
    },
    onConfirm() {
      this.$emit('selectBatchList', this.selectedList);
      this.showPop = false;
    },
    onSearch() {
      // this.selectedList = [];
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = 20;
      this.getList();
    },
    getList() {
      this.tableLoading = true;
      let params = {
        ...this.queryFormData,
        product_code: this.product_code,
        type: this.type,
        is_load_products: 1,
      };
      this.$api[this.apiName](params).then(res => {
        this.tableLoading = false;
        this.list = this.handler(res.list);
        this.total = res.total;
      });
    },
    handler(list) {
      if (!list) return [];
      console.log(this.selected_items, 'items');
      list.forEach((item, index) => {
        item.checked = false;
        if (item.code == this.code || this.selected_items.id === item.id) {
          item.checked = true;
          this.selected_items = item;
        }
      });
      return list;
    },
    handleSizeChange(val) {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.queryFormData.page = val;
      this.getList();
    },
    onShow() {
      this.showPop = true;
      // this.getList();

      // if (this.type !== '') {
      //   this.showPop = true;
      // } else {
      //   this.showPop = false;
      // }
    },

    // 表格的选中
    selectAll(val) {
      val &&
        val.forEach((item, index) => {
          let real_index = this.list.findIndex(items => items.id === item.id); // 该全选方法兼容了选项禁用的方法
          this.$set(this.list, real_index, { ...item, _checked: true });
          if (!item._checked) {
            this.selectedList.push(item);
          }
        });
    },
    select(val, row) {
      this.list.map(item => (item.checked = false));
      let _listIndex = this.getIndex('list', row);
      this.$set(this.list, _listIndex, { ...row, _checked: true });
      this.selectedList.push(row);
    },
    selectAllCancel(val) {
      this.list &&
        this.list.forEach((item, index) => {
          let _selectIndex = this.getIndex('selectedList', item);
          if (_selectIndex >= 0) {
            this.$set(this.list, index, { ...item, _checked: false });
            this.selectedList.splice(_selectIndex, 1);
          }
        });
    },
    selectCancel(val, row) {
      let _selectIndex = this.getIndex('selectedList', row);
      let _listIndex = this.getIndex('list', row);
      this.$set(this.list, _listIndex, { ...row, _checked: false });
      this.selectedList.splice(_selectIndex, 1);
    },
    getIndex(key, row) {
      let _index = -1;
      this[key].some((item, index) => {
        if (item.id === row.id) {
          _index = index;
        }
      });
      return _index;
    },
  },

  destroyed() {},
};
</script>

<style scoped lang="less">
:deep(.el-popover__reference-wrapper) {
  width: 100%;
}

.popover-content-box {
  .header-box {
    .title {
      font-weight: 600;
      line-height: 50px;
      padding: 0 20px;
      font-size: 16px;
      border-bottom: 1px solid rgb(223, 225, 230);
    }

    .search-box {
      display: flex;
      align-items: center;
      padding: 10px 20px;

      .el-input {
        width: 200px;
      }

      .el-button {
        margin-left: 10px;
      }
    }
  }

  .bottom-btn-wrapper {
    height: 50px;
    display: flex;
    justify-content: flex-end;
    padding: 10px 20px;
    border-top: 1px solid rgb(223, 225, 230);
    margin-top: 20px;
  }
}
</style>
<style lang="less">
.pop-common {
  padding: 0 !important;
}
</style>
