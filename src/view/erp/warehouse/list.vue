<template>
  <div>
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <Col>
          <FormItem>
            <Input v-model="queryFormData.name" placeholder="请输入仓库名称" clearable />
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Input v-model="queryFormData.code" placeholder="请输入仓库编号" clearable />
          </FormItem>
        </Col>
        <Col>
          <FormItem style="text-align: left">
            <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
            <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
            <Button type="primary" @click="createWarehouse">新建仓库</Button>
          </FormItem>
        </Col>
      </Row>
    </Form>

    <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 240">
      <template slot-scope="{ row }" slot="address">
        {{ row.address || '-' }}
      </template>
      <template slot-scope="{ row }" slot="contact">
        {{ row.contact || '-' }}
      </template>
      <template slot-scope="{ row }" slot="mobile">
        {{ row.mobile || '-' }}
      </template>
      <template slot-scope="{ row }" slot="operator">
        {{ row.operator || '-' }}
      </template>
      <template slot-scope="{ row }" slot="create_time">
        {{ row.create_time | date_format }}
      </template>
      <template slot-scope="{ row }" slot="update_time">
        {{ row.update_time | date_format }}
      </template>
      <template slot-scope="{ row }" slot="action">
        <a @click="editSupplier(row)">编辑</a>
      </template>
    </Table>

    <div class="block_20"></div>
    <KPage
      :total="total"
      :page-size="+queryFormData.pageSize"
      :current="+queryFormData.page"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
      style="text-align: center"
    />
    <edit-warehouse :warehouseId="warehouseId" :visible.sync="editVisible" @refresh="refresh"></edit-warehouse>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'utils/util';
import EditWarehouse from '@/view/erp/warehouse/compontents/EditWarehouse';
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '', // 名称
  r: ''
};

export default {
  name: 'list',
  components: {
    EditWarehouse
  },
  mixins: [search],
  data() {
    return {
      apiName: 'getErpWarehouseList',
      queryFormData: {
        ...init_query_form_data
      },

      tableCols: [
        { title: '仓库名称', key: 'name', align: 'center' },
        { title: '仓库编码', key: 'code', align: 'center' },
        { title: '仓库地址', slot: 'address', align: 'center' },
        { title: '联系人', slot: 'contact', align: 'center' },
        { title: '联系方式', slot: 'mobile', align: 'center' },
        { title: '状态', key: 'status_text', align: 'center' },
        { title: '创建人', slot: 'operator', align: 'center' },
        { title: '创建时间', slot: 'create_time', align: 'center' },
        { title: '更新时间', slot: 'update_time', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' }
      ],
      list_count: {},
      warehouseId: '',
      editVisible: false
    };
  },
  computed: {},
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },
    createWarehouse() {
      this.warehouseId = '';
      this.editVisible = true;
    },
    refresh() {
      this.loadList();
    },
    editSupplier(row) {
      this.warehouseId = row.id;
      this.editVisible = true;
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  }
};
</script>

<style scoped lang="less"></style>

<style lang="less" scoped>
// current page common less
.ml10 {
  margin-left: 10px;
}
</style>
