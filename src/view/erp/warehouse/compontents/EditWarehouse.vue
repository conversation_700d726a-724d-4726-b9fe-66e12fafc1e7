<template>
  <Modal
    :value="visible"
    :title="warehouseId ? '编辑仓库' : '新增仓库'"
    width="900px"
    @on-cancel="cancelHandle"
    :mask-closable="false"
  >
    <Form
      label-position="top"
      class="common-modal-form"
      :model="formData"
      :rules="formDataValidateRules"
      ref="supplierRef"
    >
      <div class="section-header">
        <div class="section-mark"></div>
        <div class="section-title">基本信息</div>
      </div>

      <div class="create-section">
        <FormItem label="仓库名称" prop="name" class="common-form-item">
          <Input v-model="formData.name" placeholder="请输入仓库名称" maxlength="20"></Input>
        </FormItem>

        <FormItem label="仓库编码" prop="code" class="common-form-item">
          <Input v-model="formData.code" placeholder="请输入仓库编码" :disabled="!!warehouseId"></Input>
        </FormItem>

        <FormItem label="仓库状态" prop="status" class="common-form-item">
          <Select v-model="formData.status" placeholder="请选择仓库状态">
            <Option value="ENABLE">启用</Option>
            <Option value="DISABLE">停用</Option>
          </Select>
        </FormItem>

        <FormItem label="仓库地址" prop="address" class="common-form-item">
          <Input v-model="formData.address" placeholder="请输入仓库地址" maxlength="50"></Input>
        </FormItem>

        <FormItem label="联系人" class="common-form-item">
          <Input v-model="formData.contact" placeholder="请输入联系人"></Input>
        </FormItem>

        <FormItem label="联系方式" class="common-form-item">
          <Input v-model="formData.mobile" placeholder="请输入联系方式"></Input>
        </FormItem>
      </div>
    </Form>
    <div slot="footer">
      <Button @click="cancelHandle">取消</Button>
      <Button type="primary" @click="confirmHandle" :loading="submitLoading">确定</Button>
    </div>
  </Modal>
</template>

<script>
import S from 'utils/util';
const initFormData = {
  name: '', // 仓库名称
  code: '', // 仓库编码
  address: '', // 仓库地址
  status: '', // 状态
  contact: '', // 联系人
  mobile: '' // 手机号
};

export default {
  name: 'EditProductModal',
  mixins: [],
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    warehouseId: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      formData: { ...initFormData },
      formDataValidateRules: {
        name: [{ required: true, message: '请输入仓库名称', trigger: 'blur' }],
        code: [{ required: true, message: '请选择仓库编码', trigger: 'blur' }],
        status: [{ required: true, message: '请选择仓库状态', trigger: 'blur' }]
      },
      submitLoading: false // 弹窗确定的loading
    };
  },

  computed: {},

  watch: {
    visible: {
      immediate: true,
      handler(val) {
        console.log('warehouseId', this.warehouseId);
        if (val && this.warehouseId) {
          this.getErpWarehouseDetail();
        }
      }
    }
  },

  created() {},

  mounted() {},

  methods: {
    // 创建
    confirmHandle() {
      this.$refs['supplierRef'].validate(valid => {
        if (valid) {
          this.warehouseId ? this.editErpWarehouse() : this.createErpWarehouse();
        } else {
          this.$Message.error('请填写完整');
        }
      });
    },

    // api - 创建仓库
    createErpWarehouse() {
      if (!this.validateMobile(this.formData.mobile)) {
        return;
      }
      let params = { ...this.formData };
      this.submitLoading = true;
      this.$api
        .createErpWarehouse(params)
        .then(res => {
          this.$Message.success('创建成功');
          this.cancelHandle();
          this.$emit('refresh');
          this.submitLoading = false;
        })
        .catch(err => {
          this.submitLoading = false;
        });
    },

    // api - 编辑仓库
    editErpWarehouse() {
      if (!this.validateMobile(this.formData.mobile)) {
        return;
      }
      let params = {
        ...this.formData,
        id: this.warehouseId
      };
      this.submitLoading = true;
      this.$api
        .editErpWarehouse(params)
        .then(res => {
          this.$Message.success('编辑成功');
          this.cancelHandle();
          this.$emit('refresh');
          this.submitLoading = false;
        })
        .catch(err => {
          this.submitLoading = false;
        });
    },

    // api - 获取仓库详情
    getErpWarehouseDetail() {
      let params = {
        id: this.warehouseId
      };
      this.$api
        .getErpWarehouseDetail(params)
        .then(res => {
          this.formData.name = res.name;
          this.formData.code = res.code;
          this.formData.address = res.address;
          this.formData.status = res.status;
          this.formData.contact = res.contact;
          this.formData.mobile = res.mobile;
        })
        .catch(err => {
        });
    },

    // 关闭弹窗,清除数据
    cancelHandle() {
      this.formData = { ...initFormData };
      this.$emit('update:visible', false);
      this.$refs.supplierRef.resetFields();
    },

    // 手机号校验
    validateMobile(mobile, require) {
      let flag = false;
      if (!mobile && !require) {
        flag = true;
        return flag;
      }
      const reg = /^1[3456789]\d{9}$/;
      if (!reg.test(mobile)) {
        this.$Message.error('请输入正确的手机号');
        return flag;
      }
      return true;
    }
  },

  destroyed() {}
};
</script>

<style scoped lang="less">
@import url('../../common/modal.less');
</style>
