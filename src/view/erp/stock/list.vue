<template>
  <div class="purchase-list-wrapper">
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <Col>
          <FormItem>
            <Input v-model="queryFormData.name" placeholder="请输入产品名称/编码/条形码" clearable />
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Select v-model="queryFormData.warehouse_code" placeholder="请选择仓库" clearable>
              <Option v-for="item in warehouseList" :value="item.code" :key="item.code">{{ item.name }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Input v-model="queryFormData.supplier_name" placeholder="请输入供应商名称" clearable />
          </FormItem>
        </Col>
        <Col>
          <FormItem style="text-align: left">
            <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
            <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
          </FormItem>
        </Col>
      </Row>
      <!--        <FormItem label="供应商：">-->
      <!--          <Select v-model="queryFormData.supplier_code" placeholder="请选择供应商">-->
      <!--            <Option v-for="item in supplierList" :value="item.code" :key="item.code">{{ item.name }}</Option>-->
      <!--          </Select>-->
      <!--        </FormItem>-->
    </Form>

    <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 240">
      <!-- 产品条码 -->
      <template slot-scope="{ row }" slot="barcode">
        {{ row.barcode || '-' }}
      </template>
      <!-- 产品规格 -->
      <template slot-scope="{ row }" slot="spec">
        {{ row.spec || '-' }}
      </template>
      <!-- 备注 -->
      <template slot-scope="{ row }" slot="remark">
        {{ row.remark || '-' }}
      </template>

      <template slot-scope="{ row }" slot="audit_status_text">
        <span :style="{ color: statusColor(row.audit_status) }">{{ row.audit_status_text }}</span>
      </template>

      <!-- 创建人 -->
      <template slot-scope="{ row }" slot="operator">
        {{ row.operator || '-' }}
      </template>

      <template slot-scope="{ row }" slot="create_time">
        {{ row.create_time | date_format('YYYY-MM-DD') }}
      </template>
      <template slot-scope="{ row }" slot="update_time">
        {{ row.update_time | date_format('YYYY-MM-DD') }}
      </template>
    </Table>

    <div class="block_20"></div>
    <KPage
      :total="total"
      :page-size="+queryFormData.pageSize"
      :current="+queryFormData.page"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
      style="text-align: center"
    />
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'utils/util';
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  warehouse_code: '', // 仓库编号
  supplier_name: '' // 供应商名称
};

export default {
  name: 'list',
  components: {},
  mixins: [search],
  data() {
    return {
      apiName: 'getErpProductstockList',
      queryFormData: {
        ...init_query_form_data
      },

      tableCols: [
        { title: '产品编码', key: 'code', align: 'center' },
        { title: '产品条码', slot: 'barcode', align: 'center' },
        { title: '产品名称', key: 'name', align: 'center', tooltip: true },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '单位', key: 'unit', align: 'center' },
        { title: '库存量', key: 'stock', align: 'center' },
        { title: '预扣库存', key: 'lock_stock', align: 'center' },
        { title: '所属仓库', key: 'warehouse_name', align: 'center' },
        { title: '所属供应商', key: 'supplier_name', align: 'center' }
      ],
      list_count: {},

      supplierList: [], // 供应商列表
      warehouseList: [], //仓库列表
      statusList: [
        { code: '10', name: '待审核' },
        { code: '90', name: '审核完成' },
        { code: '70', name: '拒绝' }
      ] // 状态列表
    };
  },
  computed: {
    statusColor() {
      return function (status) {
        if (status == '10') {
          return '#2db7f5';
        } else if (status == '90') {
          return '#19be6b';
        } else if (status == '70') {
          return '#ed4014';
        }
      };
    }
  },
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.init();
  },
  mounted() {},
  methods: {
    init() {
      this.getErpSupplierList();
      this.getErpWarehouseList();
    },
    // 新建采购订单
    createPurchaseOrder(row) {
      if (row) {
        this.orderId = row.id;
      } else {
        this.orderId = '';
      }
      this.editVisible = true;
    },

    refresh() {
      this.loadList();
    },

    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    getErpSupplierList() {
      this.$api.getErpSupplierList().then(res => {
        this.supplierList = res.list;
      });
    },
    getErpWarehouseList() {
      this.$api.getErpWarehouseList({pageSize:999}).then(res => {
        this.warehouseList = res.list;
      });
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  }
};
</script>

<style scoped lang="less">
.supplier-list-wrapper {
}

.mr-10 {
  margin-right: 10px;
}

// ::v-deep .ivu-table-cell .ivu-table-cell-slot {
//     display: block;
//     width: 90px;
//     overflow: hidden;
//     text-overflow: ellipsis;
//     white-space: nowrap;
//     word-break: break-all;
//     box-sizing: border-box;
//   }
</style>
