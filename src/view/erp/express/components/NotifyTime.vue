<template>
  <Modal
    :value="visible"
    title="异步通知时间"
    width="900px"
    footer-hide
    @on-cancel="cancelHandle"
    :mask-closable="false"
  >
    <Timeline v-if="notify_time.length > 0">
      <TimelineItem v-for="(item, index) in notify_time" :key="index" :color="index === 0 ? 'red' : 'blue'">
        <div slot="dot" class="dot" :style="{ backgroundColor: index === 0 ? '#D63232' : '#D8D8D8' }"></div>
        <p :style="{ color: index === 0 ? '#D63232' : '#333' }" class="time">
          {{ item | date_format }} <span v-if="index === 0" class="latest ml10">最新</span>
        </p>
        <Divider class="mt20" />
      </TimelineItem>
    </Timeline>
    <div class="empty" v-else>暂无数据</div>
  </Modal>
</template>

<script>
const initFormData = {
  page: 1,
  pageSize: 20
};

export default {
  name: 'NotifyTime',
  mixins: [],
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    notify_time: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      formData: { ...initFormData }
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  methods: {
    // 关闭弹窗,清除数据
    cancelHandle() {
      this.formData = { ...initFormData };
      this.$emit('update:visible', false);
    }
  },

  destroyed() {}
};
</script>

<style scoped lang="less">
@import url('../../common/modal.less');

.dot {
  width: 7px;
  height: 7px;
  border-radius: 50%;
  background-color: red;
  margin: auto;
}

.latest {
  display: inline-block;
  width: 30px;
  height: 14px;
  text-align: center;
  line-height: 14px;
  font-size: 12px;
  color: #fff;
  background-color: #d63232;
  border-radius: 2px;
}
</style>
