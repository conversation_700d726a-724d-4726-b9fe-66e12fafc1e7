<template>
  <Modal :value="visible" title="新增物流" width="900px" @on-cancel="cancelHandle" :mask-closable="false">
    <Form
      label-position="top"
      class="common-modal-form"
      :model="formData"
      :rules="formDataValidateRules"
      ref="expressRef"
    >
      <div class="section-header">
        <div class="section-mark"></div>
        <div class="section-title">基本信息</div>
      </div>

      <div class="create-section">
        <FormItem label="业务类型" prop="biz_type" class="common-form-item">
          <Select v-model="formData.biz_type" placeholder="请选择业务类型">
            <Option v-for="item in prodBizTypeDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>

        <FormItem
          label="业务单号"
          v-show="formData.biz_type !== 'P9999'"
          :prop="formData.biz_type !== 'P9999' ? 'biz_id' : ''"
          class="common-form-item"
        >
          <Input v-model="formData.biz_id" placeholder="请输入业务单号"></Input>
        </FormItem>

        <FormItem label="业务单号" v-show="formData.biz_type === 'P9999'" class="common-form-item">
          <Input v-model="formData.a" placeholder="请输入业务单号"></Input>
        </FormItem>

        <FormItem label="快递公司" prop="express_code" class="common-form-item">
          <Select v-model="formData.express_code" placeholder="请选择仓库状态">
            <Option v-for="item in prodExpressCodeDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>

        <FormItem label="快递单号" prop="express_no" class="common-form-item">
          <Input v-model="formData.express_no" placeholder="请输入快递单号"></Input>
        </FormItem>

        <FormItem label="收件人姓名" class="common-form-item">
          <Input v-model="formData.consignee" placeholder="请输入收件人姓名"></Input>
        </FormItem>

        <FormItem label="收件人手机号" class="common-form-item">
          <Input v-model="formData.mobile" placeholder="请输入收件人手机号"></Input>
        </FormItem>

        <FormItem label="收件人目的地" class="common-form-item">
          <Input v-model="formData.destination" placeholder="请输入收件人目的地"></Input>
        </FormItem>
      </div>
    </Form>
    <div slot="footer">
      <Button @click="cancelHandle">取消</Button>
      <Button type="primary" @click="confirmHandle" :loading="submitLoading">确定</Button>
    </div>
  </Modal>
</template>

<script>
import S from 'utils/util';
const initFormData = {
  biz_type: '', // 业务类型
  biz_id: '', // 业务单号
  express_code: '', // 快递公司
  express_no: '', // 快递单号
  consignee: '', // 收件人
  mobile: '', // 电话
  destination: '' // 目的地
};

export default {
  name: 'AddExpress',
  mixins: [],
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    warehouseId: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      formData: { ...initFormData },
      formDataValidateRules: {
        biz_type: [{ required: true, message: '请选择业务类型', trigger: 'blur' }],
        biz_id: [
          // { validate: validatorBizId, message: '请输入业务单号', trigger: 'blur' },
          { required: true, message: '请输入业务单号', trigger: 'blur' }
        ],
        express_code: [{ required: true, message: '请选择快递公司', trigger: 'blur' }],
        express_no: [{ required: true, message: '请选择快递单号', trigger: 'blur' }]
      },
      submitLoading: false, // 弹窗确定的loading
      prodBizTypeDesc: [], // 业务类型
      prodExpressCodeDesc: [] // 快递公司
    };
  },

  computed: {},

  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.getErpLogisticsOptions();
        }
      }
    }
  },

  created() {},

  mounted() {},

  methods: {
    // 枚举
    getErpLogisticsOptions() {
      this.$api.getErpLogisticsOptions().then(res => {
        this.prodBizTypeDesc = S.descToArrHandle(res.prodBizTypeDesc);
        this.prodExpressCodeDesc = S.descToArrHandle(res.prodExpressCodeDesc);
        console.log('=>(AddExpress.vue:130) this.prodExpressCodeDesc', this.prodExpressCodeDesc);
      });
    },

    // 创建物流
    confirmHandle() {
      this.$refs['expressRef'].validate(valid => {
        if (valid) {
          this.createErpLogistics();
        } else {
          this.$Message.error('请填写完整');
        }
      });
    },

    // api - 创建物流
    createErpLogistics() {
      if (!this.validateMobile(this.formData.mobile)) {
        return;
      }
      let params = { ...this.formData };
      this.submitLoading = true;
      this.$api
        .createErpLogistics(params)
        .then(res => {
          this.$Message.success('创建成功');
          this.cancelHandle();
          this.$emit('refresh');
          this.submitLoading = false;
        })
        .catch(err => {
          this.submitLoading = false;
        });
    },

    // 关闭弹窗,清除数据
    cancelHandle() {
      this.formData = { ...initFormData };
      this.$emit('update:visible', false);
      this.$refs.expressRef.resetFields();
    },

    // 手机号校验
    validateMobile(mobile, require) {
      let flag = false;
      if (!mobile && !require) {
        flag = true;
        return flag;
      }
      const reg = /^1[3456789]\d{9}$/;
      if (!reg.test(mobile)) {
        this.$Message.error('请输入正确的手机号');
        return flag;
      }
      return true;
    }
  },

  destroyed() {}
};
</script>

<style scoped lang="less">
@import url('../../common/modal.less');
</style>
