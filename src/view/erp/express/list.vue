<template>
  <div class="purchase-list-wrapper">
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <!-- <div class="no-wrap"> -->
      <Row>
        <Col>
          <FormItem>
            <Input v-model="queryFormData.express_no" placeholder="请输入快递单号" clearable />
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Select v-model="queryFormData.sort_type" placeholder="请选择排序" clearable>
              <Option value="last_update_time">物流更新时间排序</Option>
            </Select>
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Input v-model="queryFormData.biz_id" placeholder="请输入业务单号" clearable />
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Select v-model="queryFormData.advanced_status" placeholder="请选择物流状态" clearable filterable>
              <Option v-for="item in prodAdvancedStatusDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Select
              @on-query-change="querySearch"
              v-model="queryFormData.express_code"
              placeholder="请选择物流公司"
              clearable
              filterable
            >
              <Option v-for="(item, index) in expressCompanyList" :key="index" :value="item.code">{{
                item.desc
              }}</Option>
            </Select>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col>
          <FormItem>
            <DatePicker
              type="daterange"
              clearable
              format="yyyy-MM-dd"
              placeholder="请选择订阅时间"
              v-model="subscriptionRange"
              @on-change="times => handleTimeChange(times, 'sub_st', 'sub_et')"
              class="time-range"
            ></DatePicker>
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <DatePicker
              type="daterange"
              clearable
              format="yyyy-MM-dd"
              placeholder="物流更新时间"
              v-model="subUpdateRange"
              @on-change="times => handleTimeChange(times, 'last_st', 'last_et')"
              class="time-range"
            ></DatePicker>
          </FormItem>
        </Col>
        <Col>
          <FormItem style="text-align: left">
            <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
            <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
            <Button type="primary" class="mr10" @click="addExpress">新增物流</Button>
          </FormItem>
        </Col>
      </Row>
      <!-- </div> -->
    </Form>

    <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 300">
      <template slot-scope="{ row }" slot="biz_id">
        {{ row.biz_id || '-' }}
      </template>
      <template slot-scope="{ row }" slot="destination">
        {{ row.destination || '-' }}
      </template>

      <template slot-scope="{ row }" slot="latest_data">
        <span class="_3U0W">{{ row.latest_data.time }} - {{ row.latest_data.context }}</span>
        <a @click="getInfo(row)" v-if="row.latest_data.time">展开</a>
      </template>
      <template slot-scope="{ row }" slot="subscribe_time">
        <div style="text-align: left">
          <div>订阅时间: {{ row.subscribe_time | date_format }}</div>
          <div>订阅状态: {{ row.subscribe_status_text }}</div>
        </div>
      </template>

      <template slot-scope="{ row }" slot="latest_time">
        <p>{{ row.last_update_time | date_format }}</p>
        <p v-if="row.last_update_time && row.subscribe_status !== 'shutdown' && row.diffTime">
          {{ row.diffTime }} 未更新
        </p>
      </template>

      <template slot-scope="{ row }" slot="action">
        <Poptip confirm title="确认更新?" @on-ok="update(row.id)">
          <a>更新物流信息</a>
        </Poptip>
        <!--        <Divider type="vertical"></Divider>-->
        <p><a @click="showNotify(row)">异步通知时间</a></p>
      </template>
    </Table>

    <div class="block_20"></div>
    <KPage
      :total="total"
      :page-size="+queryFormData.pageSize"
      :current="+queryFormData.page"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
      style="text-align: center"
    />
    <add-express :visible.sync="addVisible" @refresh="refresh"></add-express>
    <notify-time :visible.sync="notifyVisible" :notify_time="notify_time"></notify-time>
    <k-logistics-progress
      v-model="logisticsVisible"
      :express_detail="express_detail"
      :progress_id="progress_id"
      :is-logistics-detail="false"
    ></k-logistics-progress>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'utils/util';
import AddExpress from './components/AddExpress';
import NotifyTime from './components/NotifyTime';
import KLogisticsProgress from '../../../components/k-logistics-progress/k-logistics-progress';
import { $operator } from '@/utils/operation';
import moment from 'moment';
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  express_no: '', // 快递单号
  sort_type: '', // 排序
  advanced_status: '', // 物流状态
  express_code: '',
  sub_st: '',
  sub_et: '',
  last_st: '',
  last_et: '',
};

export default {
  name: 'list',
  components: { KLogisticsProgress, AddExpress, NotifyTime },
  mixins: [search],
  data() {
    return {
      apiName: 'getErpLogisticsList',
      queryFormData: {
        ...init_query_form_data,
      },

      tableCols: [
        { title: '快递单号', key: 'express_no', align: 'center' },
        { title: '物流公司', key: 'express_company', align: 'center' },
        { title: '业务类型', key: 'biz_type_text', align: 'center' },
        { title: '业务单号', slot: 'biz_id', align: 'center' },
        { title: '采购单号', align: 'center', render: (h, { row }) => h('span', {}, row.operator || '-') },
        { title: '物流状态', key: 'advanced_status_text', align: 'center' },
        { title: '目的地', slot: 'destination', align: 'center' },
        { title: '最新物流信息', slot: 'latest_data', align: 'center', minWidth: 120 },
        { title: '订阅信息', slot: 'subscribe_time', align: 'center', minWidth: 120 },
        { title: '物流更新时间', slot: 'latest_time', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' },
      ],
      list_count: {},
      logisticsVisible: false,
      progress_id: '', // 列表快递ID
      progress_no: '', // 物流单号
      progress_name: '', // 物流公司
      addVisible: false, // 新增弹框
      notifyVisible: false, // 异步通知弹框
      list: [],
      notify_time: [],
      prodAdvancedStatusDesc: [], // 物流状态
      express_detail: [],
      subscriptionRange: [],
      subUpdateRange: [],
      expressCompanyList: [],
    };
  },
  watch: {
    logisticsVisible: {
      immediate: true,
      handler(val) {
        if (!val) {
          this.progress_id = '';
        }
      },
    },
  },
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.getErpLogisticsOptions();
    this.getErpOutboundExpress();
    // console.log("=>(list.vue:118) moment.seconds(1)", moment().second(1000))
  },
  mounted() {},
  methods: {
    querySearch(e) {
      if (e === '') {
        this.queryFormData.express_code = '';
      }
    },
    getErpOutboundExpress() {
      this.$api.getErpOutboundExpress({ is_all: '1' }).then(res => {
        // this.expressCompanyList = res.express;
        this.expressCompanyList = S.descToArrHandle(res.express);
        console.log(this.expressCompanyList);
      });
    },
    // 导出文件
    refresh() {
      this.loadList();
    },

    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
    },

    update(id) {
      let params = { id };
      this.$api
        .getErpLogisticsQueryUpdate(params)
        .then(res => {
          this.$Message.success('更新成功');
          this.loadList();
        })
        .catch(error => {

        });
    },

    getInfo(row) {
      this.logisticsVisible = true;
      this.express_detail = [
        { express_code: row.express_code, express_name: row.express_company, express_no: row.express_no },
      ];
      this.progress_id = row.id;
    },

    addExpress() {
      this.addVisible = true;
    },

    loadList() {
      this.tableLoading = true;
      this.$api[this.apiName](this.queryFormData)
        .then(
          data => {
            this.total = Number(data.total);
            this.list = data.list;

            this.list.forEach(item => {
              if (item.last_update_time && item.subscribe_status !== 'shutdown') {
                let diffTime = $operator.subtract(Math.round(new Date() / 1000), item.last_update_time);
                let day = parseInt(diffTime / 86400);
                let hour = parseInt((diffTime % 86400) / 3600);
                day = day ? day + '天' : '';
                hour = hour ? hour + '小时' : '';
                // console.log("=>(list.vue:172) moment().endOf('day').fromNow();  ", moment(Number(item.last_update_time * 1000)).endOf('day').fromNow());
                item.diffTime = day + hour;
              }
            });
          },
          error => {

          }
        )
        .finally(() => {
          this.tableLoading = false;
        });
    },

    showNotify(row) {
      this.notifyVisible = true;
      this.notify_time = row.notify_time;
      console.log('=>(list.vue:191) this.notify_Time', this.notify_time);
    },

    // 枚举
    getErpLogisticsOptions() {
      this.$api.getErpLogisticsOptions().then(res => {
        this.prodAdvancedStatusDesc = S.descToArrHandle(res.prodAdvancedStatusDesc);
      });
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange('sub_st', 'sub_et', 'subscriptionRange');
    this.getTimeRange('last_st', 'last_et', 'subUpdateRange');
    this.loadList();
    next();
  },
};
</script>

<style scoped lang="less">
.supplier-list-wrapper {
}

.mr-10 {
  margin-right: 10px;
}

._3U0W {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  position: relative;
  -webkit-box-orient: vertical;
  word-break: break-all;
}

// ::v-deep .ivu-table-cell .ivu-table-cell-slot {
//     display: block;
//     width: 90px;
//     overflow: hidden;
//     text-overflow: ellipsis;
//     white-space: nowrap;
//     word-break: break-all;
//     box-sizing: border-box;
//   }
</style>
