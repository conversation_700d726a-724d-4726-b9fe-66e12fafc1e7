<template>
  <Timeline style="padding-left: 15px">
    <TimelineItem v-for="item in timeList" :key="item.id"
      ><span>{{ item.create_time | date_format }}</span> <span>{{ item.operator }}</span>
      {{ item.remark }}</TimelineItem
    >
  </Timeline>
</template>

<script>
export default {
  props: {
    b_type: {
      type: Number,
      default: 0
    },
    b_id: {
      type: Number,
      default: 0
    },
    isRecord: {
      type: Number,
      default: 0
    }
  },
  watch: {
    isRecord: {
      handler(val) {
        this.getErpOperationlog(this.b_type, this.b_id);
      }
    }
  },
  data() {
    return {
      timeList: []
    };
  },
  methods: {
    getErpOperationlog(b_type, b_id) {
      let params = {
        b_type,
        b_id
      };
      this.$api.getErpOperationlog(params).then(res => {
        console.log('🚀 ~ file: operationlog-record.vue ~ line 45 ~ this.$api.getErpOperationlog ~ res', res);
        this.timeList = res.records;
      });
    }
  }
};
</script>

<style></style>
