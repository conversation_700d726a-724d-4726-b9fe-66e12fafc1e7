<template>
  <div class="purchase_return-list-wrapper">
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <Col>
          <FormItem>
            <Input v-model="queryFormData.code" placeholder="请输入采购退货单编号" clearable />
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Select v-model="queryFormData.audit_status" placeholder="请选择状态" clearable>
              <Option v-for="item in statusList" :value="item.code" :key="item.code">{{ item.name }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Select v-model="queryFormData.push_status" style="width: 180px" placeholder="推送状态" clearable>
              <Option v-for="item in pushStatus" :value="item.code" :key="item.code">{{ item.name }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col>
          <FormItem style="text-align: left">
            <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
            <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
            <Button type="primary" @click="createPurchaseReturnOrder">新建采购退货单</Button>
          </FormItem>
        </Col>
      </Row>
    </Form>

    <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 240">
      <!-- 采购单编号 -->
      <template slot-scope="{ row }" slot="purchase_code">
        <KLink
          v-if="row.purchase_code"
          :to="{ path: '/erp/purchase/detail', query: { code: row.purchase_code } }"
          target="_blank"
          >{{ row.purchase_code }}</KLink
        >
        <span v-else>-</span>
      </template>

      <template slot-scope="{ row }" slot="remark">
        {{ row.remark || '-' }}
      </template>
      <template slot-scope="{ row }" slot="audit_status_text">
        <!--        <span :style="{color:statusColor(row.audit_status)}">{{row.audit_status_text }}</span>-->
        <status-text :status="row.audit_status"
          ><span>{{ row.audit_status_text }}</span></status-text
        >
      </template>
      <template slot-scope="{ row }" slot="supplier_name">
        {{ row.supplier_info.supplier_name }}
      </template>

      <template slot-scope="{ row }" slot="create_time">
        {{ row.create_time | date_format('YYYY-MM-DD') }}
      </template>
      <template slot-scope="{ row }" slot="update_time">
        {{ row.update_time | date_format('YYYY-MM-DD') }}
      </template>
      <template slot-scope="{ row }" slot="push_status">
        <span v-if="row.push_status === '4'"
          >{{ row.push_status_text }}
          <Tooltip placement="bottom" max-width="200" :content="row.hangup_text">
            <Icon type="md-help-circle" style="font-size: 16px" class="cursor" />
          </Tooltip>
        </span>
        <span v-else>{{ row.push_status_text }}</span>
      </template>

      <template slot-scope="{ row }" slot="action">
        <a v-if="row.audit_status == '70'" class="mr-10" @click="createPurchaseReturnOrder(row)">编辑</a>
        <a href="javascript:;" @click="goDetail(row)">详情</a>
      </template>
    </Table>

    <div class="block_20"></div>
    <KPage
      :total="total"
      :page-size="+queryFormData.pageSize"
      :current="+queryFormData.page"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
      style="text-align: center"
    />
    <EditPurchaseReturn :orderId="orderId" :visible.sync="editVisible" @refresh="refresh"></EditPurchaseReturn>
  </div>
</template>

<script>
import EditPurchaseReturn from '@/view/erp/purchase_return/compontents/EditPurchaseReturn';
import search from '@/mixins/search';
import S from 'utils/util';
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  code: '', // 名称
  push_status: '', // 推送状态
  r: ''
};

export default {
  name: 'list',
  components: { EditPurchaseReturn },
  mixins: [search],
  data() {
    return {
      apiName: 'getErpPurchaseReturnList',
      queryFormData: {
        ...init_query_form_data
      },
      pushStatus: [
        { name: '不推送', code: '0' },
        { name: '待推送', code: '1' },
        { name: '挂起', code: '4' },
        { name: '已推送', code: '9' }
      ],
      tableCols: [
        { title: '采购退货单编号', key: 'code', align: 'center', width: 130 },
        { title: '采购单编号', slot: 'purchase_code', align: 'center' },
        { title: '供应商名称', slot: 'supplier_name', align: 'center' },
        { title: '退货金额', key: 'total_price', align: 'center' },
        { title: '退货日期', key: 'plan_date', align: 'center' },
        { title: '退货原因', key: 'reason', align: 'center' },
        { title: '备注', slot: 'remark', align: 'center' },
        { title: '审核状态', slot: 'audit_status_text' },
        { title: '入库状态', key: 'stock_status_text', align: 'center' },
        { title: '推送状态', slot: 'push_status', align: 'center', width: 80 },
        { title: '创建人', key: 'operator', align: 'center' },
        { title: '创建时间', slot: 'create_time', align: 'center' },
        { title: '更新时间', slot: 'update_time', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' }
      ],
      list_count: {},

      orderId: '', // 采购订单id
      editVisible: false, // 新建/编辑采购退款订单弹窗
      statusList: [
        { code: '10', name: '审核中' },
        { code: '90', name: '审核完成' },
        { code: '70', name: '拒绝' }
      ] // 状态列表
    };
  },
  computed: {
    statusColor() {
      return function (status) {
        if (status == '10') {
          return '#2db7f5';
        } else if (status == '90') {
          return '#19be6b';
        } else if (status == '70') {
          return '#ed4014';
        }
      };
    }
  },
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    // 新建采购退货单
    createPurchaseReturnOrder(row) {
      if (row) {
        this.orderId = row.id;
      } else {
        this.orderId = '';
      }
      this.editVisible = true;
    },

    refresh() {
      this.loadList();
    },

    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    goDetail(row) {
      this.$router.push({
        path: '/erp/purchase_return/detail',
        query: {
          id: row.id
        }
      });
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  }
};
</script>

<style scoped lang="less">
.supplier-list-wrapper {
}

.mr-10 {
  margin-right: 10px;
}
</style>
