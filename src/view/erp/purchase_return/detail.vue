<template>
  <div class="container">
    <div class="demo-spin-container" v-if="detailLoading">
      <Spin fix></Spin>
    </div>

    <Tabs v-else :value="tab" @on-click="changeTab" :animated="false">
      <!-- 详细资料 -->
      <TabPane label="详细资料" name="detail" :animated="false">
        <div class="block-header">
          基本信息
          <Button type="primary" class="edit-button" @click="showEditModal" v-if="formData.audit_status == '70'"
            >编辑基础信息</Button
          >
        </div>
        <div class="flex flex-between">
          <Form :model="formData" label-position="right" :label-width="100" class="basicInfo">
            <Row :gutter="40">
              <Col span="12">
                <FormItem label="采购退货单编号:">
                  {{ formData.code }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="采购单号:">
                  {{ formData.purchase_code }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="供应商名称:">
                  <span v-if="formData.supplier">{{ formData.supplier.name }}</span>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="退货金额:">
                  {{ formData.total_price }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="退货日期:">
                  {{ formData.plan_date }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="退货原因:">
                  {{ formData.reason || '-' }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="备注:">
                  {{ formData.remark || '-' }}
                </FormItem>
              </Col>
            </Row>
          </Form>
        </div>
        <div class="block-header">系统信息</div>
        <Form :model="formData" label-position="right" :label-width="80" class="basicInfo">
          <Row :gutter="40">
            <Col span="12">
              <FormItem label="创建人:">
                {{ formData.operator }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="创建时间:">
                {{ formData.create_time | date_format }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="更新时间:">
                {{ formData.update_time | date_format }}
              </FormItem>
            </Col>
          </Row>
        </Form>

        <div class="block-header">收件人信息</div>
        <Form :model="formData" label-position="right" :label-width="80" class="basicInfo">
          <Row :gutter="40">
            <Col span="12">
              <FormItem label="发件人:">
                {{ formData.cg_name || '-' }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="发件人电话:">
                {{ formData.cg_mobile || '-' }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="发货地址:">
                {{ getcgAddress || '-' }}
              </FormItem>
            </Col>
          </Row>
        </Form>

        <div class="block-header">产品</div>
        <Table :columns="tableCols" :data="list">
          <!-- 产品编码 -->
          <template slot-scope="{ row, index }" slot="code">
            <KLink v-if="row.code" :to="{ path: '/erp/product/detail', query: { id: row.id } }" target="_blank">{{
              row.code
            }}</KLink>
            <span v-else>{{ row.code }}</span>
          </template>

          <template slot-scope="{ row, index }" slot="barcode">
            {{ row.barcode || '-' }}
          </template>

          <template slot-scope="{ row, index }" slot="spec">
            {{ row.spec || '-' }}
          </template>
          <template slot-scope="{ row, index }" slot="unit">
            {{ row.unit || '-' }}
          </template>
          <template slot-scope="{ row, index }" slot="note">
            {{ row.note || '-' }}
          </template>
        </Table>
        <div class="total mt10">
          已选中产品：<span class="total-num">{{ this.list.length }}</span> 总金额：{{ this.formData.total_amount }}元
        </div>
        <div class="block_20"></div>
        <div class="block_20"></div>
      </TabPane>

      <!-- 出库记录 -->
      <TabPane label="出库记录" name="outboundDetail">
        <div class="mb-10 inbound-title flex flex-item-between">
          <span>待出库产品</span>
          <Button type="primary" @click="productOut" v-if="formData.audit_status === '90' && waitList.length !== 0"
            >出库</Button
          >
        </div>
        <div class="empty" v-if="waitList.length === 0">暂无数据</div>
        <Table v-else :columns="waitTableCols" :data="waitList" height="300">
          <template slot-scope="{ row }" slot="code">
            <KLink
              v-if="row.product && row.product.code"
              :to="{ path: '/erp/product/detail', query: { code: row.product.code } }"
              target="_blank"
              >{{ row.product.code }}</KLink
            >
            <span v-else>-</span>
          </template>

          <template slot-scope="{ row }" slot="barcode">
            {{ row.product.barcode || '-' }}
          </template>
          <template slot-scope="{ row }" slot="name">
            {{ row.product.name }}
          </template>
          <template slot-scope="{ row }" slot="spec">
            {{ row.product.spec }}
          </template>
          <template slot-scope="{ row }" slot="unit">
            {{ row.product.unit }}
          </template>
        </Table>

        <div class="inbound-title mb10 mt10">出库记录</div>
        <div class="inboundTable" v-for="item in outboundList" :key="item.code">
          <div class="flex mb-10 mt10 flex-wrap">
            <div class="mr20">
              出库单号：
              <KLink
                v-if="item.code"
                :to="{ path: '/erp/product-out/detail', query: { code: item.code } }"
                target="_blank"
                >{{ item.code }}</KLink
              >
              <span v-else>-</span>
            </div>
            <div class="mr20">出库仓库：{{ item.warehouse_name }}</div>
            <div class="mr20">出库时间：{{ item.actual_time | date_format('YYYY-MM-DD HH:mm:ss') }}</div>
            <div class="mr20">操作人：{{ item.operator }}</div>
            <div class="mr20">物流方式：{{ item.express_info && item.express_info.express_name }}</div>
            <div v-if="item.express_info && item.express_info.express_code !== 'ZTCK'">
              物流单号：<a @click="showLogistics(item)">{{ item.express_info && item.express_info.express_no }}</a>
            </div>
          </div>
          <Table :columns="outboundTableCols" :data="item.detail" class="mb20">
            <template slot-scope="{ row }" slot="code">
              <KLink
                v-if="row.product && row.product.code"
                :to="{ path: '/erp/product/detail', query: { code: row.product.code } }"
                target="_blank"
                >{{ row.product.code }}</KLink
              >
              <span v-else>-</span>
            </template>
            <template slot-scope="{ row }" slot="barcode">
              {{ row.product.barcode || '-' }}
            </template>

            <template slot-scope="{ row }" slot="name">
              {{ row.product.name }}
            </template>
            <template slot-scope="{ row }" slot="spec">
              {{ row.product.spec }}
            </template>
            <template slot-scope="{ row }" slot="unit">
              {{ row.product.unit }}
            </template>
          </Table>
        </div>
        <div class="empty" v-if="outboundList.length === 0">暂无数据</div>
      </TabPane>

      <!-- 操作记录 -->
      <TabPane label="操作记录" name="operationRecord">
        <operationlog-record :b_type="b_type" :b_id="b_id" :isRecord="isRecord"></operationlog-record>
      </TabPane>
    </Tabs>

    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Button v-if="formData.audit_status === '10'" style="margin: 0 20px" type="error" @click="showRefuseModal"
        >审核驳回
      </Button>
      <Button v-if="formData.audit_status === '10'" type="primary" @click="passCheck">审核通过 </Button>
    </div>

    <EditPurchaseReturn :orderId="orderId" :visible.sync="editVisible" @refresh="init"></EditPurchaseReturn>
    <EditProductOut
      :code="purchase_code"
      :type="'10'"
      :isDetailCreate="isDetailCreate"
      :visible.sync="productVisible"
      @refresh="getErpPurchaseOutboundDetail"
      @changeCreate="changeCreate"
    ></EditProductOut>
    <refuse-reason-modal :visible.sync="refuseModalVisible" :auditSalesOrder="review"></refuse-reason-modal>
    <k-logistics-progress
      v-model="logisticsVisible"
      :progress_no="progress_no"
      :is-logistics-detail="false"
      :progress_code="progress_code"
      :express_detail="progress_express_detail"
    ></k-logistics-progress>
  </div>
</template>

<script>
import EditPurchaseReturn from './compontents/EditPurchaseReturn';
import EditProductOut from '@/view/erp/product-out/compontents/EditProductOut';
import KLogisticsProgress from '@/components/k-logistics-progress';
import OperationlogRecord from '../components/operationlog-record';
import RefuseReasonModal from '@/components/RefuseReasonModal';
import { $operator } from '@/utils/operation';
const init_query_form_data = {
  // page: 1,
  // pageSize: 20,
  id: '', // 采购订单id
  code: '' // 采购订单编号
};

export default {
  data() {
    return {
      tab: 'detail',
      formData: {
        name: '1',
        cg_name: '',
        cg_mobile: '',
        express_name: '', // 快递名称
        cg_address: '',
        cg_city: '',
        cg_province: '',
        cg_county: '',
        express_no: '' // 快递单号
      },
      orderId: '',
      editVisible: false,
      productVisible: false, // 出库创建
      tableCols: [
        { title: '产品编码', slot: 'code', align: 'center' },
        { title: '产品条码', slot: 'barcode', align: 'center' },
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '退货数量', key: 'quantity', align: 'center' },
        { title: '退货单价', key: 'price', align: 'center' },
        { title: '合计', key: 'total_price', align: 'center' }
      ],
      list: [],
      total: 0,
      queryFormData: { ...init_query_form_data },
      productsList: [],
      b_type: 14,
      b_id: 0,
      isRecord: 0,
      showCard: false,
      refuseModalVisible: false,
      reason: '',
      waitTableCols: [
        { title: '产品编码', slot: 'code', align: 'center' },
        { title: '产品条码', slot: 'barcode', align: 'center' },
        { title: '产品名称', slot: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '采购退货数量', key: 'quantity', align: 'center' },
        { title: '已出库', key: 'p_qty', align: 'center' },
        { title: '待出库', key: 'w_qty', align: 'center' }
      ],
      waitList: [],
      outboundTableCols: [
        { title: '产品编码', slot: 'code', align: 'center' },
        { title: '产品条码', slot: 'barcode', align: 'center' },
        { title: '产品名称', slot: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '本次出库', key: 'quantity', align: 'center' },
        { title: '备注', key: 'note', align: 'center' }
      ],
      outboundList: [],
      returnTableCols: [
        { title: '采购退货单编码', key: 'code', align: 'center', width: 120 },
        { title: '采购单编号', key: 'purchase_code', align: 'center', width: 120 },
        { title: '供应商名称', key: 'supplier_name', align: 'center', tooltip: true },
        { title: '退货金额', key: 'total_price', align: 'center' },
        { title: '退货日期', key: 'plan_date', align: 'center' },
        { title: '退货原因', key: 'reason', align: 'center', tooltip: true },
        { title: '备注', key: 'remark', align: 'center', tooltip: true },
        { title: '审核状态', key: 'audit_status_text', align: 'center' },
        { title: '库存状态', key: 'stock_status_text', align: 'center' },
        { title: '创建人', key: 'operator', align: 'center' },
        { title: '创建时间', slot: 'create_time', align: 'center' },
        { title: '更新时间', slot: 'update_time', align: 'center' }
      ],
      returnList: [],
      detailLoading: false,
      purchase_code: '',
      isDetailCreate: false,
      logisticsVisible: false,
      progress_no: '',
      progress_code: '',
      progress_express_detail: []
    };
  },
  computed: {
    getcgAddress() {
      return this.formData.cg_province + this.formData.cg_city + this.formData.cg_county + this.formData.cg_address;
    }
  },
  created() {},
  mounted() {
    this.init();
  },
  components: {
    OperationlogRecord,
    EditPurchaseReturn,
    EditProductOut,
    RefuseReasonModal,
    KLogisticsProgress
  },
  methods: {
    init() {
      if (this.$route.query.id || this.$route.query.code) {
        this.getErpPurchasereturnInfo(this.$route.query.id, this.$route.query.code);
      }
    },
    // tabs事件
    changeTab(name) {
      if (name == 'outboundDetail') {
        this.getErpPurchaseOutboundDetail();
      } else if (name == 'operationRecord') {
        this.isRecord++;
      }
    },
    getErpPurchasereturnInfo(id, code) {
      this.detailLoading = true;
      let params = { id, code };
      this.$api.getErpPurchasereturnInfo(params).then(res => {
        this.formData = res;
        this.purchase_code = res.code;
        this.$router.replace({
          query: {
            ...this.$route.query,
            id: res.id
          }
        });
        this.queryFormData.id = this.$route.query.id;
        this.b_id = Number(this.$route.query.id);
        this.detailLoading = false;
        this.getErpPurchasereturnDetail();
      });
    },
    getErpPurchasereturnDetail() {
      let params = { ...this.queryFormData };
      this.$api.getErpPurchasereturnDetail(params).then(res => {
        this.handlerDetail(res.list, res.products);
      });
    },

    // 处理明细数据，回显采购订单
    handlerDetail(list, products) {
      console.log('-> %c list, products  === %o', 'font-size: 15px;color: green;', list, products);
      let productDetailList = [];
      list &&
        list.forEach(item => {
          productDetailList.push({
            ...products[item.product_id],
            quantity: item.quantity,
            price: item.price,
            total_price: item.total_price
          });
        });
      console.log('-> %c productDetailList  === %o', 'font-size: 15px;color: green;', productDetailList);
      this.list = productDetailList;
      this.handlerTotalPrice(this.list);
    },

    handlerTotalPrice(list) {
      let sum = 0;
      list.forEach(item => {
        sum = $operator.add(sum, Number(item.total_price));
      });
      this.formData.total_amount = sum;
    },

    getErpPurchaseOutboundDetail() {
      let params = { id: this.$route.query.id };
      // let params ={id: '2'}
      this.$api.getErpPurchaseOutboundDetail(params).then(res => {
        this.waitList = res.wait_detail;
        this.outboundList = res.outbound_detail;
      });
    },

    // 新建按钮
    showEditModal() {
      this.orderId = this.$route.query.id;
      this.editVisible = true;
    },

    // 审核按钮
    passCheck() {
      this.$Modal.confirm({
        title: '通过审核',
        content: '您确定要通过该审核吗？',
        onOk: () => {
          this.review('PASS');
        }
      });
    },

    // 审核驳回
    showRefuseModal() {
      this.refuseModalVisible = true;
    },

    refuseCancel() {
      this.refuseModalVisible = false;
    },

    /**
     * @description 审核/驳回的接口
     * @param { action } 审核的状态
     * @param { reason } 驳回的原因
     * */
    review(action, reason) {
      const params = {
        id: this.$route.query.id,
        action,
        reason: ''
      };
      let isPass = true;
      if (reason) {
        params.reason = reason;
        isPass = false;
      }
      this.$api.changeErpPurchaseReturnStatus(params).then(
        res => {
          this.$Message.success(`${isPass ? '通过审核成功' : '驳回审核成功'}`);
          this.init();
        },
      );
    },

    showCardHandle() {
      this.showCard = !this.showCard;
    },
    back() {
      this.$router.back();
    },

    // 出库按钮
    productOut() {
      this.isDetailCreate = true;
      this.productVisible = true;
    },

    // 出库弹窗关闭时间
    changeCreate(val) {
      this.isDetailCreate = val;
    },

    showLogistics(item) {
      this.logisticsVisible = true;
      console.log('=>(detail.vue:435) item', item);
      this.progress_express_detail = item.express_detail;
      this.progress_no = item.express_info.express_no;
      this.progress_code = item.express_info.express_code;
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  .card {
    position: absolute;
    top: 0;
    right: 0;
    width: 350px;
    // height: 200px;
  }
  .card-show {
    position: absolute;
    top: 14px;
    right: 16px;
  }
  .total {
    text-align: right;
    .total-num {
      margin-right: 15px;
      color: #fd715a;
    }
  }

  .inbound-title {
    position: relative;
    padding-left: 10px;
    line-height: 32px;
    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      display: block;
      width: 3px;
      height: 14px;
      content: '';
      background: #0052cc;
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);
    }
  }
}

// ::v-deep .ivu-card-body{
//     padding: 0;
// }

.basicInfo {
  width: 60%;
  margin-left: 60px;
  .basic-item {
    width: 50%;
  }
  .remark {
    width: 100%;
  }
}

.buttonGroup {
  text-align: center;
}

.ml-10 {
  margin-left: 10px;
}

.mr-10 {
  margin-right: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

::v-deep .block-header span {
  font-size: 12px;
  padding: 0;
  font-weight: normal;
}

.edit-button {
  position: absolute;
  top: 3px;
  right: 5px;
}

.demo-spin-container {
  display: inline-block;
  width: 100%;
  height: 100%;
}
</style>
