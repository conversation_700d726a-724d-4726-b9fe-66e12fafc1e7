<template>
  <div>
    <Modal
        :value="visible"
        :title="orderId ? '编辑采购退货单' : '新建采购退货单'"
        width="980px"
        @on-cancel="cancelHandle"
        :mask-closable="false"
    >
      <Form
          label-position="top"
          class="common-modal-form"
          :model="formData"
          :rules="formDataValidateRules"
          ref="purchaseRef"
      >
        <div class="section-header">
          <div class="section-mark"></div>
          <div class="section-title">基本信息</div>
        </div>

        <div class="create-section">
          <FormItem label="采购退货单编号" prop="code" class="common-form-item">
            <Input v-model="formData.code" placeholder="不填系统将自动生成" maxlength="20"></Input>
          </FormItem>

          <FormItem label="采购单号" prop="purchase_code" class="common-form-item">
            <SelectPurchasePopper
                class="flex"
                ref="selectPurchase"
                @selectSup="selectPurchase"
                :code="formData.purchase_code"
                :stock_status="'PART,COMPLETE'"
                is_return="1"
            >
              <el-select
                  :multiple="false"
                  v-model="formData.purchase_code"
                  :multiple-limit="1"
                  style="width: 100%"
                  @visible-change="selectChange($event, 'selectPurchase')"
                  size="small"
                  popper-class="rxj-pop-select"
              >
                <el-option
                    v-for="(item, index) in selectPurchaseList"
                    :value="item.code"
                    :label="item.name"
                    :key="index + item.code"
                ></el-option>
              </el-select>
            </SelectPurchasePopper>
          </FormItem>

          <FormItem label="供应商" prop="supplier_code" class="common-form-item">
            <SelectPopper class="flex" ref="selectSupplier" :isDetail="true" :code="formData.supplier_code">
              <el-select
                  :multiple="false"
                  :disabled="true"
                  v-model="formData.supplier_code"
                  :multiple-limit="1"
                  style="width: 100%"
                  @visible-change="selectChange($event, 'selectSupplier')"
                  size="small"
                  popper-class="rxj-pop-select"
              >
                <el-option
                    v-for="(item, index) in selectSupplierList"
                    :value="item.code"
                    :label="item.name"
                    :key="index + item.code"
                ></el-option>
              </el-select>
            </SelectPopper>
          </FormItem>

          <FormItem label="仓库名称" prop="warehouse_code" class="common-form-item">
            <SelectWarehousePopper class="flex" ref="selectWarehouse" :isDetail="true" :code="formData.warehouse_code">
              <el-select
                  :multiple="false"
                  :disabled="true"
                  v-model="formData.warehouse_code"
                  :multiple-limit="1"
                  style="width: 100%"
                  @visible-change="selectChange($event, 'selectWarehouse')"
                  size="small"
                  popper-class="rxj-pop-select"
              >
                <el-option
                    v-for="(item, index) in selectWarehouseList"
                    :value="item.code"
                    :label="item.name"
                    :key="index + item.code"
                ></el-option>
              </el-select>
            </SelectWarehousePopper>
          </FormItem>

          <FormItem label="日期" prop="plan_date" class="common-form-item">
            <DatePicker
                type="date"
                ref="time"
                :value="formData.plan_date"
                style="width: 100%"
                placeholder="请输入日期"
                @on-change="date => (formData.plan_date = date)"
            ></DatePicker>
          </FormItem>

          <FormItem label="退货原因" prop="reason" class="common-form-item">
            <Input v-model="formData.reason" placeholder="请输入退货原因" maxlength="20"></Input>
          </FormItem>

          <FormItem label="备注" class="common-form-item">
            <Input
                v-model="formData.remark"
                type="textarea"
                maxlength="50"
                show-word-limit
                :autosize="{ maxRows: 2, minRows: 2 }"
            ></Input>
          </FormItem>
        </div>
        <div class="section-header mt10">
          <div class="section-mark"></div>
          <div class="section-title">产品信息</div>
        </div>

        <Table
            class="mt10"
            :loading="tableLoading"
            :columns="product_refund_tableCols"
            :data="product_refund_list"
            @on-select-all="selectAll"
            @on-select="select"
            @on-select-all-cancel="selectAllCancel"
            @on-select-cancel="selectCancel"
        >
          <!-- 退货数量 -->
          <template slot-scope="{ row, index }" slot="quantity">
            <InputNumber
                :ref="'quantity' + index"
                style="width: 100%"
                :min="0"
                :max="+row.surplus_quantity"
                :precision="0"
                v-model="product_refund_list[index].quantity"
                placeholder="请输入退货数量"
                @on-blur="calcPrice('quantity', index)"
            ></InputNumber>
          </template>

          <!-- 退货单价 -->
          <template slot-scope="{ row, index }" slot="price">
            <InputNumber
                :ref="'price' + index"
                style="width: 100%"
                :active-change="false"
                :min="0"
                :precision="4"
                v-model="product_refund_list[index].price"
                placeholder="请输入退货单价"
                disabled
            ></InputNumber>
            <!--                @on-blur="calcPrice('price', index)"-->
          </template>

          <!-- 合计 -->
          <template slot-scope="{ row, index }" slot="total_price">

            <InputNumber
                :ref="'total_price' + index"
                style="width: 100%"
                :active-change="false"
                :min="0"
                :precision="2"
                :max="+row.refundable_amount"
                v-model="product_refund_list[index].total_price"
                placeholder="请输入退货总价"
                @on-blur="calcPrice('total_price', index)"
            ></InputNumber>
          </template>

          <template slot-scope="{ row, index }" slot="action">
            <a @click="deleteRefundProduct(row, index)">删除</a>
          </template>
        </Table>

        <div class="mt10 flex flex-item-end">
          <span class="custom-label mr10">已选中产品：{{ selectedRefundList.length }}</span>
          <span class="custom-label">总金额：{{ custom_total_price }} 元</span>
        </div>

        <div class="section-header mt10">
          <div class="section-mark"></div>
          <div class="section-title">发货信息</div>
        </div>
        <div class="create-section">
          <FormItem label="客户姓名" class="common-form-item" prop="cg_name" :rules="customRules.cg_name">
            <Input v-model="formData.cg_name" placeholder="请输入收件人姓名"></Input>
          </FormItem>

          <FormItem label="手机号" class="common-form-item" prop="cg_mobile" :rules="customRules.cg_mobile">
            <Input v-model="formData.cg_mobile" placeholder="请输入收件人手机号"/>
          </FormItem>
          <div class="flex" style="width: 100%">
            <FormItem
                :rules="customRules.cg_county"
                label="客户地址"
                prop="cg_county"
                class="common-form-item"
                style="width: 300px"
            >
              <div class="addWrap">
                <div class="addressBox">
                  <!--								<v-region v-model="selectedAddress"-->
                  <!--								          @values="regionChange" :disabled="!$route.query.isEdit &&!!$route.query.id"></v-region>-->
                  <el-cascader
                      v-model="selectedAddress"
                      :options="regionData"
                      placeholder="请选择联系地址"
                      size="small"
                      clearable
                      popper-class="address-com"
                      style="width: 300px"
                      @change="regionChange"
                  >
                  </el-cascader>
                </div>
              </div>
            </FormItem>

            <div class="addressInput ml10" style="width: 100%">
              <FormItem
                  label="详细地址"
                  prop="cg_address"
                  :rules="customRules.cg_address"
                  class="common-form-item"
                  style="width: 100%"
              >
                <Input v-model="formData.cg_address" placeholder="详细地址"/>
              </FormItem>
            </div>
          </div>
        </div>
      </Form>

      <div slot="footer">
        <Button @click="cancelHandle">取消</Button>
        <Button type="primary" @click="confirmHandle" :loading="submitLoading">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import S from 'utils/util';
import {$operator} from '@/utils/operation';
import SelectPurchasePopper from '@/components/select-purchase-popper/select-purchase-popper';
import SelectWarehousePopper from '@/components/select-warehouse-popper/select-warehouse-popper';
import SelectPopper from '@/components/select-popper/select-popper';
import {CodeToText, regionData, TextToCode} from '@/utils/chinaMap';

const initFormData = {
  code: '', //采购单编号
  purchase_code: '', // 采购单编号
  supplier_code: '', //供应商编号
  warehouse_code: '', //仓库编号
  plan_date: '', // 退货日期
  reason: '', // 退货原因
  remark: '', //备注
  cg_province: '', //发件人省
  cg_city: '', //发件人市
  cg_county: '', //发件人区
  cg_address: '', //发件人详细地址
  cg_name: '', //发件人姓名
  cg_mobile: '' //发件人手机号
};

export default {
  name: 'list',
  mixins: [],
  components: {SelectPopper, SelectPurchasePopper, SelectWarehousePopper},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    orderId: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      formData: {...initFormData},
      formDataValidateRules: {
        purchase_code: [{required: true, message: '请选择采购单号', trigger: 'blur,change'}],
        supplier_code: [{required: true, message: '请输入供应商名称', trigger: 'blur,change'}],
        warehouse_code: [{required: true, message: '请选择仓库', trigger: 'blur,change'}]
      },
      regionData: regionData,
      customRules: {
        cg_name: [{required: false, message: '请输入客户姓名', trigger: 'change'}],
        cg_mobile: [
          {required: false, message: '请输入客户手机号', trigger: 'change', pattern: /^(?:(?:\+|00)86)?1[3-9]\d{9}$/}
        ],
        cg_county: [{required: false, message: '请选择收货地址', trigger: 'change'}],
        cg_address: [{required: false, message: '请选择收货地址', trigger: 'change'}]
      },
      submitLoading: false, // 弹窗确定的loading
      selectSupplierList: [], // 选中的供应商

      selectPurchaseList: [], // 选中的采购订单

      tableLoading: false,
      product_refund_tableCols: [
        {type: 'selection', align: 'center'},
        {title: '产品名称', key: 'name', align: 'center'},
        {title: '产品规格', key: 'spec', align: 'center'},
        {title: '产品单位', key: 'unit', align: 'center'},
        {
          title: '采购金额', align: 'center', render: (h, {row}) => {
            return h('div', {}, row.purchase_amount ? `￥${row.purchase_amount}` : '-');
          }
        },
        {
          title: '已完成退款的金额', align: 'center', render: (h, {row}) => {
            return h('div', {}, row.return_amount ? `￥${row.return_amount}` : '-');
          }
        },
        // {title: '剩余数量', key: 'surplus_quantity', align: 'center'},
        {title: '采购数量', key: 'purchase_quantity', align: 'center'},
        {title: '已退款数量', key: 'return_quantity', align: 'center'},
        {title: '本次退货单价', slot: 'price', align: 'center'},
        {title: '本次退货数量', slot: 'quantity', align: 'center'},
        {title: '本次退货金额', slot: 'total_price', align: 'center'},
        {title: '操作', slot: 'action', align: 'center'}
      ],
      product_refund_list: [], // 商品退款数据
      selectedRefundList: [], // 选中的退款产品
      selectWarehouseList: [], // 选中的仓库
      selectedAddress: [], // 选择的地址
      // 选择商品
      editProductVisible: false, // 选择商品弹窗
      isJd: false // 是否是京东
    };
  },

  computed: {
    // 总金额
    custom_total_price() {
      let price = 0;
      this.selectedRefundList.forEach(item => {
        price = $operator.add(Number(price), Number(item.total_price), 4);
      });
      return price || 0;
    }
  },

  watch: {
    visible: {
      immediate: true,
      handler(val) {
        if (val && this.orderId) {
          this.getErpPurchasereturnInfo(this.orderId);
        }
      }
    }
  },

  created() {
  },

  mounted() {
  },

  methods: {
    regionChange(address) {
      console.log('-> %c address  === %o ', 'font-size: 15px;color: green;', address);
      if (address.length) {
        const province = {
          name: CodeToText[address[0]],
          code: address[0]
        };
        const city = {
          name: CodeToText[address[1]],
          code: address[1]
        };
        const county = {
          name: CodeToText[address[2]],
          code: address[2]
        };
        console.log(province, city, county);
        this.formData.cg_province = province.name || '';
        this.formData.cg_city = city.name || '';
        this.formData.cg_county = county.name || '';
      } else {
        this.formData.cg_province = '';
        this.formData.cg_city = '';
        this.formData.cg_county = '';
      }
    },
    dynamicValidate(isRequired) {
      this.isJd = isRequired;
      for (const rule in this.customRules) {
        const ruleInfo = this.customRules[rule];
        this.$set(this.customRules, rule, [{...ruleInfo[0], required: isRequired}]);
      }
    },
    selectChange(e, node) {
      // poppver的ref
      let nodeList = ['selectWarehouse', 'selectPurchase', 'selectSupplier'];
      // select date 的ref
      let selectNodeList = ['time'];
      if (e) {
        let hideList = nodeList.filter(item => item !== node);
        hideList.forEach(item => {
          this.$refs[item] && (this.$refs[item].showPop = false);
        });
        selectNodeList.forEach(item => {
          this.$refs[item] && (this.$refs[item].visible = false);
        });
      }
    },
    // 去零
    // removeZero(key, index) {
    //   // let currentValue = this.$refs[key+index].currentValue
    //   // if ( Number(currentValue) == 0 ) {
    //   //   this.product_refund_list[index].key = null
    //   //   this.$refs[key+index].currentValue = null
    //   // }
    //   this.calcPrice(key,index);
    // },
    // 计算价格
    calcPrice(key, index) {
      let _quantity = this.product_refund_list[index].quantity;
      let _price = this.product_refund_list[index].price;
      let _total_price = this.product_refund_list[index].total_price;
      // if(key === 'total_price'){
      if (_quantity) {
        this.product_refund_list[index].price = $operator.divide(_total_price, _quantity);
      } else {
        this.product_refund_list[index].price = 0
      }
      // }else {
      //   if (_quantity && _price) {
      //     this.product_refund_list[index].total_price = $operator.multiply(Number(_price), Number(_quantity), 4);
      //   } else {
      //     this.product_refund_list[index].total_price = 0;
      //   }
      // }


      // 数据变动，同步变动已勾选的数据
      let id = this.product_refund_list[index].id;
      this.selectedRefundList.some(item => {
        if (item.id == id) {
          item.quantity = _quantity;
          item.price = _price;
          item.total_price = this.product_refund_list[index].total_price;
        }
      });
    },
    // 校验产品是否存在数量，单价未填写的情况
    validProductCalc() {
      let isUnValid = false;
      if (!this.selectedRefundList.length) {
        this.$Message.error('至少勾选一个退款商品');
        isUnValid = true;
        return true;
      }
      this.selectedRefundList &&
      this.selectedRefundList.some(item => {
        if (!item.quantity) {
          this.$Message.error(`商品【${item.name}】的退货数量不可为0`);
          isUnValid = true;
          return true;
        }
        // if (!item.total_price) {
        //   this.$Message.error(`商品【${item.name}】的退货总价不可为空`);
        //   isUnValid = true;
        //   return true;
        // }
      });
      return isUnValid;
    },
    // 创建
    confirmHandle() {
      this.$refs['purchaseRef'].validate(valid => {
        if (valid) {
          // 商品计算未完成,不允许提交
          if (this.validProductCalc()) {
            return;
          }
          this.orderId ? this.purchasereturnEdit() : this.purchasereturnCreate();
        } else {
          this.$Message.error('请填写完整');
        }
      });
    },

    // 选中采购订单
    selectPurchase(val) {
      console.log('-> %c val  === %o', 'font-size: 15px;color: green;', val);
      this.product_refund_list = [];
      this.selectedRefundList = [];
      this.selectPurchaseList = [];
      this.formData.purchase_code = '';
      if (!S.isEmptyObject(val)) {
        this.selectPurchaseList.push(val);
        this.formData.purchase_code = val.code;
        const {warehouse_info, supplier_info} = val;
        console.log(
            '-> %c warehouse_info,supplier_info  === %o',
            'font-size: 15px;color: green;',
            warehouse_info,
            supplier_info
        );
        this.selectSupplierList.push({
          code: supplier_info && supplier_info.supplier_code,
          name: supplier_info && supplier_info.supplier_name
        });
        this.formData.supplier_code = (supplier_info && supplier_info.supplier_code) || '';

        this.selectWarehouseList.push({
          code: warehouse_info && warehouse_info.warehouse_code,
          name: warehouse_info && warehouse_info.warehouse_name
        });

        this.formData.warehouse_code = (warehouse_info && warehouse_info.warehouse_code) || '';
        this.dynamicValidate(warehouse_info && warehouse_info.is_jd === '1');
        // 获取产品信息
        this.getErpPurchaseDetail(val.id, val.code);
      } else {
        this.selectSupplierList = [];
        this.selectWarehouseList = [];
        this.formData.supplier_code = '';
        this.formData.warehouse_code = '';
        this.dynamicValidate(false);
      }

      this.$forceUpdate();
      this.$refs.purchaseRef.validateField('purchase_code');
      this.$refs.purchaseRef.validateField('warehouse_code');
      this.$refs.purchaseRef.validateField('supplier_code');
    },

    /* 产品表格相关事件 */
    // 表格的选中
    selectAll(val) {
      val &&
      val.map((item, index) => {
        let _existIndex = this.getExistIndex(item);
        if (_existIndex == -1) {
          this.$set(this.product_refund_list, index, {...item, _checked: true});
          this.selectedRefundList.push(item);
        }
      });
    },
    select(val, row) {
      [row] &&
      [row].map((item, index) => {
        let _existIndex = this.getExistIndex(item);
        let _productIndex = this.getProductIndex(item);
        if (_existIndex == -1) {
          this.$set(this.product_refund_list, _productIndex, {...item, _checked: true});
          this.selectedRefundList.push(item);
        } else {
          this.$set(this.product_refund_list, _productIndex, {...item, _checked: false});
          this.selectedRefundList.splice(_existIndex, 1);
        }
      });
    },
    selectAllCancel(val) {
      this.product_refund_list &&
      this.product_refund_list.map((item, index) => {
        let _existIndex = this.getExistIndex(item);
        if (_existIndex >= 0) {
          this.$set(this.product_refund_list, index, {...item, _checked: false});
          this.selectedRefundList.splice(_existIndex, 1);
        }
      });
    },
    selectCancel(val, row) {
      [row] &&
      [row].map((item, index) => {
        let _existIndex = this.getExistIndex(item);
        let _productIndex = this.getProductIndex(item);
        if (_existIndex == -1) {
          this.$set(this.product_refund_list, _productIndex, {...item, _checked: true});
          this.selectedRefundList.push(item);
        } else {
          this.$set(this.product_refund_list, _productIndex, {...item, _checked: false});
          this.selectedRefundList.splice(_existIndex, 1);
        }
      });
    },

    getExistIndex(item) {
      let _existIndex = -1;
      this.selectedRefundList.forEach((selected_item, selected_index) => {
        if (selected_item.id == item.id) {
          _existIndex = selected_index;
        }
      });
      return _existIndex;
    },

    getProductIndex(item) {
      let _productIndex = '';
      this.product_refund_list.some((pro_item, pro_index) => {
        if (pro_item.id == item.id) {
          _productIndex = pro_index;
        }
      });
      return _productIndex;
    },

    deleteRefundProduct(row, index) {
      this.product_refund_list.splice(index, 1);
      let _existIndex = -1;
      this.selectedRefundList.forEach((selected_item, selected_index) => {
        if (selected_item.id == row.id) {
          _existIndex = selected_index;
        }
      });
      this.selectedRefundList.splice(_existIndex, 1);
    },

    // 处理商品的价格数据，作为参数
    handlerProductDetails() {
      let detail = [];
      this.selectedRefundList &&
      this.selectedRefundList.forEach(item => {
        detail.push({
          product_id: item.id,
          quantity: item.quantity,
          price: item.price,
          total_price: item.total_price
        });
      });
      return detail || [];
    },

    /**
     * @description: 选中的商品，是否 退货数量小于等于剩余数量
     * @returns { valid } true:校验通过， false:校验不通过
     * */
    validateProduct() {
      let valid = true;
      this.selectedRefundList.some(item => {
        if (Number(item.surplus_quantity) < Number(item.quantity)) {
          this.$Message.error(`商品【${item.name}】的退货数量不可以大于剩余数量`);
          valid = false;
          return true;
        }
      });
      return valid;
    },

    // api - 创建采购退款单
    purchasereturnCreate() {
      this.submitLoading = true;
      let params = {
        ...this.formData,
        detail: JSON.stringify(this.handlerProductDetails())
      };
      this.$api
          .purchasereturnCreate(params)
          .then(res => {
            this.$Message.success('创建成功');
            this.cancelHandle();
            this.$emit('refresh');
            this.submitLoading = false;
          })
          .catch(err => {
            this.$Message.error(err.errmsg);
            this.submitLoading = false;
          });
    },

    // api - 编辑采购退款单
    purchasereturnEdit() {
      if (this.validateProduct()) {
        this.submitLoading = true;
        let params = {
          id: this.orderId,
          ...this.formData,
          detail: this.handlerProductDetails()
        };
        this.$api
            .purchasereturnEdit(params)
            .then(res => {
              this.$Message.success('编辑成功');
              this.cancelHandle();
              this.$emit('refresh');
              this.submitLoading = false;
            })
            .catch(err => {
              this.$Message.error(err.errmsg);
              this.submitLoading = false;
            });
      }
    },

    // 获取退货单详情
    getErpPurchasereturnInfo(id) {
      let params = {
        id
      };
      this.$api.getErpPurchasereturnInfo(params).then(res => {
        console.log('-> %c res  === %o', 'font-size: 15px;color: green;', res);
        this.formData.code = res.code;
        this.formData.plan_date = res.plan_date;
        this.formData.reason = res.reason;
        this.formData.remark = res.remark;

        // 回显采购订单poppver
        this.formData.purchase_code = res.purchase_code;

        // 回显供应商poppver
        this.formData.supplier_code = res.supplier_code;
        this.selectSupplierList.push({
          code: res.supplier && res.supplier.code,
          name: res.supplier && res.supplier.name
        });

        // 回显仓库poppver
        this.formData.warehouse_code = res.warehouse_code;
        this.selectWarehouseList.push({
          code: res.warehouse && res.warehouse.code,
          name: res.warehouse && res.warehouse.name
        });

        this.formData.cg_name = res.cg_name;
        this.formData.cg_mobile = res.cg_mobile;
        this.formData.cg_province = res.cg_province;
        this.formData.cg_city = res.cg_city;
        this.formData.cg_county = res.cg_county;
        this.formData.cg_address = res.cg_address;
        const province = TextToCode[res.cg_province];
        if (res.cg_county) {
          this.selectedAddress = [province.code, province[res.cg_city].code, province[res.cg_city][res.cg_county].code];
        } else {
          this.selectedAddress = [];
        }
        // 回显商品
        this.getErpPurchasereturnDetail(res.id, res.code);
      });
    },

    // api - 获取退货单明细
    getErpPurchasereturnDetail(id, code) {
      let params = {
        id,
        code
      };
      this.$api
          .getErpPurchasereturnDetail(params)
          .then(res => {
            this.product_refund_list = this.handlerDetail(res.list, res.products, true);
          })
          .catch(err => {
            this.$Message.error(err.errmsg);
          });
    },

    // api - 获取采购单明细
    getErpPurchaseDetail(id, code) {
      let params = {
        id,
        code,
        is_return: '1'
      };
      this.$api
          .getErpPurchaseDetail(params)
          .then(res => {
            console.log('-> %c res  === %o', 'font-size: 15px;color: green;', res);
            this.product_refund_list = this.handlerDetail(res.list, res.products);
            const cgInfo = res.sender_info;
            this.formData = {
              ...this.formData,
              ...{
                cg_name: cgInfo.sender_name,
                cg_mobile: cgInfo.sender_mobile,
                cg_province: cgInfo.sender_province,
                cg_city: cgInfo.sender_city,
                cg_county: cgInfo.sender_county,
                cg_address: cgInfo.sender_address
              }
            };
            const province = TextToCode[cgInfo.sender_province];
            if (cgInfo.sender_county) {
              this.selectedAddress = [
                province.code,
                province[cgInfo.sender_city].code,
                province[cgInfo.sender_city][cgInfo.sender_county].code
              ];
            } else {
              this.selectedAddress = [];
            }
          })
          .catch(err => {
            console.log('-> %c err  === %o', 'font-size: 15px;color: green;', err);
            this.$Message.error(err.errmsg);
          });
    },

    handlerDetail(list, products, echo = false) {
      let productDetailList = [];
      list &&
      list.forEach(item => {
        productDetailList.push({
          ...item,
          ...products[item.product_id],
          surplus_quantity: item.r_qty,
          quantity: echo?+item.quantity:+item.refundable_quantity,
          original_price: item.price, // 原价
          total_price: echo?+item.total_price:+item.refundable_amount,
          price: handlePrice(item,echo),
        });
      });

      function handlePrice(item, echo) {
        if(echo){
          return $operator.divide(+item.total_price, +item.quantity, 4)
        }
        if (+item.refundable_quantity > 0) {
          return $operator.divide(+item.refundable_amount, +item.refundable_quantity, 4)
        } else {
          return null
        }
      }

      console.log("%c=>(EditPurchaseReturn.vue:811) productDetailList", 'font-size: 18px;color: #FF7043 ;', productDetailList);
      return productDetailList || [];
    },

    // 关闭弹窗,清除数据
    cancelHandle() {
      this.formData = {...initFormData};
      this.$emit('update:visible', false);
      this.$refs.purchaseRef.resetFields();
      this.product_refund_list = [];
      this.selectPurchaseList = [];
      this.selectedRefundList = [];
      this.selectSupplierList = [];
      this.selectWarehouseList = [];
    }
  },

  destroyed() {
  }
};
</script>

<style scoped lang="less">
@import url('../../common/modal.less');
</style>
