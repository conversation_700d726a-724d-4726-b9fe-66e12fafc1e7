<template>
  <div class="container">
    <div class="demo-spin-container" v-if="detailLoading">
      <Spin fix></Spin>
    </div>

    <Tabs v-else :value="tab" @on-click="changeTab" :animated="false">
      <!-- 详细资料 -->
      <TabPane label="详细资料" name="detail">
        <div class="block-header">基础信息</div>
        <div class="flex flex-between">
          <Form :model="formData" label-position="right" :label-width="100" class="basicInfo">
            <Row :gutter="40">
              <Col span="12">
                <FormItem label="出库单编号:">
                  {{ formData.code }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="预计出库时间:">
                  {{ formData.outbound_date }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="实际出库时间:">
                  {{ formData.actual_time | date_format('YYYY-MM-DD HH:mm:ss') }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="出库类型:">
                  <span>{{ formData.type_text }}</span>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label=" 出库仓库:">
                  <span v-if="formData.warehouse_info">{{ formData.warehouse_info.warehouse_name }}</span>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="关联单号:">
                  {{ formData.relate_code || '-' }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="备注:">
                  {{ formData.remark || '-' }}
                </FormItem>
              </Col>
            </Row>
          </Form>
        </div>
        <div class="block-header">系统信息</div>
        <Form :model="formData" label-position="right" :label-width="100" class="basicInfo">
          <Row :gutter="40">
            <Col span="12">
              <FormItem label="创建人:">
                {{ formData.operator }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="创建时间:">
                {{ formData.create_time | date_format }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="更新时间:">
                {{ formData.update_time | date_format }}
              </FormItem>
            </Col>
          </Row>
        </Form>
        <div class="block-header">产品</div>
        <Table :columns="formData.type == '20' ? saleTableCols : tableCols" :data="list">
          <template slot-scope="{ row, index }" slot="code">
            <!-- 产品编码 -->
            <KLink v-if="row.code" :to="{ path: '/erp/product/detail', query: { id: row.id } }" target="_blank">{{
              row.code
            }}</KLink>
            <span v-else>{{ row.code }}</span>
          </template>

          <template slot-scope="{ row, index }" slot="spec">
            {{ row.spec || '-' }}
          </template>
          <template slot-scope="{ row, index }" slot="unit">
            {{ row.unit || '-' }}
          </template>
          <template slot-scope="{ row, index }" slot="batch_code">
            {{ row.batch_code || '-' }}
          </template>
          <template slot-scope="{ row, index }" slot="note">
            {{ row.note || '-' }}
          </template>
        </Table>
        <div class="block_20"></div>
        <div class="block_20"></div>
      </TabPane>

      <!-- 操作记录 -->
      <TabPane label="操作记录" name="operationRecord">
        <operationlog-record :b_type="b_type" :b_id="b_id" :isRecord="isRecord"></operationlog-record>
      </TabPane>
    </Tabs>

    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
    </div>
  </div>
</template>

<script>
import OperationlogRecord from '../components/operationlog-record';
const init_query_form_data = {
  // page: 1,
  // pageSize: 20,
  id: '' // 采购订单id
};

export default {
  data() {
    return {
      tab: 'detail',
      formData: {
        name: '1'
      },
      editVisible: false,
      tableCols: [
        { title: '产品编码', slot: 'code', align: 'center' },
        { title: '产品条码', key: 'barcode', align: 'center' },
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '出库数量', key: 'quantity', align: 'center' },
        { title: '备注', slot: 'note', align: 'center' }
      ],
      saleTableCols: [
        { title: '产品编码', slot: 'code', align: 'center' },
        { title: '产品条码', key: 'barcode', align: 'center' },
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '出库数量', key: 'quantity', align: 'center' },
        { title: '批号', slot: 'batch_code', align: 'center' },
        { title: '备注', slot: 'note', align: 'center' }
      ],
      list: [],
      total: 0,
      queryFormData: { ...init_query_form_data },
      productsList: [],
      b_type: 16,
      b_id: 0,
      isRecord: 0,
      detailLoading: false
    };
  },
  created() {},
  mounted() {
    this.init();
  },
  components: {
    OperationlogRecord
  },
  methods: {
    init() {
      if (this.$route.query.id || this.$route.query.code) {
        this.getErpOutboundInfo(this.$route.query.id, this.$route.query.code);
      }
    },
    // tabs事件
    changeTab(name) {
      if (name == 'operationRecord') {
        this.isRecord++;
      }
    },
    getErpOutboundInfo(id, code) {
      this.detailLoading = true;
      let params = { id, code };
      this.$api.getErpOutboundInfo(params).then(res => {
        this.formData = res;
        this.$router.replace({
          query: {
            ...this.$route.query,
            id: res.id
          }
        });
        this.queryFormData.id = this.$route.query.id;
        this.b_id = Number(this.$route.query.id);
        this.detailLoading = false;
        this.getErpOutboundDetail();
      });
    },
    getErpOutboundDetail() {
      let params = { ...this.queryFormData };
      this.$api.getErpOutboundDetail(params).then(res => {
        this.handlerDetail(res.detail, res.products);
      });
    },

    // 处理明细数据，回显采购订单
    handlerDetail(list, products) {
      let productDetailList = [];
      list &&
        list.forEach(item => {
          productDetailList.push({
            ...products[item.product_id],
            quantity: item.quantity,
            price: item.price,
            total_price: item.total_price,
            batch_code: item.batch_code
          });
        });
      this.list = productDetailList;
    },

    // 编辑
    showEditModal() {
      this.supplierId = this.$route.query.id;
      this.editVisible = true;
    },

    submitRefuseReason() {
      this.review('REJECT', this.reason);
    },

    back() {
      this.$router.back();
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  //position: relative;
  .total {
    text-align: right;
    .total-num {
      margin-right: 15px;
      color: #fd715a;
    }
  }
}

.basicInfo {
  width: 60%;
  margin-left: 60px;
  .basic-item {
    width: 50%;
  }
  .remark {
    width: 100%;
  }
}

.buttonGroup {
  text-align: center;
}

.ml-10 {
  margin-left: 10px;
}

.mr-10 {
  margin-right: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}
</style>
