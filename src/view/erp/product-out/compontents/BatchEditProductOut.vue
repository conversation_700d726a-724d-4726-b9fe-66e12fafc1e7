<template>
  <div>
    <Modal :value="visible" title="批量新建出库单" width="900px" @on-cancel="cancelHandle" :mask-closable="false">
      <Form
        label-position="top"
        class="common-modal-form"
        :model="formData"
        :rules="formDataValidateRules"
        ref="purchaseRef"
      >
        <div class="section-header">
          <div class="section-mark"></div>
          <div class="section-title">基本信息</div>
        </div>

        <div class="create-section">
          <FormItem label="出库类型 " prop="type" class="common-form-item">
            <Select v-model="formData.type" ref="out-type" placeholder="请选择出库类型" disabled>
              <Option v-for="item in typeList" :value="item.type" :key="item.type">{{ item.name }}</Option>
            </Select>
          </FormItem>

          <FormItem label="采购退货单号" v-if="formData.type == '10'" prop="purchase_code" class="common-form-item">
            <SelectPurchaseReturnPopper
              class="flex"
              ref="selectPurchaseReturn"
              @selectSup="selectPurchaseReturn"
              :code="formData.purchase_code"
            >
              <el-select
                :multiple="false"
                v-model="formData.purchase_code"
                :multiple-limit="1"
                style="width: 100%"
                @visible-change="selectChange('$event', 'selectPurchaseReturn')"
                size="small"
                popper-class="rxj-pop-select"
              >
                <el-option
                  v-for="(item, index) in selectPurchaseList"
                  :value="item.code"
                  :label="item.name"
                  :key="index + item.code"
                ></el-option>
              </el-select>
            </SelectPurchaseReturnPopper>
          </FormItem>

          <FormItem label="销售订单单号" v-if="formData.type == '20'" prop="order_code" class="common-form-item">
            <SelectOrderPopper class="flex" ref="selectSaleOrder" @selectSup="selectOrder" :code="formData.order_code">
              <el-select
                :multiple="false"
                v-model="formData.order_code"
                @visible-change="selectChange('$event', 'selectSaleOrder')"
                :multiple-limit="1"
                style="width: 100%"
                size="small"
                popper-class="rxj-pop-select"
              >
                <el-option
                  v-for="(item, index) in selectOrderList"
                  :value="item.code"
                  :label="item.name"
                  :key="index + item.code"
                ></el-option>
              </el-select>
            </SelectOrderPopper>
          </FormItem>

          <FormItem label="出库仓库" prop="warehouse_code" class="common-form-item">
            <SelectWarehousePopper
              class="flex"
              ref="selectWarehouse"
              @selectSup="selectWarehouse"
              :code="formData.warehouse_code"
              :is-detail="formData.type == '10' || formData.type == '20'"
            >
              <el-select
                :multiple="false"
                v-model="formData.warehouse_code"
                @visible-change="selectChange('$event', 'selectWarehouse')"
                :multiple-limit="1"
                style="width: 100%"
                :disabled="formData.type == '10' || formData.type == '20'"
                size="small"
                @on-change="supplierChange"
                popper-class="rxj-pop-select"
              >
                <el-option
                  v-for="(item, index) in selectWarehouseList"
                  :value="item.code"
                  :label="item.name"
                  :key="index + item.code"
                ></el-option>
              </el-select>
            </SelectWarehousePopper>
          </FormItem>

          <!-- <FormItem label="出库单号 " prop="code" class="common-form-item">
            <Input v-model="formData.code" placeholder="不填系统将自动生成" maxlength="20" :disabled="true"></Input>
          </FormItem> -->

          <FormItem label="预计出库时间" prop="outbound_date" class="common-form-item">
            <DatePicker
              type="date"
              ref="out-time"
              :value="formData.outbound_date"
              style="width: 100%"
              placeholder="请输入预计出库时间"
              @on-change="date => (formData.outbound_date = date)"
            ></DatePicker>
          </FormItem>

          <FormItem label="备注" class="common-form-item">
            <Input
              v-model="formData.remark"
              type="textarea"
              maxlength="50"
              show-word-limit
              :autosize="{ maxRows: 2, minRows: 2 }"
            ></Input>
          </FormItem>
        </div>
      </Form>

      <div class="section-header mt10">
        <div class="section-mark"></div>
        <div class="section-title">产品信息</div>
      </div>
      <div class="table-wrapper">
        <Table class="mt10" :loading="tableLoading" :columns="product_tableCols" :data="product_list">
          <template slot-scope="{ row, index }" slot="spec">
            {{ row.spec || '-' }}
          </template>
          <template slot-scope="{ row, index }" slot="unit">
            {{ row.unit || '-' }}
          </template>
          <template slot-scope="{ row, index }" slot="stock">
            {{ row.stock || 0 }}
          </template>

          <!-- 出库数量 -->
          <template slot-scope="{ row, index }" slot="quantity">
            {{ row.quantity }}
          </template>
          <template slot-scope="{ row, index }" slot="action">
            <a @click="deleteProduct(index)">删除</a>
          </template>
        </Table>
        <div class="add-btn">
          <Button type="primary" icon="md-add" @click="addOutStockItem">新增出库单</Button>
        </div>
      </div>
      <div class="stock-box">
        <div class="stock-item" v-for="(stock, stockIndex) in stockList" :key="stockIndex + 'stock'">
          <h4 class="title">
            出库单{{ stockIndex + 1 }}：
            <div class="specs-group_item-del btn-delete" @click="deleteOstockItem(stockIndex)">×</div>
          </h4>
          <KWidget :labelWidth="70" label="出库单号:" class="ostock-form-item">
            <Input
              v-model="formData.code"
              placeholder="不填系统将自动生成"
              maxlength="20"
              :disabled="true"
              style="width: 50%"
            ></Input>
          </KWidget>
          <div
            class="flex flex-item-between mt20"
            v-for="(express, expressIndex) in stock.express_detail"
            :key="'batch_express' + expressIndex"
          >
            <div class="form-box">
              <KWidget :labelWidth="80" label="快递公司:" required class="ostock-form-item">
                <Select
                  v-model="stockList[stockIndex].express_detail[expressIndex].express_code"
                  ref="express-sel"
                  style="width: 100%"
                >
                  <Option
                    v-for="(item, index) in expressCompanyList"
                    :value="item.code"
                    :label="item.short_name"
                    :key="index + item.code"
                  ></Option>
                </Select>
              </KWidget>
              <KWidget :labelWidth="80" label="快递单号:" required class="ostock-form-item">
                <Input
                  v-model="stockList[stockIndex].express_detail[expressIndex].express_no"
                  placeholder="请输入快递单号"
                ></Input>
              </KWidget>
              <KWidget :labelWidth="10" label="" style="width: 100px; margin-top: 0">
                <Button v-if="expressIndex === 0" type="primary" @click="addExpress(stockIndex)">新增</Button>
                <Button v-else @click="deleteExpress(stockIndex, expressIndex)">删除</Button>
              </KWidget>
            </div>
            <!-- <Button type="primary" @click="deleteOstockItem(stockIndex)">删除该出库单</Button> -->
          </div>

          <Table :columns="ostockColumns" :data="stock.list">
            <!-- 出库数量 -->
            <template slot-scope="{ row, index }" slot="localQuantity">
              <InputNumber
                v-model="stockList[stockIndex].list[index].localQuantity"
                style="width: 100px"
                :min="0"
                :precision="0"
                placeholder="请输入出库数量"
                @on-focus="removeZero($event, 'localQuantity', stockIndex, index)"
              ></InputNumber>
            </template>
            <!-- 批号 -->
            <template slot-scope="{ row, index }" slot="batch_code">
              <Input
                style="width: 140px"
                v-model="stockList[stockIndex].list[index].batch_code"
                placeholder="请输入批号"
              ></Input>
            </template>

            <!-- 备注 -->
            <template slot-scope="{ row, index }" slot="note">
              <Input
                style="width: 140px"
                v-model="stockList[stockIndex].list[index].note"
                placeholder="请输入备注"
              ></Input>
            </template>
            <template slot-scope="{ row, index }" slot="action">
              <a @click="deleteProduct(stockIndex, index)">删除</a>
            </template>
          </Table>
        </div>
      </div>

      <div slot="footer">
        <Button @click="cancelHandle">取消</Button>
        <Button type="primary" @click="confirmHandle" :loading="submitLoading">确定</Button>
      </div>
    </Modal>
    <error-table v-model="errorTableVisible" :fail_list="fail_list"></error-table>
    <!--    <FormItem label="快递公司" prop="express_code" class="common-form-item">-->
    <!--      <Select v-model="formData.express_code" ref="express-sel" style="width: 100%;" >-->
    <!--        <Option v-for="(item, index) in expressCompanyList" :value="item.code" :label="item.short_name"-->
    <!--                :key="index + item.code"></Option>-->
    <!--      </Select>-->
    <!--    </FormItem>-->

    <!--    <FormItem label="快递单号" prop="express_no"  class="common-form-item">-->
    <!--      <Input v-model="formData.express_no" placeholder="请输入快递单号"></Input>-->
    <!--    </FormItem>-->
  </div>
</template>

<script>
import S from 'utils/util';
import { $operator } from '@/utils/operation';
// import SelectPopper from '@/components/select-popper/select-popper'
import SelectWarehousePopper from '@/components/select-warehouse-popper/select-warehouse-popper';
import SelectPurchaseReturnPopper from '@/components/select-purchase-return-popper/select-purchase-return-popper';
import SelectOrderPopper from '@/components/select-order-popper/select-order-popper';
// import SelectProductPopper from '@/components/select-product-popper/select-product-popper'
import ErrorTable from './ErrorTable.vue';
const initFormData = {
  type: '20', // 出库类型
  code: '', // 出库单号
  purchase_code: '', // 采购单号
  order_code: '', // 销售单号
  outbound_date: '', // 出库日期
  warehouse_code: '', // 仓库编号
  relate_code: '', // 关联单号
  remark: '' //备注
};

export default {
  name: 'list',
  mixins: [],
  components: {
    SelectPurchaseReturnPopper,
    SelectOrderPopper,
    SelectWarehousePopper,
    /*SelectProductPopper*/ ErrorTable
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      formData: { ...initFormData },
      formDataValidateRules: {
        type: [{ required: true, message: '请选择出库类型', trigger: 'blur,change' }],
        outbound_date: [{ required: true, message: '请选择预计出库时间', trigger: 'blur,change' }],
        warehouse_code: [{ required: true, message: '请选择仓库', trigger: 'blur,change' }],
        express_code: [{ required: true, message: '请选择快递公司', trigger: 'blur,change' }],
        express_no: [{ required: true, message: '请输入快递单号', trigger: 'blur,change' }]
      },
      typeList: [
        { type: '10', name: '采购退货单出库' },
        { type: '20', name: '销售订单出库' },
        { type: '30', name: '其他出库' }
      ],
      submitLoading: false, // 弹窗确定的loading
      selectWarehouseList: [], // 选中的仓库
      selectPurchaseList: [], // 选中的采购订单
      selectOrderList: [], // 选中的采购退货单

      tableLoading: false,
      product_tableCols: [
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '库存数量', slot: 'stock', align: 'center' },
        { title: '出库总量', key: 'total_quantity', align: 'center' },
        { title: '已出库', key: 'p_qty', align: 'center' }
      ],
      product_list: [], // 商品数据
      // 选择商品
      editProductVisible: false, // 选择商品弹窗
      showWarehouse: false,
      expressCompanyList: [],
      ostockColumns: [
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '本次出库', slot: 'localQuantity', align: 'center' },
        { title: '批号', slot: 'batch_code', align: 'center' },
        { title: '备注', slot: 'note', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' }
      ],
      stockList: [], //  批量出库列表

      // 拦截
      errorTableVisible: false,
      fail_list: []
    };
  },

  computed: {},

  watch: {
    'formData.type': {
      deep: true,
      handler(newVal, oldVal) {
        console.log('🚀 ~ file: EditProductOut.vue ~ line 198 ~ handler ~ newVal, oldVal', newVal, oldVal);
        if (newVal != '30' || oldVal == '10' || oldVal == '20') {
          this.product_list = [];
          this.clearPoppverInfo();
        } else {
          this.clearPoppverInfo();
        }

        if (newVal == '10' || newVal == '20') {
          // this.showWarehouse = true
        } else {
          this.showWarehouse = false;
        }
      }
    },
    'formData.warehouse_code': {
      handler(val) {
        if (!val) {
          this.editProductVisible = false;
        }
      }
    }
  },

  created() {},

  mounted() {
    this.getErpOutboundExpress();
  },

  methods: {
    addExpress(stockIndex) {
      this.stockList[stockIndex].express_detail.push({
        express_code: '',
        express_no: ''
      });
    },
    deleteExpress(stockIndex, expressIndex) {
      this.stockList[stockIndex].express_detail.splice(expressIndex, 1);
    },
    deleteOstockItem(index) {
      console.log('-> %c index  === %o', 'font-size: 15px;color: green;', index);
      this.stockList.splice(index, 1);
    },
    addOutStockItem() {
      if (!this.formData.order_code) {
        this.$Message.error('请先选择销售订单');
        return;
      }
      this.stockList.push(this.handleInitList());
    },
    handleInitList() {
      console.log('-> %c this.product_list  === %o', 'font-size: 15px;color: green;', this.product_list);
      return {
        list: this._.cloneDeep(this.product_list),
        express_detail: [
          {
            express_code: '',
            express_no: ''
          }
        ]
      };
    },
    selectChange(e, node) {
      // poppver的ref
      let nodeList = ['selectWarehouse', 'selectProduct', 'selectSaleOrder', 'selectPurchaseReturn'];
      // select date 的ref
      let selectNodeList = ['out-type', 'out-time', 'express-sel'];
      if (e) {
        let hideList = nodeList.filter(item => item !== node);
        hideList.forEach(item => {
          this.$refs[item] && (this.$refs[item].showPop = false);
        });
        selectNodeList.forEach(item => {
          this.$refs[item] && (this.$refs[item].visible = false);
        });
      }
    },
    // 去零
    removeZero(val, key, stockIndex, index) {
      if (!val) {
        this.stockList[stockIndex].list[index][key] = null;
      }
      // this.calcPrice(index)
    },

    // 校验产品是否存在数量，单价，折扣未填写的情况
    validProductCalc() {
      if (!this.stockList.length) {
        this.$Message.error(`请至少新增一条出库单`);
        return false;
      }
      for (const prod of this.product_list) {
        console.log('-> %c prod  === %o', 'font-size: 15px;color: green;', prod);
        let quantity = prod.quantity;
        let totalQuantity = 0;
        for (let i = 0; i < this.stockList.length; i++) {
          const stockOrder = this.stockList[i];
          console.log('-> %c stockOrder  === %o', 'font-size: 15px;color: green;', stockOrder);
          if (!stockOrder.list.length) {
            this.$Message.error(`【出库单${i + 1}】出库产品不能为空`);
            return false;
          }

          // if ( !stockOrder.express_code ) {
          //   this.$Message.error( `【出库单${ i + 1 }】快递公司不能为空` )
          //   return false
          // }
          // if ( !stockOrder.express_no ) {
          //   this.$Message.error( `【出库单${ i + 1 }】快递单号不能为空` )
          //   return false
          // }
          for (const product of stockOrder.list) {
            console.log('-> %c product  === %o', 'font-size: 15px;color: green;', product);
            if (!product.localQuantity) {
              console.log(1231232);
              this.$Message.error(`【出库单${i + 1}】【商品${product.name}】出库数量不能为空`);
              return false;
            }
            if (product.id === prod.id) {
              totalQuantity = $operator.add(totalQuantity, product.localQuantity);
            }
          }
          for (const express of stockOrder.express_detail) {
            console.log('-> %c express  === %o', 'font-size: 15px;color: green;', express);
            if (!express.express_code) {
              this.$Message.error(`【出库单${i + 1}】快递公司不能为空`);
              return false;
            }
            if (!express.express_no) {
              this.$Message.error(`【出库单${i + 1}】快递单号不能为空`);
              return false;
            }
          }

          if (totalQuantity > quantity) {
            // this.$Message.error(`【商品${ prod.name }】总出库量不能大于出库总量`)
            this.$Message.error(`商品【${prod.name}】批量出库数量有误，请先修改`);
            return false;
          }
        }
      }
      return true;
    },

    // 选中采购退货订单
    selectPurchaseReturn(val) {
      this.selectPurchaseList = [];
      this.formData.purchase_code = '';
      if (!S.isEmptyObject(val)) {
        this.selectPurchaseList.push(val);
        this.formData.purchase_code = val.code;
        this.selectWarehouseList = [
          {
            code: val.warehouse_info && val.warehouse_info.warehouse_code,
            name: val.warehouse_info && val.warehouse_info.warehouse_name
          }
        ];
        this.formData.warehouse_code = val.warehouse_info && val.warehouse_info.warehouse_code;
        // 获取产品信息
        this.getErpPurchasereturnDetail(val.id, val.code);
      }
      this.$forceUpdate();
      this.$refs.purchaseRef.validateField('purchase_code');
      this.$refs.purchaseRef.validateField('warehouse_code');
    },

    // 选中销售订单
    selectOrder(val) {
      this.selectOrderList = [];
      this.formData.order_code = '';
      if (!S.isEmptyObject(val)) {
        this.selectPurchaseList.push(val);
        this.formData.order_code = val.code;
        this.selectWarehouseList = [
          {
            code: val.warehouse_info && val.warehouse_info.warehouse_code,
            name: val.warehouse_info && val.warehouse_info.warehouse_name
          }
        ];
        this.formData.warehouse_code = val.warehouse_info.warehouse_code;
        // 获取产品信息
        this.getErpOrderProductDetail(val.id, val.code);
      }
      this.$refs.purchaseRef.validateField('order_code');
      this.$refs.purchaseRef.validateField('warehouse_code');
      this.$forceUpdate();
    },
    // type切换，删除其他类型带出来的数据
    clearPoppverInfo() {
      this.formData.purchase_code = '';
      this.formData.warehouse_code = '';
      this.selectPurchaseList = [];
      this.formData.relate_code = '';
      this.selectOrderList = [];
    },

    // 添加商品
    addProductE() {
      if (this.formData && this.formData.warehouse_code == '') {
        this.$Message.error('请先选择仓库');
        return;
      }
      this.editProductVisible = true;
    },
    // 删除商品
    deleteProduct(stockIndex, index) {
      this.stockList[stockIndex].list.splice(index, 1);
    },
    // 获取勾选的商品
    selectedList(list) {
      this.product_list = [];
      list &&
        list.forEach((item, index) => {
          this.$set(this.product_list, index, {
            ...item,
            quantity: item.quantity || null,
            price: item.price || null,
            total_price: item.total_price || 0,
            total_quantity: item.stock || 0,
            p_qty: 0,
            stock: item.stock
          });
        });
      this.editProductVisible = true;
    },
    // 创建
    confirmHandle() {
      this.$refs['purchaseRef'].validate(valid => {
        if (valid) {
          // 商品计算未完成,不允许提交
          if (!this.validProductCalc()) {
            return;
          }
          this.erpOutboundBatchCreate();
        } else {
          this.$Message.error('请填写完整');
        }
      });
    },

    // 选中仓库
    selectWarehouse(val) {
      console.log('🚀 ~ file: EditProductOut.vue ~ line 341 ~ selectWarehouse ~ val', val);
      this.selectWarehouseList = [];
      this.formData.warehouse_code = '';
      if (!S.isEmptyObject(val)) {
        this.selectWarehouseList.push(val);
        this.formData.warehouse_code = val.code;
      }
      this.$forceUpdate();
      this.$refs.purchaseRef.validateField('warehouse_code');
    },

    supplierChange(val) {
      console.log('val', val);
    },

    // 处理商品的价格数据，作为参数
    handlerProductDetails() {
      let items = this.stockList.map(item => {
        return {
          detail: item.list.map(subItem => {
            console.log('-> %c subItem  === %o', 'font-size: 15px;color: green;', subItem);
            return {
              product_code: subItem.product_code,
              note: subItem.note,
              quantity: subItem.localQuantity,
              batch_code: subItem.batch_code
            };
          }),
          express_detail: item.express_detail
        };
      });
      console.log('-> %c items  === %o', 'font-size: 15px;color: green;', items);
      return items;
    },

    // api - 获取采购退货单明细
    getErpPurchasereturnDetail(id, code) {
      let params = {
        id,
        code
      };
      this.$api
        .getErpPurchasereturnDetail(params)
        .then(res => {
          this.product_list = this.handlerDetail(res.list, res.products);
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
        });
    },

    // api - 获取销售订单明细
    getErpOrderProductDetail(id, code) {
      let params = {
        id,
        code
      };
      this.$api
        .getErpOrderProductDetail(params)
        .then(res => {
          this.product_list = this.handlerDetail(res.list, res.products);
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
        });
    },

    handlerDetail(list, products) {
      let productDetailList = [];
      list &&
        list.forEach(item => {
          productDetailList.push({
            product_code: item.product_code,
            ...products[item.product_id],
            total_quantity: item.quantity,
            stock: item.stock,
            surplus_quantity: $operator.subtract(Number(item.quantity), Number(item.p_qty)),
            r_qty: item.r_qty,
            //剩余发货数量回显  剩余发货数量大于库存数量 显示仓库数量
            quantity:
              $operator.subtract(Number(item.quantity), Number(item.p_qty)) > +item.stock
                ? +item.stock
                : $operator.subtract(Number(item.quantity), Number(item.p_qty)),
            price: null,
            original_price: item.price, // 原价
            total_price: 0,
            p_qty: item.p_qty,
            note: '',
            localQuantity: null
          });
        });
      console.log('🚀 ~ file: EditProductOut.vue ~ line 416 ~ handlerDetail ~ productDetailList', productDetailList);
      return productDetailList || [];
    },

    // api - 创建出库单
    erpOutboundBatchCreate() {
      this.submitLoading = true;
      let params = {
        type: this.formData.type,
        // code: this.formData.code,
        warehouse_code: this.formData.warehouse_code,
        outbound_date: this.formData.outbound_date,
        remark: this.formData.remark,
        relate_code: this.formData.order_code,
        items: this.handlerProductDetails()
      };
      this.$api
        .erpOutboundBatchCreate(params)
        .then(res => {
          // 存在售后情况，拦截出库单
          if (res.error_type == 1) {
            this.errorTableVisible = true;
            this.fail_list = res.fail_data;
          } else {
            this.$Message.success('批量创建成功');
            this.$emit('refresh');
          }

          this.cancelHandle();
          this.submitLoading = false;
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
          this.submitLoading = false;
        });
    },

    getErpOutboundExpress() {
      this.$api.getErpOutboundExpress().then(res => {
        this.expressCompanyList = res.express;
      });
    },

    // 关闭弹窗,清除数据
    cancelHandle() {
      this.formData = { ...initFormData };
      this.$refs.purchaseRef.resetFields();
      this.product_list = [];
      this.stockList = [];
      this.$emit('update:visible', false);
    }
  },

  destroyed() {}
};
</script>

<style scoped lang="less">
@import url('../../common/modal.less');

.table-wrapper {
  .add-btn {
    margin: 10px 0;
  }
}

.stock-box {
  .stock-item {
    position: relative;
    margin-bottom: 20px;

    .title {
      position: relative;
      font-size: 15px;
      font-weight: 600;
      background: #f8f8f8;
      padding: 10px;
      margin-bottom: 12px;
    }

    .form-box {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      flex: 1;
      margin-right: 30px;

      .ostock-form-item {
        flex: 1;
        margin-top: 0;

        &:last-of-type {
          margin-left: 10px;
        }
      }
    }
  }
}

.stock-item:hover .specs-group_item-del {
  display: block;
}

.btn-delete {
  display: none;
  position: absolute;
  top: 10px;
  right: 10px;
  color: #fff;
  text-align: center;
  cursor: pointer;
  width: 18px;
  height: 18px;
  font-size: 14px;
  line-height: 16px;
  background: rgba(153, 153, 153, 0.6);
  border-radius: 10px;
  text-indent: 0;
  z-index: 10;
  font-family: -webkit-body, sans-serif;
}

.btn-delete:hover {
  background: rgba(25, 25, 25, 0.6);
}
</style>
