<template>
  <div class="export-wrap">
    <Modal
      :value="exportVisible"
      title="导出待出库订单列表"
      width="580px"
      @on-cancel="cancelHandle"
      :mask-closable="false"
    >
      <div class="modal-content">
        <KWidget :label-width="80" label="销售单仓库">
          <Select v-model="formData.warehouse_code" @on-change="changeWarehouse" placeholder="请选择销售单仓库">
            <Option
              v-for="item in getEnableWarehouseList"
              :value="item.code"
              :disabled="item.code !== 'SW001' && item.code !== 'CF001'"
              :key="item.code"
              >{{ item.name }}</Option
            >
          </Select>
        </KWidget>
        <KWidget :label-width="80" label="下单时间">
          <DatePicker
            :options="dateOptions"
            type="daterange"
            placeholder="请选择下单时间"
            @on-change="changeTime"
            v-model="times"
            style="width: 100%"
          ></DatePicker>
        </KWidget>

        <KWidget :label-width="80" label="">
          <p class="result" v-if="showRes">
            该时间段内，查询到{{ warehouseName }}审核完成的 - 待出库/部分出库销售订单 总计
            <span style="font-size: 15px; color: #1157e5">{{ total }}</span
            >条；
          </p>
        </KWidget>
      </div>

      <div slot="footer">
        <Button @click="cancelHandle">取消</Button>
        <Button type="primary" @click="downloadProductFile" :loading="downloadLoading" :disabled="total === 0"
          >确定</Button
        >
      </div>
    </Modal>
  </div>
</template>

<script>
import download from '@/mixins/download';
import downloadCenter from '@/mixins/downloadCenter';

export default {
  name: 'ExportOrderModal',
  mixins: [download, downloadCenter],

  components: {},

  props: {
    exportVisible: {
      type: Boolean,
      default: false,
    },
    warehouseList: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      formData: {
        warehouse_code: 'SW001',
        begin_date: '',
        end_date: '',
      },
      dateOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      downloadUrl: '',
      downloadLoading: false,
      showRes: false,
      total: 0,
      times: [],
    };
  },

  computed: {
    warehouseName() {
      const { warehouse_code } = this.formData;
      const warehouse = this.warehouseList.find(item => item.code === warehouse_code);
      return warehouse ? warehouse.name : '';
    },
    getEnableWarehouseList() {
      console.log('-> this.warehouseList', this.warehouseList);
      return this.warehouseList.filter(item => item.code === 'SW001' || item.code === 'CF001');
    },
  },

  watch: {},

  created() {},

  mounted() {},

  methods: {
    changeTime(times) {
      if (times.length) {
        this.formData.begin_date = times[0];
        this.formData.end_date = times[1];
        this.searchProductOutOrder();
      } else {
        // 清空
        this.formData.begin_date = '';
        this.formData.end_date = '';
      }
    },
    changeWarehouse() {
      this.searchProductOutOrder();
    },
    cancelHandle() {
      this.$emit('update:exportVisible', false);
      this.formData = {
        warehouse_code: 'SW001',
        begin_date: '',
        end_date: '',
      };
      this.showRes = false;
      this.total = 0;
      this.times = [];
    },
    searchProductOutOrder() {
      const { warehouse_code, begin_date, end_date } = this.formData;
      if (!warehouse_code || !begin_date || !end_date) {
        return;
      }
      this.$api.getUnOutOrderCount(this.formData).then(
        res => {
          console.log('-> %c res  === %o', 'font-size: 15px;color: green;', res);
          this.total = +res.totals;
          this.downloadUrl = res.url;
          this.showRes = true;
        },
        err => {
        }
      );
    },
    downloadProductFile() {
      this.$Message.success('导出成功');
      this.createDownloadCenterMission('exportPendingOrder', this.formData);
      this.cancelHandle();
    },
  },

  destroyed() {},
};
</script>

<style scoped lang="less">
.modal-content {
  height: 160px;

  .result {
    line-height: 30px;
  }
}
</style>
