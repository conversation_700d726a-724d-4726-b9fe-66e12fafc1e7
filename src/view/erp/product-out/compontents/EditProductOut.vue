<template>
  <div>
    <Modal
      :value="visible"
      :title="id ? '编辑出库单' : '新建出库单'"
      width="900px"
      @on-cancel="cancelHandle"
      :mask-closable="false"
    >
      <Form
        label-position="top"
        class="common-modal-form"
        :model="formData"
        :rules="formDataValidateRules"
        ref="purchaseRef"
      >
        <div class="section-header">
          <div class="section-mark"></div>
          <div class="section-title">基本信息</div>
        </div>

        <div class="create-section">
          <FormItem label="出库类型 " prop="type" class="common-form-item">
            <Select
              v-model="formData.type"
              ref="out-type"
              @on-change="typeChange"
              placeholder="请选择出库类型"
              :disabled="isDetailCreate"
            >
              <Option v-for="item in typeList" :value="item.type" :key="item.type">{{ item.name }}</Option>
            </Select>
          </FormItem>

          <FormItem label="采购退货单号" v-if="formData.type == '10'" prop="purchase_code" class="common-form-item">
            <SelectPurchaseReturnPopper
              class="flex"
              ref="selectPurchaseReturn"
              @selectSup="selectPurchaseReturn"
              :code="formData.purchase_code"
              :isDetail="isDetailCreate"
            >
              <el-select
                :multiple="false"
                v-model="formData.purchase_code"
                :multiple-limit="1"
                style="width: 100%"
                @visible-change="selectChange('$event', 'selectPurchaseReturn')"
                size="small"
                popper-class="rxj-pop-select"
                :disabled="isDetailCreate"
              >
                <el-option
                  v-for="(item, index) in selectPurchaseList"
                  :value="item.code"
                  :label="item.name"
                  :key="index + item.code"
                ></el-option>
              </el-select>
            </SelectPurchaseReturnPopper>
          </FormItem>

          <FormItem label="销售订单单号" v-if="formData.type == '20'" prop="order_code" class="common-form-item">
            <SelectOrderPopper
              class="flex"
              ref="selectSaleOrder"
              @selectSup="selectOrder"
              :code="formData.order_code"
              :isDetail="isDetailCreate"
            >
              <el-select
                :multiple="false"
                v-model="formData.order_code"
                @visible-change="selectChange('$event', 'selectSaleOrder')"
                :multiple-limit="1"
                style="width: 100%"
                size="small"
                popper-class="rxj-pop-select"
                :disabled="isDetailCreate"
              >
                <el-option
                  v-for="(item, index) in selectOrderList"
                  :value="item.code"
                  :label="item.name"
                  :key="index + item.code"
                ></el-option>
              </el-select>
            </SelectOrderPopper>
          </FormItem>

          <FormItem label="出库仓库" prop="warehouse_code" class="common-form-item">
            <SelectWarehousePopper
              class="flex"
              ref="selectWarehouse"
              @selectSup="selectWarehouse"
              :code="formData.warehouse_code"
              :is-detail="formData.type == '10' || formData.type == '20'"
            >
              <el-select
                :multiple="false"
                v-model="formData.warehouse_code"
                @visible-change="selectChange('$event', 'selectWarehouse')"
                :multiple-limit="1"
                style="width: 100%"
                :disabled="formData.type == '10' || formData.type == '20'"
                size="small"
                @on-change="supplierChange"
                popper-class="rxj-pop-select"
              >
                <el-option
                  v-for="(item, index) in selectWarehouseList"
                  :value="item.code"
                  :label="item.name"
                  :key="index + item.code"
                ></el-option>
              </el-select>
            </SelectWarehousePopper>
          </FormItem>

          <FormItem label="出库单号 " prop="code" class="common-form-item">
            <Input v-model="formData.code" placeholder="不填系统将自动生成" maxlength="20" :disabled="true"></Input>
          </FormItem>

          <FormItem label="预计出库时间" prop="outbound_date" class="common-form-item">
            <DatePicker
              type="date"
              ref="out-time"
              :value="formData.outbound_date"
              style="width: 100%"
              placeholder="请选择预计出库时间"
              @on-change="date => (formData.outbound_date = date)"
            ></DatePicker>
          </FormItem>

          <FormItem label="备注" class="common-form-item">
            <Input
              v-model="formData.remark"
              type="textarea"
              maxlength="50"
              show-word-limit
              :autosize="{ maxRows: 2, minRows: 2 }"
            ></Input>
          </FormItem>
        </div>
        <div class="section-header mt10">
          <div class="section-mark"></div>
          <div class="section-title">包裹信息</div>
        </div>
        <div>
          <FormItem label="出库方式" required class="common-form-item">
            <Select v-model="productOutType" style="width: 351px" @on-change="changeProductOutType">
              <Option
                v-for="(item, index) in productOutTypeList"
                :value="item.code"
                :label="item.name"
                :key="index + item.code"
              ></Option>
            </Select>
          </FormItem>
        </div>
        <div v-if="productOutType === 'express'">
          <div class="express-box flex" v-for="(item, index) in formData.express_detail" :key="'express' + index">
            <FormItem label="快递公司" required class="common-form-item">
              <Select
                v-model="formData.express_detail[index].express_code"
                ref="express-sel"
                style="width: 100%"
                filterable
              >
                <Option
                  v-for="(item, index) in expressCompanyList"
                  :value="item.code"
                  :label="item.name"
                  :key="index + item.code"
                ></Option>
              </Select>
            </FormItem>
            <FormItem label="快递单号" required class="common-form-item">
              <Input v-model="formData.express_detail[index].express_no" placeholder="请输入快递单号" />
            </FormItem>
            <FormItem label="" class="common-form-item" style="width: 100px; margin-top: 25px" v-if="index === 0">
              <Button type="primary" @click="addExpress">新增</Button>
            </FormItem>
            <FormItem label="" class="common-form-item" style="width: 100px; margin-top: 25px" v-else>
              <Button @click="deleteExpress(index)">删除</Button>
            </FormItem>
          </div>
        </div>
      </Form>

      <div class="section-header mt10">
        <div class="section-mark"></div>
        <div class="section-title">产品信息</div>
      </div>
      <div class="flex flex-item-end" v-if="formData.type == '30' || formData.type == '50'">
        <SelectProductPopper
          class="flex"
          ref="selectProduct"
          v-if="formData.warehouse_code"
          @selectedList="selectedList"
          :product_list="product_list"
          :apiName="'getErpWarehouseProds'"
          :warehouse_code="formData.warehouse_code"
        >
          <Button type="primary" @click="addProductE">添加商品</Button>
        </SelectProductPopper>
        <Button type="primary" v-else @click="addProductE">添加商品</Button>
      </div>

      <Table class="mt10" :loading="tableLoading" :columns="product_tableCols" :data="product_list">
        <template slot-scope="{ row, index }" slot="spec">
          {{ row.spec || '-' }}
        </template>
        <template slot-scope="{ row, index }" slot="unit">
          {{ row.unit || '-' }}
        </template>
        <template slot-scope="{ row, index }" slot="stock">
          {{ row.stock || 0 }}
        </template>

        <!-- 出库数量 -->
        <template slot-scope="{ row, index }" slot="quantity">
          <InputNumber
            :ref="'quantity' + index"
            style="width: 100%"
            :min="0"
            :max="+row.stock >= +row.surplus_quantity ? +row.surplus_quantity : +row.stock"
            v-model="product_list[index].quantity"
            placeholder="请输入出库数量"
            @on-blur="removeZero($event, 'quantity', index)"
          ></InputNumber>
        </template>

        <!-- 批号 -->
        <template slot-scope="{ row, index }" slot="batch_code">
          <Input style="width: 100%" v-model="product_list[index].batch_code" placeholder="批号"></Input>
        </template>

        <!-- 备注 -->
        <template slot-scope="{ row, index }" slot="note">
          <Input style="width: 100%" v-model="product_list[index].note" placeholder="请输入备注"></Input>
        </template>

        <template slot-scope="{ row, index }" slot="action">
          <a @click="deleteProduct(index)">删除</a>
        </template>
      </Table>

      <div slot="footer">
        <Button @click="cancelHandle">取消</Button>
        <Button type="primary" @click="confirmHandle" :loading="submitLoading">确定</Button>
      </div>
    </Modal>
    <error-table v-model="errorTableVisible" :fail_list="fail_list"></error-table>
  </div>
</template>

<script>
import S from 'utils/util';
import { $operator } from '@/utils/operation';
// import SelectPopper from '@/components/select-popper/select-popper'
import SelectWarehousePopper from '@/components/select-warehouse-popper/select-warehouse-popper';
import SelectPurchaseReturnPopper from '@/components/select-purchase-return-popper/select-purchase-return-popper';
import SelectOrderPopper from '@/components/select-order-popper/select-order-popper';
import SelectProductPopper from '@/components/select-product-popper/select-product-popper';
import ErrorTable from './ErrorTable.vue';

const initFormData = {
  type: '', // 出库类型
  code: '', // 出库单号
  purchase_code: '', // 采购单号
  order_code: '', // 销售单号
  outbound_date: '', // 出库日期
  warehouse_code: '', // 仓库编号
  relate_code: '', // 关联单号
  remark: '', //备注
  express_detail: [
    {
      express_code: '',
      express_no: '',
    },
  ],
};

export default {
  name: 'EditProductOut',
  mixins: [],
  components: {
    /*SelectPopper,*/
    SelectPurchaseReturnPopper,
    SelectOrderPopper,
    SelectWarehousePopper,
    SelectProductPopper,
    ErrorTable,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: '',
    },
    code: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: '',
    },
    isDetailCreate: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      formData: this.$lodash.cloneDeep(initFormData),
      formDataValidateRules: {
        type: [{ required: true, message: '请选择出库类型', trigger: 'blur,change' }],
        outbound_date: [{ required: true, message: '请选择预计出库时间', trigger: 'blur,change' }],
        warehouse_code: [{ required: true, message: '请选择仓库', trigger: 'blur,change' }],
        express_code: [{ required: true, message: '请选择快递公司', trigger: 'blur,change' }],
        express_no: [{ required: true, message: '请输入快递单号', trigger: 'blur,change' }],
      },
      typeList: [
        { type: '10', name: '采购退货单出库' },
        { type: '50', name: '领料出库' },
        { type: '20', name: '销售订单出库' },
        { type: '30', name: '其他出库' },
      ],
      submitLoading: false, // 弹窗确定的loading
      selectWarehouseList: [], // 选中的仓库
      selectPurchaseList: [], // 选中的采购订单
      selectOrderList: [], // 选中的采购退货单

      tableLoading: false,
      product_tableCols: [
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '库存数量', slot: 'stock', align: 'center' },
        { title: '出库总量', key: 'total_quantity', align: 'center' },
        { title: '已出库', key: 'p_qty', align: 'center' },
        { title: '出库数量', slot: 'quantity', align: 'center' },
        { title: '备注', slot: 'note', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' },
      ],
      product_order_tableCols: [
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '库存数量', slot: 'stock', align: 'center' },
        { title: '出库总量', key: 'total_quantity', align: 'center' },
        { title: '已出库', key: 'p_qty', align: 'center' },
        { title: '出库数量', slot: 'quantity', align: 'center' },
        { title: '备注', slot: 'note', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' },
      ],
      product_sale_tableCols: [
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '库存数量', slot: 'stock', align: 'center' },
        { title: '出库总量', key: 'total_quantity', align: 'center' },
        { title: '已出库', key: 'p_qty', align: 'center' },
        { title: '出库数量', slot: 'quantity', align: 'center' },
        { title: '批号', slot: 'batch_code', align: 'center' },
        { title: '备注', slot: 'note', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' },
      ],
      product_list: [], // 商品数据

      // 选择商品
      editProductVisible: false, // 选择商品弹窗
      showWarehouse: false,
      expressCompanyList: [],
      productOutType: 'express',
      productOutTypeList: [
        { code: 'express', name: '物流' },
        { code: 'ZTCK', name: '自提' },
      ],

      // 拦截
      errorTableVisible: false,
      fail_list: [],
    };
  },

  computed: {},

  watch: {
    'formData.type': {
      deep: true,
      handler(newVal, oldVal) {
        console.log('🚀 ~ file: EditProductOut.vue ~ line 198 ~ handler ~ newVal, oldVal', newVal, oldVal);
        if (newVal != '30' || oldVal == '10' || oldVal == '20') {
          this.product_list = [];
        }
        if (!this.isDetailCreate) {
          this.clearPoppverInfo();
        }
        if (newVal == '10' || newVal == '20') {
          // this.showWarehouse = true
        } else {
          this.showWarehouse = false;
        }
        if (newVal == '20') {
          this.product_tableCols = this.product_sale_tableCols;
        } else {
          this.product_tableCols = this.product_order_tableCols;
        }
      },
    },
    'formData.warehouse_code': {
      handler(val) {
        if (!val) {
          this.editProductVisible = false;
        }
      },
    },
    isDetailCreate: {
      immediate: true,
      handler(val) {
        if (val) {
          this.createDetailEnter();
        }
      },
    },
    selectOrderList: {
      deep: true,
      handler(val) {
        console.log('=>(EditProductOut.vue:275) val', val);
      },
    },
  },

  created() {},

  mounted() {
    this.getErpOutboundExpress();
  },
  // 切换出库方式需清空 , 选择自提需要赋值
  methods: {
    //添加物流
    addExpress() {
      this.formData.express_detail.push({
        express_code: '',
        express_no: '',
      });
    },
    deleteExpress(index) {
      console.log('-> %c index  === %o', 'font-size: 15px;color: green;', index);
      this.formData.express_detail.splice(index, 1);
    },
    selectChange(e, node) {
      // poppver的ref
      let nodeList = ['selectWarehouse', 'selectProduct', 'selectSaleOrder', 'selectPurchaseReturn'];
      // select date 的ref
      let selectNodeList = ['out-type', 'out-time', 'express-sel'];
      if (e) {
        let hideList = nodeList.filter(item => item !== node);
        hideList.forEach(item => {
          this.$refs[item] && (this.$refs[item].showPop = false);
        });
        selectNodeList.forEach(item => {
          this.$refs[item] && (this.$refs[item].visible = false);
        });
      }
    },
    // 去零
    removeZero(val, key, index) {
      if (Number(val) == 0) {
        this.product_list[index].key = null;
        this.$refs[key + index].currentValue = null;
      }
      this.calcPrice(index);
    },
    // 计算价格
    calcPrice(index) {
      let _quantity = this.product_list[index].quantity;
      let _price = this.product_list[index].price;
      let _discount = $operator.divide(Number(this.product_list[index].discount), 100);
      if (_quantity && _price && _discount) {
        this.product_list[index].total_price = $operator.multiply(Number(_price), Number(_quantity));
        this.product_list[index].discount_price = $operator.multiply(
          Number(this.product_list[index].total_price),
          Number(_discount)
        );
      } else {
        this.product_list[index].total_price = 0;
        this.product_list[index].discount_price = 0;
      }
    },

    // 校验产品是否存在数量，单价，折扣未填写的情况
    validProductCalc() {
      if (!this.product_list.length) {
        this.$Message.error('商品不可为空');
        return true;
      }
      for (const product of this.product_list) {
        console.log('=>(EditProductOut.vue:286) item.stock', product.stock);
        if (!product.stock) {
          this.$Message.error(`商品【${product.name}】库存不足`);
          return true;
        }
        if (!product.quantity) {
          this.$Message.error(`商品【${product.name}】请填写完整`);
          return true;
        }
      }

      if (this.productOutType === 'express') {
        for (const express of this.formData.express_detail) {
          if (!express.express_code || !express.express_no) {
            this.$Message.error('请填写完整物流信息');
            return true;
          }
        }
      }

      return false;
    },

    // 选中采购退货订单
    selectPurchaseReturn(val) {
      this.selectPurchaseList = [];
      this.formData.purchase_code = '';
      if (!S.isEmptyObject(val)) {
        this.selectPurchaseList.push(val);
        this.formData.purchase_code = val.code;
        this.selectWarehouseList = [
          {
            code: val.warehouse_info && val.warehouse_info.warehouse_code,
            name: val.warehouse_info && val.warehouse_info.warehouse_name,
          },
        ];
        this.formData.warehouse_code = val.warehouse_info && val.warehouse_info.warehouse_code;
        // 获取产品信息
        this.getErpPurchasereturnDetail(val.id, val.code);
      }
      this.$forceUpdate();
      this.$refs.purchaseRef.validateField('purchase_code');
      this.$refs.purchaseRef.validateField('warehouse_code');
    },

    // 选中销售订单
    selectOrder(val) {
      this.selectOrderList = [];
      this.formData.order_code = '';
      if (!S.isEmptyObject(val)) {
        this.selectPurchaseList.push(val);
        this.formData.order_code = val.code;
        this.selectWarehouseList = [
          {
            code: val.warehouse_info && val.warehouse_info.warehouse_code,
            name: val.warehouse_info && val.warehouse_info.warehouse_name,
          },
        ];
        this.formData.warehouse_code = val.warehouse_info.warehouse_code;
        // 获取产品信息
        this.getErpOrderProductDetail(val.id, val.code);
      }
      this.$refs.purchaseRef.validateField('order_code');
      this.$refs.purchaseRef.validateField('warehouse_code');
      this.$forceUpdate();
    },
    typeChange(val) {
      console.log('-> val', val);
    },

    // type切换，删除其他类型带出来的数据
    clearPoppverInfo() {
      this.formData.purchase_code = '';
      this.formData.warehouse_code = '';
      this.selectPurchaseList = [];
      this.formData.relate_code = '';
      this.selectOrderList = [];
    },

    // 添加商品
    addProductE() {
      if (this.formData && this.formData.warehouse_code == '') {
        this.$Message.error('请先选择仓库');
        return;
      }
      this.editProductVisible = true;
    },
    // 删除商品
    deleteProduct(index) {
      this.product_list.splice(index, 1);
    },
    // 获取勾选的商品
    selectedList(list) {
      this.product_list = [];
      list &&
        list.forEach((item, index) => {
          this.$set(this.product_list, index, {
            ...item,
            quantity: item.quantity || null,
            price: item.price || null,
            total_price: item.total_price || 0,
            total_quantity: item.stock || 0,
            p_qty: 0,
            stock: item.stock,
          });
        });
      this.editProductVisible = true;
    },
    // 创建
    confirmHandle() {
      this.$refs['purchaseRef'].validate(valid => {
        if (valid) {
          // 商品计算未完成,不允许提交
          if (this.validProductCalc()) {
            return;
          }

          this.erpOutboundCreate();
        } else {
          this.$Message.error('请填写完整');
        }
      });
    },

    // 选中仓库
    selectWarehouse(val) {
      console.log('🚀 ~ file: EditProductOut.vue ~ line 341 ~ selectWarehouse ~ val', val);
      this.selectWarehouseList = [];
      this.formData.warehouse_code = '';
      if (!S.isEmptyObject(val)) {
        this.selectWarehouseList.push(val);
        this.formData.warehouse_code = val.code;
      }
      this.$forceUpdate();
      this.$refs.purchaseRef.validateField('warehouse_code');
    },

    supplierChange(val) {
      console.log('val', val);
    },

    // 处理商品的价格数据，作为参数
    handlerProductDetails() {
      let detail = [];
      this.product_list &&
        this.product_list.forEach(item => {
          detail.push({
            product_id: item.id,
            product_code: item.code,
            quantity: item.quantity,
            batch_code: item.batch_code,
            note: item.note,
          });
        });
      return detail || [];
    },

    // api - 获取采购退货单明细
    getErpPurchasereturnDetail(id, code) {
      let params = {
        id,
        code,
      };
      this.$api
        .getErpPurchasereturnDetail(params)
        .then(res => {
          this.product_list = this.handlerDetail(res.list, res.products);
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
        });
    },

    // api - 获取销售订单明细
    getErpOrderProductDetail(id, code) {
      let params = {
        id,
        code,
      };
      this.$api
        .getErpOrderProductDetail(params)
        .then(res => {
          this.product_list = this.handlerDetail(res.list, res.products);
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
        });
    },

    handlerDetail(list, products) {
      let productDetailList = [];
      list &&
        list.forEach(item => {
          productDetailList.push({
            ...products[item.product_id],
            total_quantity: item.quantity,
            stock: item.stock,
            surplus_quantity: $operator.subtract(Number(item.quantity), Number(item.p_qty)),
            r_qty: item.r_qty,
            //剩余发货数量回显  剩余发货数量大于库存数量 显示仓库数量
            quantity:
              $operator.subtract(Number(item.quantity), Number(item.p_qty)) > +item.stock
                ? +item.stock
                : $operator.subtract(Number(item.quantity), Number(item.p_qty)),
            price: null,
            original_price: item.price, // 原价
            total_price: 0,
            p_qty: item.p_qty,
          });
        });
      console.log('🚀 ~ file: EditProductOut.vue ~ line 416 ~ handlerDetail ~ productDetailList', productDetailList);
      return productDetailList || [];
    },

    // api - 创建出库单
    erpOutboundCreate() {
      this.submitLoading = true;
      let params = {
        type: this.formData.type,
        code: this.formData.code,
        warehouse_code: this.formData.warehouse_code,
        outbound_date: this.formData.outbound_date,
        remark: this.formData.remark,
        relate_code:
          this.formData.type == '10'
            ? this.formData.purchase_code
            : this.formData.type == '20'
            ? this.formData.order_code
            : '',
        detail: JSON.stringify(this.handlerProductDetails()),
        express_detail: this.formData.express_detail,
      };
      this.$api
        .erpOutboundCreate(params)
        .then(res => {
          // 存在售后情况，拦截出库单
          if (res.error_type == 1) {
            this.errorTableVisible = true;
            this.fail_list = res.fail_data;
          } else {
            this.$Message.success('创建成功');
            this.$emit('refresh');
          }
          this.cancelHandle();
          this.submitLoading = false;
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
          this.submitLoading = false;
        });
    },

    getErpOutboundExpress() {
      this.$api.getErpOutboundExpress({ is_all: '1' }).then(res => {
        this.expressCompanyList = res.express;
      });
    },

    // 关闭弹窗,清除数据
    cancelHandle() {
      this.formData = this.$lodash.cloneDeep(initFormData);
      this.$refs.purchaseRef.resetFields();
      this.product_list = [];
      this.productOutType = 'express';
      this.$emit('update:visible', false);
      this.$emit('changeCreate', false);
    },

    // 采购退货详情创建时,获取采购仓库
    getPurchaseWareHouse(code) {
      let params = { code };
      this.$api.getErpPurchasereturnInfo(params).then(res => {
        this.selectWarehouseList = [
          {
            code: res.warehouse.code,
            name: res.warehouse.name,
          },
        ];
        this.formData.warehouse_code = res.warehouse.code;
      });
    },

    // 销售订单详情创建时,获取仓库
    getRefundWareHouse(code) {
      let params = { code };
      this.$api.getErpOrderInfo(params).then(res => {
        this.selectWarehouseList = [
          {
            code: res.warehouse.code,
            name: res.warehouse.name,
          },
        ];
        this.formData.warehouse_code = res.warehouse.code;
      });
    },

    // 详情入库单创建
    createDetailEnter() {
      this.formData.type = this.type;
      // 采购订单入库创建
      if (this.type === '10') {
        this.selectPurchaseList = [];
        this.selectPurchaseList.push({
          code: this.code,
        });
        this.formData.purchase_code = this.code;
        this.getPurchaseWareHouse(this.code);
        this.getErpPurchasereturnDetail('', this.code);
      } else if (this.type === '20') {
        console.log('=>(EditProductOut.vue:585) this.code', this.code);

        this.selectOrderList = [];
        this.selectOrderList.push({
          code: this.code,
        });
        this.formData.order_code = this.code;
        this.getRefundWareHouse(this.code);
        this.getErpOrderProductDetail('', this.code);
      }
    },

    // 出库方式
    changeProductOutType(val) {
      this.formData.express_detail = [{ express_code: '', express_no: '' }];
      if (val === 'ZTCK') {
        this.formData.express_detail = [{ express_code: val, express_no: '' }];
      }
    },
  },
  destroyed() {},
};
</script>

<style scoped lang="less">
@import url('../../common/modal.less');
</style>
