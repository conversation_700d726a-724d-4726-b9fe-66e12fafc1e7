<template>
  <div class="purchase-list-wrapper">
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <div class="no-wrap">
          <FormItem>
            <GoodsSearch v-model="queryFormData.product_code" :onSearch="onSearch"></GoodsSearch>
          </FormItem>
        <FormItem>
          <Input v-model="queryFormData.code" placeholder="请输入出库单号" clearable />
        </FormItem>
        <FormItem>
          <Select v-model="queryFormData.type" placeholder="请选择出库类型" clearable>
            <Option v-for="item in prodTypeDesc" :value="item.id" :key="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <Select v-model="queryFormData.warehouse_code" placeholder="请选择仓库" clearable>
            <Option v-for="item in warehouseList" :value="item.code" :key="item.code">{{ item.name }}</Option>
          </Select>
        </FormItem>
        <FormItem style="text-align: left">
          <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
          <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
        </FormItem>
      </div>
      <FormItem>
        <Button type="primary" class="mr10" @click="createProductEnter">创建出库单</Button>
        <Button type="primary" class="mr10" @click="batchCreateProductEnter">批量创建出库单</Button>
        <!--          <Button type="primary" class="mr10" @click="batchCreateProductEnter">导出待出库订单列表</Button>-->
        <!--          <Button type="primary" class="mr10" @click="batchImportOutOrder">批量导入总部仓出库单</Button>-->
        <Button type="primary" class="mr10" @click="exportOrder">导出待出库订单列表</Button>
        <Button type="primary" class="mr10" @click="batchImportOutOrder">批量导入总部仓出库单</Button>
      </FormItem>
    </Form>

    <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 290">
      <!-- 关联单号 -->
      <template slot-scope="{ row }" slot="relate_code">
        <KLink
          v-if="row.type == 10"
          :to="{ path: '/erp/purchase_return/detail', query: { code: row.relate_code } }"
          target="_blank"
          >{{ row.relate_code }}</KLink
        >
        <KLink
          v-else-if="row.type == 20"
          :to="{ path: '/erp/sale_order/detail', query: { code: row.relate_code } }"
          target="_blank"
          >{{ row.relate_code }}</KLink
        >
        <span v-else>{{ row.relate_code || '-' }}</span>
      </template>
      <template slot-scope="{ row }" slot="actual_time">
        {{ row.actual_time | date_format }}
      </template>
      <template slot-scope="{ row }" slot="remark">
        {{ row.remark || '-' }}
      </template>

      <template slot-scope="{row}" slot="push_status_text">
        <span v-if="row.push_status === '4'">{{ row.push_status_text }}
          <Tooltip placement="bottom" max-width="200" :content="row.hangup_text">
            <Icon type="md-help-circle" style="font-size: 16px;" class="cursor"/>
          </Tooltip>
        </span>
        <span v-else>{{ row.push_status_text }}</span>
      </template>

      <template slot-scope="{ row }" slot="create_time">
        {{ row.create_time | date_format }}
      </template>
      <template slot-scope="{ row }" slot="update_time">
        {{ row.update_time | date_format }}
      </template>

      <template slot-scope="{ row }" slot="action">
        <a @click="goDetail(row)">详情</a>
      </template>
    </Table>

    <div class="block_20"></div>
    <KPage
      :total="total"
      :page-size="+queryFormData.pageSize"
      :current="+queryFormData.page"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
      style="text-align: center"
    />
    <EditProductOut :id="productId" :visible.sync="editVisible" @refresh="refresh"></EditProductOut>
    <BatchEditProductOut :visible.sync="batchEditVisible" @refresh="refresh"></BatchEditProductOut>
    <KBatchUpload
      :import-visible.sync="batchImportVisible"
      :excelKeyMap="excelKeyMap"
      :downloadApiName="downloadApiName"
      :reportColumns="reportColumns"
      :chunkNum="20"
      :importApiName="importApiName"
      @freshList="onSearch"
    ></KBatchUpload>
    <ExportOrderModal :export-visible.sync="exportOrderVisible" :warehouse-list="warehouseList"></ExportOrderModal>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'utils/util';
import EditProductOut from './compontents/EditProductOut.vue';
import BatchEditProductOut from './compontents/BatchEditProductOut';
import KBatchUpload from '@/components/BatchUpload/KBatchUpload';
import ExportOrderModal from './compontents/ExportOrderModal';
import GoodsSearch from "@/components/GoodsSearch/index.vue";

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  code: '', // 名称
  r: '',
  product_code: ''
};

export default {
  name: 'list',
  mixins: [search],
  components: { EditProductOut, BatchEditProductOut, KBatchUpload, ExportOrderModal, GoodsSearch },
  data() {
    return {
      apiName: 'getErpOutboundList',
      queryFormData: {
        ...init_query_form_data
      },
      tableCols: [
        { title: '出库类型', key: 'type_text', align: 'center' },
        { title: '出库单号', key: 'code', align: 'center' },
        { title: '关联单号', slot: 'relate_code', align: 'center' },
        { title: '出库仓库', key: 'warehouse_name', align: 'center' },
        { title: '实际出库时间', slot: 'actual_time', align: 'center' },
        { title: '备注', slot: 'remark', align: 'center' },
        { title: '库存状态', key: 'final_out_status_text' },
        { title: '推送状态', slot: 'push_status_text' },
        { title: '创建人', key: 'operator', align: 'center' },
        { title: '创建时间', slot: 'create_time', align: 'center' },
        { title: '更新时间', slot: 'update_time', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' }
      ],
      list_count: {},

      productId: '', // 采购订单id
      editVisible: false, // 新建/编辑采购订单弹窗
      batchEditVisible: false, // 新建/编辑采购订单弹窗
      prodTypeDesc: [],
      warehouseList: [],
      exportOrderVisible: false, // 导出待出库订单列表
      // 批量导入总部仓出库单 组件所需参数
      batchImportVisible: false, //批量导入
      downloadApiName: 'downloadOrderTemplate', // 导出模板API
      importApiName: 'importOutstock', // 导入模板API
      // validateApiName: 'validateProOutOrder',// 导入模板API
      excelKeyMap: {
        采购单号: { key: 'purchase_code' },
        销售单编号: { key: 'order_code', required: true },
        '客户姓名-电话': { key: 'userinfo' },
        带出库产品明细: { key: 'detail' },
        发货仓库: { key: 'warehouse_name' },
        '销售单平台（类型）': { key: 'outbound_date' },
        出库单1物流信息: { key: 'out_express_1', required: true },
        出库单1发货明细: { key: 'out_item_1' },
        出库单2物流信息: { key: 'out_express_2' },
        出库单2发货明细: { key: 'out_item_2' },
        出库单3物流信息: { key: 'out_express_3' },
        出库单3发货明细: { key: 'out_item_3' }
      },
      reportColumns: [
        { type: 'index', title: '序号', width: 60, align: 'center' },
        { title: '销售订单编号', key: 'order_code', align: 'center' },
        { title: '失败原因', key: 'fail_msg', align: 'center' }
      ]
    };
  },
  computed: {},
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.getErpOutboundOptions();
  },
  mounted() {
    this.getErpWarehouseList();
  },
  methods: {
    //导入excel
    excelUpload(excelList) {
      console.log('-> %c excelList  === %o', 'font-size: 15px;color: green;', excelList);
    },
    exportOrder() {
      this.exportOrderVisible = true;
    },
    batchImportOutOrder() {
      this.batchImportVisible = true;
    },
    batchCreateProductEnter() {
      this.productId = '';
      this.batchEditVisible = true;
    },
    // 新建采购订单
    createProductEnter() {
      this.productId = '';
      this.editVisible = true;
    },

    refresh() {
      this.loadList();
    },

    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    goDetail(row) {
      this.$router.push({
        path: '/erp/product-out/detail',
        query: {
          id: row.id
        }
      });
    },
    getErpWarehouseList() {
      this.$api.getErpWarehouseList({pageSize:999}).then(res => {
        this.warehouseList = res.list;
      });
    },

    getErpOutboundOptions() {
      this.$api.getErpOutboundOptions().then(res => {
        this.prodTypeDesc = S.descToArrHandle(res.prodTypeDesc);
      });
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  }
};
</script>

<style scoped lang="less">
.supplier-list-wrapper {
}
</style>
