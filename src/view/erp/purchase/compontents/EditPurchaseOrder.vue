<template>
  <div>
    <Modal
      :value="visible"
      :title="orderId && isModification ? '修改采购订单' : orderId ? '编辑采购订单' : '新建采购订单'"
      width="900px"
      :mask-closable="false"
      @on-cancel="cancelHandle"
    >
      <Form
        label-position="top"
        class="common-modal-form"
        :model="formData"
        ref="purchaseRef"
        :rules="formDataValidateRules"
      >
        <div class="section-header">
          <div class="section-mark"></div>
          <div class="section-title">基本信息</div>
        </div>

        <div class="create-section">
          <FormItem label="采购单编号" prop="code" class="common-form-item">
            <Input v-model="formData.code" placeholder="不填系统将自动生成" maxlength="20" :disabled="isModification" />
          </FormItem>

          <FormItem label="采购日期" prop="plan_date" class="common-form-item">
            <DatePicker
              ref="purchase-time"
              type="date"
              :value="formData.plan_date"
              style="width: 100%"
              placeholder="请输入采购日期"
              :disabled="isModification"
              @on-change="date => (formData.plan_date = date)"
            />
          </FormItem>

          <FormItem label="供应商" prop="supplier_code" class="common-form-item">
            <SelectPopper
              ref="selectSupplier"
              class="flex"
              :code="formData.supplier_code"
              @selectSup="selectSup"
              :isDetail="isModification || isOneClickPurchase"
            >
              <el-select
                v-model="formData.supplier_code"
                :multiple="false"
                :multiple-limit="1"
                style="width: 100%"
                size="small"
                @visible-change="selectChange($event, 'selectSupplier')"
                popper-class="rxj-pop-select"
                @on-change="supplierChange"
                :disabled="isModification || isOneClickPurchase"
              >
                <el-option
                  v-for="(item, index) in selectSupplierList"
                  :value="item.code"
                  :key="index + item.code"
                  :label="item.name"
                />
              </el-select>
            </SelectPopper>
          </FormItem>

          <FormItem label="仓库名称" prop="warehouse_code" class="common-form-item">
            <SelectWarehousePopper
              ref="selectWarehouse"
              class="flex"
              :code="formData.warehouse_code"
              :isDetail="isModification || isOneClickPurchase"
              @selectSup="selectWarehouse"
            >
              <el-select
                v-model="formData.warehouse_code"
                :multiple="false"
                :multiple-limit="1"
                style="width: 100%"
                size="small"
                :disabled="isModification || isOneClickPurchase"
                @visible-change="selectChange($event, 'selectWarehouse')"
                popper-class="rxj-pop-select"
                @on-change="supplierChange"
              >
                <el-option
                  v-for="(item, index) in selectWarehouseList"
                  :value="item.code"
                  :key="index + item.code"
                  :label="item.name"
                />
              </el-select>
            </SelectWarehousePopper>
          </FormItem>

          <FormItem label="合同编号" class="common-form-item" prop="contract_code">
            <Input
              v-model="formData.contract_code"
              placeholder="请输入合同编号"
              :disabled="isModification || isOneClickPurchase"
            />
          </FormItem>
          <FormItem label="备注" class="common-form-item">
            <Input
              v-model="formData.remark"
              type="textarea"
              maxlength="50"
              show-word-limit
              :autosize="{ maxRows: 2, minRows: 2 }"
            />
          </FormItem>
        </div>

        <div class="section-header mt10">
          <div class="section-mark"></div>
          <div class="section-title">产品信息</div>
        </div>

        <div v-show="!isModification && !isOneClickPurchase" class="flex flex-item-end">
          <SelectProductPopper
            v-if="formData.supplier_code"
            ref="selectProduct"
            class="flex"
            :supplier_code="formData.supplier_code"
            @selectedList="selectedList"
          >
            <Button type="primary" @click="addProductE">添加商品</Button>
          </SelectProductPopper>

          <Button type="primary" v-else @click="addProductE">添加商品</Button>
        </div>

        <Table class="mt10" :loading="infoLoading" :columns="product_tableCols" :data="product_list">
          <!-- 产品规格 -->
          <template slot="spec" slot-scope="{ row, index }">
            {{ row.spec || '-' }}
          </template>

          <!-- 采购数量 -->
          <template slot="quantity" slot-scope="{ row, index }">
            <InputNumber
              :ref="'quantity' + index"
              v-model="product_list[index].quantity"
              style="width: 100%"
              :active-change="false"
              :min="0"
              :precision="0"
              :disabled="(isModification && !row.wait_qty) || isOneClickPurchase || !!formData.order_code"
              placeholder="请输入采购数量"
              @on-blur="removeZero('quantity', index)"
              @on-change="quantityChange(index)"
            />
          </template>

          <!-- 采购单价 -->
          <template slot="price" slot-scope="{ row, index }">
            <InputNumber
              :ref="'price' + index"
              :disabled="true"
              style="width: 100%"
              :precision="4"
              :active-change="false"
              v-model="product_list[index].price"
              :min="0"
              placeholder="请输入采购单价"
              @on-blur="removeZero('price', index)"
            />
          </template>

          <!-- 含税金额 -->
          <template slot="amount" slot-scope="{ row, index }">
            <InputNumber
              :ref="'amount' + index"
              v-model="product_list[index].amount"
              style="width: 100%"
              :min="0"
              :precision="2"
              :active-change="false"
              :disabled="isModification && !row.wait_qty"
              placeholder="请输入"
              @on-blur="purchaseBlur(index)"
            />
          </template>

          <!-- 税率 -->
          <template slot="tax_rate" slot-scope="{ row, index }">
            <Select
              v-model="product_list[index].tax_rate"
              style="width: 80px"
              :disabled="isModification"
              @on-change="rateChange($event, index)"
            >
              <Option v-for="item in rate_enum" :key="item.kw" :value="item.kw">{{ item.desc }}</Option>
            </Select>
          </template>

          <!-- 不含税金额 -->
          <template slot="amount_without_tax" slot-scope="{ row, index }">
            <InputNumber
              :ref="'amount_without_tax' + index"
              style="width: 100%"
              :min="0"
              :precision="2"
              :disabled="isModification && !row.wait_qty"
              v-model="product_list[index].amount_without_tax"
              :active-change="false"
              placeholder="请输入"
            />
          </template>

          <template slot="action" slot-scope="{ row, index }">
            <a @click="deleteProduct(index)" :disabled="isModification || isOneClickPurchase">删除</a>
          </template>
        </Table>

        <div class="mt10 flex flex-item-end">
          <span class="custom-label mr10">已选中产品：{{ product_list.length }}</span>
          <span class="custom-label">总金额：{{ custom_total_price }} 元</span>
        </div>
        <div class="section-header mt10">
          <div class="section-mark"></div>
          <div class="section-title">发货信息</div>
        </div>
        <div class="create-section">
          <FormItem label="快递公司" class="common-form-item" prop="express_code" :rules="customRules.express_code">
            <Select v-model="formData.express_code" placeholder="请选择快递公司" :disabled="isModification">
              <Option v-for="item in expressList" :key="item.code" :value="item.code">{{ item.name }}</Option>
            </Select>
          </FormItem>

          <FormItem label="快递单号" class="common-form-item" prop="express_no" :rules="customRules.express_no">
            <Input v-model="formData.express_no" placeholder="请输入快递单号" :disabled="isModification"></Input>
          </FormItem>

          <FormItem label="客户姓名" class="common-form-item" prop="sender_name" :rules="customRules.sender_name">
            <Input v-model="formData.sender_name" placeholder="请输入收件人姓名" :disabled="isModification"></Input>
          </FormItem>

          <FormItem label="手机号" class="common-form-item" prop="sender_mobile" :rules="customRules.sender_mobile">
            <Input v-model="formData.sender_mobile" placeholder="请输入收件人手机号" :disabled="isModification" />
          </FormItem>
          <div class="flex" style="width: 100%">
            <FormItem
              :rules="customRules.sender_county"
              label="客户地址"
              prop="sender_county"
              class="common-form-item"
              style="width: 300px"
            >
              <div class="addWrap">
                <div class="addressBox">
                  <!--								<v-region v-model="selectedAddress"-->
                  <!--								          @values="regionChange" :disabled="!$route.query.isEdit &&!!$route.query.id"></v-region>-->
                  <el-cascader
                    v-model="selectedAddress"
                    :options="regionData"
                    placeholder="请选择联系地址"
                    size="small"
                    clearable
                    :disabled="isModification"
                    popper-class="address-com"
                    style="width: 300px"
                    @change="regionChange"
                  />
                </div>
              </div>
            </FormItem>

            <div class="addressInput ml10" style="width: 100%">
              <FormItem
                label="详细地址"
                prop="sender_address"
                :rules="customRules.sender_address"
                class="common-form-item"
                style="width: 100%"
              >
                <Input v-model="formData.sender_address" placeholder="详细地址" :disabled="isModification" />
              </FormItem>
            </div>
          </div>
        </div>
      </Form>

      <div slot="footer">
        <Button @click="cancelHandle">取消</Button>
        <Button type="primary" :loading="submitLoading" @click="confirmHandle">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import S from 'utils/util';
import { $operator } from '@/utils/operation';
import SelectPopper from '@/components/select-popper/select-popper';
import SelectWarehousePopper from '@/components/select-warehouse-popper/select-warehouse-popper';
import SelectProductPopper from '@/components/select-product-popper/select-product-popper';
import { CodeToText, regionData, TextToCode } from '@/utils/chinaMap';

const initFormData = {
  code: '', //采购单编号
  plan_date: '', //采购日期
  supplier_code: '', //供应商编号
  warehouse_code: '', //仓库编号
  remark: '', //备注
  sender_province: '', //发件人省
  sender_city: '', //发件人市
  sender_county: '', //发件人区
  sender_address: '', //发件人详细地址
  sender_name: '', //发件人姓名
  sender_mobile: '', //发件人手机号
  express_code: '', //快递公司
  express_no: '', //快递单号
  contract_code: '', //合同编号
};

export default {
  name: 'list',
  mixins: [],
  components: { SelectPopper, SelectWarehousePopper, SelectProductPopper },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    orderId: {
      type: String,
      default: '',
    },
    isModification: {
      type: Boolean,
      default: false,
    },
    // 是否是销售单一键采购
    isOneClickPurchase: {
      type: Boolean,
      default: false,
    },
    // 销售订单编号
    saleOrderCode: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      formData: { ...initFormData },
      formDataValidateRules: {
        supplier_code: [{ required: true, message: '请输入供应商名称', trigger: 'blur,change' }],
        warehouse_code: [{ required: true, message: '请选择仓库', trigger: 'blur,change' }],
        contract_code: [{ required: true, message: '请输入写合同编号', trigger: 'change' }],
      },
      submitLoading: false, // 弹窗确定的loading
      selectSupplierList: [], // 选中的供应商
      selectWarehouseList: [], // 选中的仓库
      regionData, // 地址数据
      infoLoading: false, // 产品信息表格的loading
      product_tableCols: [
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', key: 'unit', align: 'center' },
        { title: '采购数量', slot: 'quantity', align: 'center' },
        { title: '采购单价', slot: 'price', align: 'center' },
        { title: '含税金额', slot: 'amount', align: 'center' },
        { title: '税率', slot: 'tax_rate', align: 'center', width: '100' },
        { title: '不含税金额', slot: 'amount_without_tax', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' },
      ],
      customRules: {
        express_code: [{ required: false, message: '请选择物流公司', trigger: 'change' }],
        express_no: [{ required: false, message: '请输入快递单号', trigger: 'change' }],
        sender_name: [{ required: false, message: '请输入客户姓名', trigger: 'change' }],
        sender_mobile: [
          { required: false, message: '请输入客户手机号', trigger: 'change', pattern: /^(?:(?:\+|00)86)?1[3-9]\d{9}$/ },
        ],
        sender_county: [{ required: false, message: '请选择收货地址', trigger: 'change' }],
        sender_address: [{ required: false, message: '请选择收货地址', trigger: 'change' }],
      },
      product_list: [], // 商品数据
      expressList: [], // 物流公司列表
      // 税率枚举
      rate_enum: [],
      selectedAddress: [], // 地址选择
      isJd: false, // 是否是京东
    };
  },

  computed: {
    // 总金额
    custom_total_price() {
      let price = 0;
      this.product_list.forEach(item => {
        price = $operator.add(Number(price), Number(item.amount));
      });
      return price || 0;
    },
    getExpressList() {
      if (this.isJd) {
        return this.expressList.filter(item => item.code === 'JD');
      } else {
        return this.expressList;
      }
    },
  },

  watch: {
    visible: {
      immediate: true,
      handler(val) {
        if (val && this.orderId) {
          this.getErpPurchaseInfo(this.orderId);
        }
        if (val && this.isOneClickPurchase) {
          this.getOneKeyPurchaseInfo();
          // 一键采购发货信息必填
          this.dynamicValidate(true);
        }
        // 关闭弹框需重置用户信息必填
        if (!val) {
          this.dynamicValidate(false);
        }
      },
    },
    'formData.supplier_code': {
      immediate: true,
      deep: true,
      handler(val) {
        if (!val) {
          this.product_list = [];
        }
      },
    },
  },

  created() {
    // 获取枚举值
    this.getErpInboundOptions();
  },

  mounted() {
    this.getErpOutboundExpress();
  },

  methods: {
    selectChange(e, node) {
      // poppver的ref
      let nodeList = ['selectSupplier', 'selectWarehouse', 'selectProduct'];
      // select date 的ref
      let selectNodeList = ['purchase-time'];
      if (e) {
        let hideList = nodeList.filter(item => item !== node);
        hideList.forEach(item => {
          this.$refs[item] && (this.$refs[item].showPop = false);
        });
        selectNodeList.forEach(item => {
          this.$refs[item] && (this.$refs[item].visible = false);
        });
      }
    },
    /**
     * @description:计算采购单价，计算公式：含税金额 / 采购数量 ，支持小数点后四位展示；
     * @param { number } index 商品索引
     */
    calcPurchasePrice(index) {
      // 含税金额
      let amount = this.product_list[index].amount;
      // 采购数量
      let quantity = this.product_list[index].quantity;
      let price = 0;
      if (Number(quantity) !== 0) {
        price = $operator.divide(Number(amount), Number(quantity), 4);
      }
      this.product_list[index].price = price;
    },
    // 采购数量发生变化时
    quantityChange(index) {
      // 计算采购单价
      this.calcPurchasePrice(index);
    },
    // 含税金额失焦
    purchaseBlur(index) {
      let _purchase_price = this.product_list[index].amount;
      let _rate = this.product_list[index].tax_rate;

      // 当含税金额和税率均有值时，才可以计算不含税金额的数据
      if (_purchase_price && _rate) {
        this.rateChange('', index);
      }
      // 计算采购单价
      this.calcPurchasePrice(index);
    },
    // 税率变更
    rateChange(e, index) {
      let _purchase_price = this.product_list[index].amount;
      let _rate = Number(this.product_list[index].tax_rate);
      if (_purchase_price && (_rate || _rate == 0)) {
        this.product_list[index].amount_without_tax = $operator.divide(Number(_purchase_price), Number(1 + _rate));
      }
    },
    // 去零
    removeZero(key, index) {
      let currentValue = this.$refs[key + index].currentValue;
      if (Number(currentValue) == 0) {
        this.product_list[index].key = null;
        this.$refs[key + index].currentValue = null;
      }
    },
    // 计算合计,合计已经用不含税金额替换了--drop
    // calcPrice (index) {
    //   let _quantity = this.product_list[index].quantity
    //   let _price = this.product_list[index].price
    //   if (_quantity && _price ) {
    //     this.product_list[index].total_price = $operator.multiply(Number(_price), Number(_quantity))
    //   }else{
    //     this.product_list[index].total_price = 0
    //   }
    // },

    // 校验产品是否存在数量，单价，折扣未填写的情况
    validProductCalc() {
      let isUnValid = false;
      if (!this.product_list.length) {
        this.$Message.error('商品不可为空');
        isUnValid = true;
        return true;
      }
      this.product_list &&
        this.product_list.some((item, index, currentItem) => {
          if (!item.quantity) {
            this.$Message.error(`请输入【${item.name}】采购数量`);
            isUnValid = true;
            return true;
          }
          if (!item.amount) {
            this.$Message.error(`请【${item.name}】含税金额`);
            isUnValid = true;
            return true;
          }
          if (!item.tax_rate) {
            this.$Message.error(`请选择【${item.name}】税率`);
            isUnValid = true;
            return true;
          }
          if (!item.amount_without_tax) {
            this.$Message.error(`请输入【${item.name}】不含税金额`);
            isUnValid = true;
            return true;
          }
          // 一品同价进行校验
          let flag = currentItem.slice(index + 1).some(other => item.id === other.id && item.price === other.price);
          if (flag) {
            this.$Message.error(`【${item.name}】同价只允许提交一条`);
            isUnValid = true;
            return true;
          }
          if (this.isModification) {
            if (item.quantity < item.already_qty) {
              this.$Message.error(`【${item.name}】采购数量不能小于已入库产品之和`);
              isUnValid = true;
              return true;
            }

            if (item.already_amount < item.already_amount) {
              this.$Message.error(`【${item.name}】采购含税金额不能小于已入库产品之和`);
              isUnValid = true;
              return true;
            }
          }
        });
      return isUnValid;
    },

    // 添加商品
    addProductE() {
      if (!this.formData.supplier_code) {
        this.$Message.error('请先选择供应商');
        return;
      }
      this.selectChange(true);
    },
    // 删除商品
    deleteProduct(index) {
      this.product_list.splice(index, 1);
    },
    // 获取勾选的商品(支持一品多项)
    selectedList(list) {
      list &&
        list.forEach(item => {
          this.product_list.push({
            ...item,
            quantity: +item.quantity || null,
            price: +item.price || null,
            amount: +item.amount || null, // 含税金额
            tax_rate: item.tax_rate || null, // 税率
            amount_without_tax: +item.amount_without_tax || null, // 不含税金额
          });
        });
    },
    // 创建
    confirmHandle() {
      console.log(this.formData);
      this.$refs['purchaseRef'].validate(valid => {
        if (valid) {
          // 商品计算未完成,不允许提交
          if (this.validProductCalc()) {
            return;
          }
          this.orderId && this.isModification
            ? this.purchaseModification()
            : this.orderId
            ? this.purchaseEdit()
            : this.purchaseCreate();
        } else {
          this.$Message.error('请填写完整');
        }
      });
    },

    // 选中供应商
    selectSup(val) {
      this.selectSupplierList = [];
      this.formData.supplier_code = '';
      if (!S.isEmptyObject(val)) {
        this.selectSupplierList.push(val);
        this.formData.supplier_code = val.code;
      }
      this.$forceUpdate();
      this.$refs.purchaseRef.validateField('supplier_code');
      // 每次切换供应商,清除产品
      this.product_list = [];
    },

    // 选中仓库
    selectWarehouse(val) {
      console.log('-> %c val  === %o', 'font-size: 15px;color: green;', val);
      this.selectWarehouseList = [];
      this.formData.warehouse_code = '';
      if (!S.isEmptyObject(val)) {
        this.selectWarehouseList.push(val);
        this.formData.warehouse_code = val.code;
        this.dynamicValidate(val.is_jd === '1');
        if (val.is_jd !== '1') {
          this.resetValidate();
        }
      }
      this.$forceUpdate();
      this.$refs.purchaseRef.validateField('warehouse_code');
    },
    dynamicValidate(isRequired) {
      this.isJd = isRequired;
      for (const rule in this.customRules) {
        const ruleInfo = this.customRules[rule];
        this.$set(this.customRules, rule, [{ ...ruleInfo[0], required: isRequired }]);
      }
    },
    resetValidate() {
      const propKeys = Object.keys(this.customRules);
      this.$nextTick(() => {
        propKeys.forEach(item => {
          this.$refs['purchaseRef'].validateField(item);
        });
      });
    },
    supplierChange(val) {},

    // 处理商品的价格数据，作为参数
    handlerProductDetails() {
      let detail = [];
      this.product_list &&
        this.product_list.forEach(item => {
          detail.push({
            product_id: item.id,
            quantity: item.quantity,
            price: item.price,
            amount: item.amount,
            tax_rate: item.tax_rate,
            amount_without_tax: item.amount_without_tax,
          });
        });
      return detail || [];
    },

    // 处理商品的价格数据(修改)
    handlerModificationProductDetails() {
      let detail = [];
      this.product_list &&
        this.product_list.forEach(item => {
          detail.push({
            id: item.list_id,
            product_id: item.id,
            quantity: item.quantity,
            price: item.price,
            amount: item.amount,
            tax_rate: item.tax_rate,
            amount_without_tax: item.amount_without_tax,
          });
        });
      console.log('=>(EditPurchaseOrder.vue:675) detail', detail);
      return detail || [];
    },

    // api - 创建采购订单
    purchaseCreate() {
      this.submitLoading = true;
      let params = {
        ...this.formData,
        order_code: this.isOneClickPurchase ? this.saleOrderCode : '', // 销售单（渠道商产品）一键采购需传销售订单号
        detail: JSON.stringify(this.handlerProductDetails()),
      };
      this.$api
        .purchaseCreate(params)
        .then(res => {
          this.$Message.success('创建成功');
          this.cancelHandle();
          this.$emit('refresh');
          this.submitLoading = false;
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
          this.submitLoading = false;
        });
    },

    // api - 编辑采购订单
    purchaseEdit() {
      this.submitLoading = true;
      let params = {
        id: this.orderId,
        ...this.formData,
        detail: JSON.stringify(this.handlerProductDetails()),
      };
      this.$api
        .purchaseEdit(params)
        .then(res => {
          this.$Message.success('编辑成功');
          this.cancelHandle();
          this.$emit('refresh');
          this.submitLoading = false;
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
          this.submitLoading = false;
        });
    },
    // api - 修改采购订单
    purchaseModification() {
      this.submitLoading = true;
      let params = {
        id: this.orderId,
        remark: this.formData.remark,
        detail: JSON.stringify(this.handlerModificationProductDetails()),
      };
      console.log('=>(EditPurchaseOrder.vue:729) params', params);
      this.$api
        .purchaseModification(params)
        .then(res => {
          this.$Message.success('修改成功');
          this.cancelHandle();
          this.$emit('refresh');
          this.submitLoading = false;
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
          this.submitLoading = false;
        });
    },

    // 关闭弹窗,清除数据
    cancelHandle() {
      this.formData = { ...initFormData };
      this.selectedAddress = [];
      console.log('-> %c this.formData  === %o', 'font-size: 15px;color: green;', this.formData);
      this.$emit('update:visible', false);
      this.$refs.purchaseRef.resetFields();
      this.product_list = [];
    },

    // 获取采购单详情
    getErpPurchaseInfo(id) {
      let params = {
        id,
      };
      this.$api.getErpPurchaseInfo(params).then(res => {
        this.formData.code = res.code;
        this.formData.plan_date = res.plan_date;
        this.formData.remark = res.remark;

        // 回显供应商poppver
        this.formData.supplier_code = res.supplier_code;
        this.selectSupplierList.push({
          code: res.supplier && res.supplier.code,
          name: res.supplier && res.supplier.name,
        });

        // 回显仓库poppver
        this.formData.warehouse_code = res.warehouse_code;
        this.selectWarehouseList.push({
          code: res.warehouse && res.warehouse.code,
          name: res.warehouse && res.warehouse.name,
        });
        this.formData.sender_name = res.sender_name;
        this.formData.contract_code = res.contract_code;
        this.formData.sender_mobile = res.sender_mobile;
        this.formData.sender_address = res.sender_address;
        this.formData.sender_province = res.sender_province;
        this.formData.sender_city = res.sender_city;
        this.formData.sender_county = res.sender_county;
        this.formData.express_code = res.express_code;
        this.formData.express_no = res.express_no;
        this.formData.order_code = res.order_code;
        // 如果当前订单关联过销售单号
        if (res.order_code) {
          this.dynamicValidate(true);
        }

        if (res.sender_county) {
          const province = TextToCode[res.sender_province];
          this.selectedAddress = [
            province.code,
            province[res.sender_city].code,
            province[res.sender_city][res.sender_county].code,
          ];
        } else {
          this.selectedAddress = [];
        }
        console.log('-> %c this.selectedAddress  === %o', 'font-size: 15px;color: green;', this.selectedAddress);
        if (res.warehouse && res.warehouse.is_jd === '1') {
          this.dynamicValidate(res.warehouse.is_jd === '1');
        }
        // 回显商品
        this.getErpPurchaseDetail(res.id, res.code);
      });
    },

    // api - 获取枚举值
    getErpInboundOptions() {
      this.$api.getErpInboundOptions().then(res => {
        // 获取税率的枚举值
        this.rate_enum = S.descToArrHandle(res.prodTaxRateDesc);
      });
    },

    // api - 获取快递公司
    getErpOutboundExpress() {
      this.$api.getErpOutboundExpress().then(res => {
        console.log('-> %c res  === %o', 'font-size: 15px;color: green;', res);
        // 获取税率的枚举值
        this.expressList = res.express;
      });
    },

    // api - 获取采购单明细
    getErpPurchaseDetail(id, code) {
      let params = {
        id,
        code,
      };
      this.infoLoading = true;
      this.$api
        .getErpPurchaseDetail(params)
        .then(res => {
          this.handlerDetail(res.list, res.products);
          this.infoLoading = false;
        })
        .catch(err => {
          this.infoLoading = false;
          this.$Message.error(err.errmsg);
        });
    },

    // 处理明细数据，回显采购订单
    handlerDetail(list, products) {
      let productDetailList = [];
      list &&
        list.forEach(item => {
          productDetailList.push({
            ...products[item.product_id],
            list_id: item.id, // 区分列表id（todo 当前id位product_id）
            price: item.price,
            amount: Number(item.amount),
            amount_without_tax: Number(item.amount_without_tax),
            quantity: Number(item.quantity),
            tax_rate: Number(item.tax_rate_value).toFixed(4),
            already_qty: Number(item.already_qty) || 0,
            already_amount: Number(item.already_amount) || 0,
            already_amount_without_tax: Number(item.already_amount_without_tax) || 0,
            wait_qty: Number(item.wait_qty) || 0,
          });
        });
      this.selectedList(productDetailList);
    },
    regionChange(address) {
      console.log('-> %c address  === %o ', 'font-size: 15px;color: green;', address);
      if (address.length) {
        const province = {
          name: CodeToText[address[0]],
          code: address[0],
        };
        const city = {
          name: CodeToText[address[1]],
          code: address[1],
        };
        const county = {
          name: CodeToText[address[2]],
          code: address[2],
        };
        console.log(province, city, county);
        this.formData.sender_province = province.name || '';
        this.formData.sender_city = city.name || '';
        this.formData.sender_county = county.name || '';
      } else {
        this.formData.sender_province = '';
        this.formData.sender_city = '';
        this.formData.sender_county = '';
      }
    },

    getOneKeyPurchaseInfo() {
      let params = { code: this.saleOrderCode };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getOneKeyPurchaseInfo(params)
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.formData.plan_date = S.moment(new Date()).format('YYYY-MM-DD');
          this.formData.contract_code = res.contract_code;
          this.formData.express_code = 'OTHER'; // 前端写死
          this.formData.express_no = '0000000001'; // 前端写死
          this.formData.sender_name = res.sender_info?.name;
          this.formData.sender_mobile = res.sender_info?.mobile;
          this.formData.sender_address = res.sender_info?.address;
          this.formData.sender_province = res.sender_info?.province;
          this.formData.sender_city = res.sender_info?.city;
          this.formData.sender_county = res.sender_info?.county;
          this.selectedAddress = [
            res.sender_info?.province_code,
            res.sender_info?.city_code,
            res.sender_info?.county_code,
          ];
          // 回显供应商poppver
          this.formData.supplier_code = res.supplier?.code;
          this.selectSupplierList.push({
            code: res.supplier?.code,
            name: res.supplier?.name,
          });

          // 回显仓库poppver
          this.formData.warehouse_code = res.warehouse?.code;
          this.selectWarehouseList.push({
            code: res.warehouse?.code,
            name: res.warehouse?.name,
          });

          this.handlerDetail(res.list, res.products);
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
  },
  destroyed() {},
};
</script>

<style scoped lang="less">
@import url('../../common/modal.less');
</style>
