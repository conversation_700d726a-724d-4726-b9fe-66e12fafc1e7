<template>
  <div class="purchase-list-wrapper">
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <Col>
          <FormItem>
            <Input v-model="queryFormData.code" placeholder="请输入采购订单编号" clearable />
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Input v-model="queryFormData.contract_code" placeholder="请输入合同编号" clearable />
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Select v-model="queryFormData.supplier_code" filterable placeholder="请选择供应商" clearable>
              <Option v-for="item in supplierList" :value="item.code" :key="item.code">{{ item.name }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Select v-model="queryFormData.warehouse_code" placeholder="请选择仓库" clearable>
              <Option v-for="item in warehouseList" :value="item.code" :key="item.code">{{ item.name }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Select v-model="queryFormData.audit_status" placeholder="请选择审核状态" clearable>
              <Option v-for="item in statusList" :value="item.code" :key="item.code">{{ item.name }}</Option>
            </Select>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col>
          <FormItem>
            <Select v-model="queryFormData.push_status" style="width: 180px" placeholder="推送状态" clearable>
              <Option v-for="item in pushStatus" :value="item.code" :key="item.code">{{ item.name }}</Option>
            </Select>
          </FormItem>
        </Col>

        <Col>
          <FormItem>
            <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
            <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
            <Button type="primary" @click="createPurchaseOrder">新建采购订单</Button>
          </FormItem>
        </Col>
      </Row>
    </Form>
    <div class="panel-nav">
      <a class="nav" :class="{ active: !queryFormData.list_status }" @click.prevent.capture="onStatusChange('')">
        全部
      </a>
      <a
        class="nav"
        v-for="item in tabStatus"
        :key="item.id"
        :class="{ active: $route.query.list_status == item.id }"
        @click.prevent.capture="onStatusChange(item.id)"
      >
        {{ item.desc }}
        <Tag :color="getTagColor(item.id)">{{ statusCount[item.id] }}</Tag>
      </a>
    </div>
    <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 332">
      <template v-slot:contract_code="{ row }">
        {{ row.contract_code || '-' }}
      </template>
      <template slot-scope="{ row }" slot="supplier_name">
        {{ row.supplier_info.supplier_name }}
        <!-- <span>
          <Tooltip :content="row.supplier_info.supplier_name">
            {{ row.supplier_info.supplier_name}}
          </Tooltip>
        </span> -->
      </template>
      <template slot-scope="{ row }" slot="push_status">
        <span v-if="row.push_status === '4'"
          >{{ row.push_status_text }}
          <Tooltip placement="bottom" max-width="200" :content="row.hangup_text">
            <Icon type="md-help-circle" style="font-size: 16px" class="cursor" />
          </Tooltip>
        </span>
        <span v-else>{{ row.push_status_text }}</span>
      </template>
      <template slot-scope="{ row }" slot="audit_status_text">
        <!--        <span :style="{color:statusColor(row.audit_status)}">{{row.audit_status_text }}</span>-->
        <status-text :status="row.audit_status"
          ><span>{{ row.audit_status_text }}</span></status-text
        >
      </template>
      <!--作废状态-->
      <template slot-scope="{ row }" slot="invalid_status_text">
        <p style="font-size: 13px; color: #000000; font-weight: 800; font-family: Microsoft YaHei">
          {{ row.invalid_status === '1' ? row.invalid_status_text : '-' }}
        </p>
      </template>
      <template slot-scope="{ row }" slot="remark">
        {{ row.remark || '-' }}
      </template>
      <template slot-scope="{ row }" slot="create_time">
        {{ row.create_time | date_format('YYYY-MM-DD') }}
      </template>
      <template slot-scope="{ row }" slot="update_time">
        {{ row.update_time | date_format('YYYY-MM-DD') }}
      </template>
      <template slot-scope="{ row }" slot="action">
        <a v-if="row.audit_status == '70' && row.invalid_status === '2'" class="mr-10" @click="createPurchaseOrder(row)"
          >编辑</a
        >
        <a @click="goDetail(row)" class="mr-10">详情</a>
        <a
          v-show="row.audit_status === '90' && row.stock_status !== 'COMPLETE'"
          v-eleControl="'EylXmmxl5K'"
          @click="createPurchaseOrder(row, true)"
          >修改采购单</a
        >
      </template>
    </Table>

    <div class="block_20"></div>
    <KPage
      :total="total"
      :page-size="+queryFormData.pageSize"
      :current="+queryFormData.page"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
      style="text-align: center"
    />

    <EditPurchaseOrder
      :orderId="orderId"
      :visible.sync="editVisible"
      :isModification="isModification"
      @refresh="refresh"
    ></EditPurchaseOrder>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'utils/util';
import EditPurchaseOrder from '@/view/erp/purchase/compontents/EditPurchaseOrder';
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  code: '', // 采购单编号
  contract_code: '', // 合同编号
  supplier_code: '', // 供应商编号
  warehouse_code: '', // 仓库编号
  push_status: '', // 推送状态
};

export default {
  name: 'list',
  components: { EditPurchaseOrder },
  mixins: [search],
  data() {
    return {
      apiName: 'getErpPurchaseList',
      queryFormData: {
        ...init_query_form_data,
      },
      pushStatus: [
        { name: '不推送', code: '0' },
        { name: '待推送', code: '1' },
        { name: '挂起', code: '4' },
        { name: '已推送', code: '9' },
      ],
      tableCols: [
        { title: '采购单编号', key: 'code', align: 'center' },
        { title: '合同编号', slot: 'contract_code', align: 'center' },
        { title: '采购日期', key: 'plan_date', align: 'center' },
        { title: '供应商名称', slot: 'supplier_name', align: 'center', width: '' },
        { title: '采购金额', key: 'total_price', align: 'center' },
        { title: '备注', slot: 'remark', align: 'center' },
        { title: '审核状态', slot: 'audit_status_text', width: 90 },
        { title: '作废状态', slot: 'invalid_status_text', align: 'center', width: 80 },
        { title: '入库状态', key: 'stock_status_text', align: 'center' },
        { title: '推送状态', slot: 'push_status', align: 'center', width: 80 },

        { title: '创建人', key: 'operator', align: 'center' },
        { title: '创建时间', slot: 'create_time', align: 'center' },
        { title: '更新时间', slot: 'update_time', align: 'center' },
        { title: '操作', slot: 'action', align: 'center', width: 120 },
      ],

      orderId: '', // 采购订单id
      editVisible: false, // 新建/编辑采购订单弹窗
      supplierList: [], // 供应商列表
      warehouseList: [], //仓库列表
      statusList: [
        { code: '10', name: '审核中' },
        { code: '90', name: '审核完成' },
        { code: '70', name: '拒绝' },
      ], // 状态列表
      isModification: false, // 是否改变
      tabStatus: [],
    };
  },
  computed: {
    statusColor() {
      return function (status) {
        if (status == '10') {
          return '#2db7f5';
        } else if (status == '90') {
          return '#19be6b';
        } else if (status == '70') {
          return '#ed4014';
        }
      };
    },
    getTagColor() {
      return type => {
        switch (type) {
          case 'TO_BE_EXAMINED': // 待审核
          case 'TO_BE_DELIVERED': // 诊所待付款
          case 'COM_W_PAY': // 省公司待付款
            return 'warning';
          case 'WAIT_SHIP': // 待发货
            return 'primary';
          case 'REJECTED': // 待发货
            return 'error';
          case 'DELIVERED': // 已发货
          case 'FINISHED': // 已完成
            return 'success';
          default: // 已取消
            return 'default';
        }
      };
    },
  },
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.init();
  },
  mounted() {},
  methods: {
    init() {
      this.getErpSupplierList();
      this.getErpWarehouseList();
      this.getErpPurchaseOptions();
    },
    // 新建采购订单
    createPurchaseOrder(row, isModification) {
      if (row) {
        this.orderId = row.id;
      } else {
        this.orderId = '';
      }
      this.editVisible = true;
      this.isModification = isModification || false;
    },

    refresh() {
      this.loadList();
    },

    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },
    goDetail(row) {
      this.$router.push({
        path: '/erp/purchase/detail',
        query: {
          id: row.id,
        },
      });
    },
    getErpSupplierList() {
      this.$api.getErpSupplierList({ pageSize: 1000 }).then(res => {
        this.supplierList = res.list;
      });
    },
    getErpWarehouseList() {
      this.$api.getErpWarehouseList().then(res => {
        this.warehouseList = res.list;
      });
    },
    getErpPurchaseOptions() {
      let params = {};
      this.$api.getErpPurchaseOptions(params).then(res => {
        console.log('=>(list.vue:283) res', res);
        this.tabStatus = S.descToArrHandle(res.listStatusDesc);
      });
    },
    onStatusChange(status) {
      this.queryFormData.page = 1;
      this.queryFormData.list_status = status;
      this.submitQueryForm();
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  },
};
</script>

<style scoped lang="less">
.supplier-list-wrapper {
}

.mr-10 {
  margin-right: 10px;
}

// ::v-deep .ivu-table-cell .ivu-table-cell-slot {
//     display: block;
//     width: 90px;
//     overflow: hidden;
//     text-overflow: ellipsis;
//     white-space: nowrap;
//     word-break: break-all;
//     box-sizing: border-box;
//   }
</style>
