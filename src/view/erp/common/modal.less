::v-deep .ivu-modal-body {
  padding: 20px 30px;
  height: calc(~'100% - 110px');
  overflow-y: auto;
}
::v-deep .ivu-modal {
  height: calc(~'100% - 100px') !important;
}
::v-deep .ivu-modal-content {
  height: calc(~'100% - 100px');
}
.custom-label {
  font-size: 14px;
  line-height: 1.5;
  color: #6b778c;
  word-break: break-all;
  word-wrap: break-word;
  padding-bottom: 4px !important;
}

// 总计展示
.total-show {
  font-size: 12px;
  font-weight: 400;
  color: #000000;
  line-height: 17px;
  .amount {
    margin: 0 4px;
    font-size: 13px;
    font-weight: 600;
  }
}

.mt10 {
  margin-top: 10px;
}
.mt30 {
  margin-top: 30px;
}
.ml10 {
  margin-left: 10px;
}
.mr10 {
  margin-right: 10px;
}
.addWrap {
  display: flex;
  align-items: center;

  .addressBox {
    div.rg-select div.rg-select__el div.rg-select__content {
      padding: 0 30px 0px 15px !important;
      font-size: 12px;
    }
  }

  .addressInput {
    flex: 1;
  }
}

::v-deep .el-input__inner {
  padding: 0 24px 0 8px;
  border: 1px solid #dcdee2;
  font-size: 12px;
}
