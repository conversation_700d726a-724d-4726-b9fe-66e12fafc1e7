<template>
  <div class="container">
    <div class="demo-spin-container" v-if="detailLoading">
      <Spin fix></Spin>
    </div>
    <Tabs v-else :value="tab" @on-click="changeTab" :animated="false">
      <!-- 详细资料 -->
      <TabPane label="详细资料" name="detail">
        <div class="block-header">
          基本信息
          <Button type="primary" class="edit-button" @click="showEditModal" v-if="formData.audit_status == '70'"
            >编辑基础信息</Button
          >
        </div>
        <div class="flex flex-between">
          <Form :model="formData" label-position="right" :label-width="100" class="basicInfo">
            <Row :gutter="40">
              <Col span="12">
                <FormItem label="销售退货单编号:">
                  {{ formData.code }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="销售单号:">
                  {{ formData.order_code }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="客户姓名:">
                  <span>{{ formData.cg_name || '-' }}</span>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="退货金额:">
                  {{ formData.total_price }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="退货日期:">
                  {{ formData.plan_date }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="退货原因:">
                  {{ formData.reason }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="备注:">
                  {{ formData.remark || '-' }}
                </FormItem>
              </Col>
            </Row>
          </Form>
        </div>
        <div class="block-header">系统信息</div>
        <Form :model="formData" label-position="right" :label-width="80" class="basicInfo">
          <Row :gutter="40">
            <Col span="12">
              <FormItem label="创建人:">
                {{ formData.operator }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="创建时间:">
                {{ formData.create_time | date_format }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="更新时间:">
                {{ formData.update_time | date_format }}
              </FormItem>
            </Col>
          </Row>
        </Form>
        <div class="block-header">收件人信息</div>
        <Form :model="formData" label-position="right" :label-width="80" class="basicInfo">
          <Row :gutter="40">
            <Col span="12">
              <FormItem label="发件人:">
                {{ formData.sender_name || '-' }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="发件人电话:">
                {{ formData.sender_mobile || '-' }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="物流公司:">
                {{ formData.express_name || '-' }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="快递单号:">
                {{ formData.express_no || '-' }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="发货地址:">
                {{ getSenderAddress || '-' }}
              </FormItem>
            </Col>
          </Row>
        </Form>
        <div class="block-header">产品</div>
        <Table :columns="tableCols" :data="list">
          <template slot-scope="{ row, index }" slot="code">
            <KLink v-if="row.code" :to="{ path: '/erp/product/detail', query: { id: row.id } }" target="_blank">{{
              row.code
            }}</KLink>
            <span v-else>-</span>
          </template>

          <template slot-scope="{ row, index }" slot="barcode">
            {{ row.barcode || '-' }}
          </template>
          <template slot-scope="{ row, index }" slot="spec">
            {{ row.spec || '-' }}
          </template>
          <template slot-scope="{ row, index }" slot="unit">
            {{ row.unit || '-' }}
          </template>
          <template slot-scope="{ row, index }" slot="note">
            {{ row.note || '-' }}
          </template>
        </Table>
        <div class="total mt10">
          已选中产品：<span class="total-num">{{ this.list.length }}</span> 总金额：{{ this.formData.total_amount }}元
        </div>
        <div class="block_20"></div>
        <div class="block_20"></div>
      </TabPane>

      <!-- 入库记录 -->
      <TabPane label="入库记录" name="inboundDetail">
        <div class="mb-10 inbound-title flex flex-item-between">
          <span>待入库产品</span>
          <Button type="primary" @click="productEnter" v-if="formData.audit_status === '90' && waitList.length !== 0"
            >入库</Button
          >
        </div>
        <div class="empty" v-if="waitList.length === 0">暂无数据</div>
        <Table v-else :columns="waitTableCols" :data="waitList" height="300">
          <template slot-scope="{ row }" slot="code">
            <KLink
              v-if="row.product && row.product.code"
              :to="{ path: '/erp/product/detail', query: { code: row.product.code } }"
              target="_blank"
              >{{ row.product.code }}</KLink
            >
            <span v-else>-</span>
          </template>

          <template slot-scope="{ row }" slot="barcode">
            {{ row.product.barcode || '-' }}
          </template>

          <template slot-scope="{ row }" slot="name">
            {{ row.product.name }}
          </template>
          <template slot-scope="{ row }" slot="spec">
            {{ row.product.spec }}
          </template>
          <template slot-scope="{ row }" slot="unit">
            {{ row.product.unit }}
          </template>
        </Table>

        <div class="inbound-title mb10 mt10">入库记录</div>
        <div class="inboundTable" v-for="item in inboundList" :key="item.code">
          <div class="flex mb-10 mt10">
            <div class="mr20">
              入库单号：
              <KLink
                v-if="item.code"
                :to="{ path: '/erp/product-enter/detail', query: { code: item.code } }"
                target="_blank"
                >{{ item.code }}</KLink
              >
              <span v-else>-</span>
            </div>
            <div class="mr20">入库仓库：{{ item.warehouse_name }}</div>
            <div class="mr20">入库时间：{{ item.actual_time | date_format('YYYY-MM-DD HH:mm:ss') }}</div>
            <div>操作人：{{ item.operator }}</div>
          </div>
          <Table :columns="outboundTableCols" :data="item.detail" class="mb20">
            <template slot-scope="{ row }" slot="code">
              <KLink
                v-if="row.product && row.product.code"
                :to="{ path: '/erp/product/detail', query: { code: row.product.code } }"
                target="_blank"
                >{{ row.product.code }}</KLink
              >
              <span v-else>-</span>
            </template>
            <template slot-scope="{ row }" slot="barcode">
              {{ row.product.barcode || '-' }}
            </template>

            <template slot-scope="{ row }" slot="name">
              {{ row.product.name }}
            </template>
            <template slot-scope="{ row }" slot="spec">
              {{ row.product.spec }}
            </template>
            <template slot-scope="{ row }" slot="unit">
              {{ row.product.unit }}
            </template>
          </Table>
        </div>
        <div class="empty" v-if="inboundList.length === 0">暂无数据</div>
      </TabPane>

      <!-- 操作记录 -->
      <TabPane label="操作记录" name="operationRecord">
        <operationlog-record :b_type="b_type" :b_id="b_id" :isRecord="isRecord"></operationlog-record>
      </TabPane>
    </Tabs>

    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Button v-if="formData.audit_status === '10'" style="margin: 0 20px" type="error" @click="showRefuseModal"
        >审核驳回
      </Button>
      <Button v-if="formData.audit_status === '10'" type="primary" @click="passCheck">审核通过 </Button>
    </div>

    <EditReturnOrderDialog :order-id="orderId" :visible.sync="editVisible" @refresh="init"></EditReturnOrderDialog>
    <EditProductEnter
      :code="order_code"
      :type="'30'"
      :isDetailCreate="isDetailCreate"
      :visible.sync="productVisible"
      @refresh="getOrderReturnInboundDetail"
      @changeCreate="changeCreate"
    ></EditProductEnter>
    <refuse-reason-modal :visible.sync="refuseModalVisible" :auditSalesOrder="review"></refuse-reason-modal>
  </div>
</template>

<script>
import EditReturnOrderDialog from './compontents/EditReturnOrderDialog';
import EditProductEnter from '@/view/erp/product-enter/compontents/EditProductEnter';
import OperationlogRecord from '../components/operationlog-record';
import RefuseReasonModal from '@/components/RefuseReasonModal';
import { $operator } from '@/utils/operation';
const init_query_form_data = {
  // page: 1,
  // pageSize: 20,
  id: '', // 采购订单id
  code: '' // 采购订单编号
};

export default {
  data() {
    return {
      tab: 'detail',
      formData: {
        name: '1',
        sender_name: '',
        sender_mobile: '',
        express_name: '', // 快递名称
        sender_address: '',
        sender_city: '',
        sender_province: '',
        sender_county: '',
        express_no: '' // 快递单号
      },
      orderId: '',
      editVisible: false,
      productVisible: false, // 产品入库
      tableCols: [
        { title: '产品编码', slot: 'code', align: 'center' },
        { title: '产品条码', slot: 'barcode', align: 'center' },
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '退货数量', key: 'quantity', align: 'center' },
        { title: '退货单价', key: 'price', align: 'center' },
        { title: '合计', key: 'total_price', align: 'center' }
      ],
      list: [],
      total: 0,
      queryFormData: { ...init_query_form_data },
      productsList: [],
      b_type: 18,
      b_id: 0,
      isRecord: 0,
      showCard: false,
      refuseModalVisible: false,
      reason: '',
      waitTableCols: [
        { title: '产品编码', slot: 'code', align: 'center' },
        { title: '产品条码', slot: 'barcode', align: 'center' },
        { title: '产品名称', slot: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '采购数量', key: 'quantity', align: 'center' },
        { title: '已入库', key: 'p_qty', align: 'center' },
        { title: '待入库', key: 'w_qty', align: 'center' }
      ],
      waitList: [],
      outboundTableCols: [
        { title: '产品编码', slot: 'code', align: 'center' },
        { title: '产品条码', slot: 'barcode', align: 'center' },
        { title: '产品名称', slot: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '本次入库', key: 'quantity', align: 'center' },
        { title: '备注', key: 'note', align: 'center' }
      ],
      inboundList: [],
      returnTableCols: [
        { title: '采购退货单编码', key: 'code', align: 'center', width: 120 },
        { title: '采购单编号', key: 'purchase_code', align: 'center', width: 120 },
        { title: '供应商名称', key: 'supplier_name', align: 'center', tooltip: true },
        { title: '退货金额', key: 'total_price', align: 'center' },
        { title: '退货日期', key: 'plan_date', align: 'center' },
        { title: '退货原因', key: 'reason', align: 'center', tooltip: true },
        { title: '备注', key: 'remark', align: 'center', tooltip: true },
        { title: '审核状态', key: 'audit_status_text', align: 'center' },
        { title: '库存状态', key: 'stock_status_text', align: 'center' },
        { title: '创建人', key: 'operator', align: 'center' },
        { title: '创建时间', slot: 'create_time', align: 'center' },
        { title: '更新时间', slot: 'update_time', align: 'center' }
      ],
      returnList: [],
      detailLoading: false,
      order_code: '', //销售订单编码
      isDetailCreate: false //是否详情创建出库
    };
  },
  created() {},
  mounted() {
    this.init();
  },
  components: {
    OperationlogRecord,
    EditReturnOrderDialog,
    EditProductEnter,
    RefuseReasonModal
  },
  computed: {
    getSenderAddress() {
      return (
        (this.formData.sender_province || '') +
          (this.formData.sender_city || '') +
          (this.formData.sender_county || '') +
          (this.formData.sender_address || '') || ''
      );
    }
  },
  methods: {
    init() {
      if (this.$route.query.id || this.$route.query.code) {
        this.getErpReturnOrderInfo(this.$route.query.id, this.$route.query.code);
      }
    },
    // tabs事件
    changeTab(name) {
      if (name == 'inboundDetail') {
        this.getOrderReturnInboundDetail();
      } else if (name == 'operationRecord') {
        this.isRecord++;
      }
    },
    getErpReturnOrderInfo(id, code) {
      this.detailLoading = true;
      let params = { id, code };
      this.$api.getErpReturnOrderInfo(params).then(res => {
        this.formData = res;
        this.order_code = res.code;
        this.$router.replace({
          query: {
            ...this.$route.query,
            id: res.id
          }
        });
        this.queryFormData.id = this.$route.query.id;
        this.b_id = Number(this.$route.query.id);
        this.detailLoading = false;
        this.getErpReturnOrderProductDetail();
      });
    },
    getErpReturnOrderProductDetail() {
      let params = { ...this.queryFormData };
      this.$api.getErpReturnOrderProductDetail(params).then(res => {
        this.handlerDetail(res.list, res.products);
      });
    },

    // 处理明细数据，回显采购订单
    handlerDetail(list, products) {
      console.log('-> %c list, products  === %o', 'font-size: 15px;color: green;', list, products);
      let productDetailList = [];
      list &&
        list.forEach(item => {
          productDetailList.push({
            ...products[item.product_id],
            quantity: item.quantity,
            price: item.price,
            total_price: item.total_price
          });
        });
      console.log('-> %c productDetailList  === %o', 'font-size: 15px;color: green;', productDetailList);
      this.list = productDetailList;
      this.handlerTotalPrice(this.list);
    },

    handlerTotalPrice(list) {
      let sum = 0;
      list.forEach(item => {
        sum = $operator.add(sum, Number(item.total_price));
      });
      this.formData.total_amount = sum;
    },

    getOrderReturnInboundDetail() {
      let params = { id: this.$route.query.id };
      // let params ={id: '1'}
      this.$api.getOrderReturnInboundDetail(params).then(res => {
        this.waitList = res.wait_detail;
        this.inboundList = res.inbound_detail;
      });
    },

    // 新建按钮
    showEditModal() {
      this.orderId = this.$route.query.id;
      this.editVisible = true;
    },

    // 审核按钮
    passCheck() {
      this.$Modal.confirm({
        title: '通过审核',
        content: '您确定要通过该审核吗？',
        onOk: () => {
          this.review('PASS');
        }
      });
    },

    // 审核驳回
    showRefuseModal() {
      this.refuseModalVisible = true;
    },

    refuseCancel() {
      this.refuseModalVisible = false;
    },

    /**
     * @description 审核/驳回的接口
     * @param { action } 审核的状态
     * @param { reason } 驳回的原因
     * */
    review(action, reason) {
      const params = {
        id: this.$route.query.id,
        action,
        reason: ''
      };
      let isPass = true;
      if (reason) {
        params.reason = reason;
        isPass = false;
      }
      this.$api.changeSalesReturnOrderStatus(params).then(
        res => {
          this.$Message.success(`${isPass ? '通过审核成功' : '驳回审核成功'}`);
          this.init();
        },
      );
    },

    submitRefuseReason() {
      if (this.reason == '') {
        this.$Message.error('请输入驳回原因');
        return;
      }
      this.refuseCancel();
      this.review('REJECT', this.reason);
    },

    showCardHandle() {
      this.showCard = !this.showCard;
    },
    back() {
      this.$router.back();
    },

    // 入库按钮
    productEnter() {
      this.isDetailCreate = true;
      this.productVisible = true;
    },

    // 入库弹窗关闭时间
    changeCreate(val) {
      this.isDetailCreate = val;
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  //position: relative;
  .card {
    position: absolute;
    top: 0;
    right: 0;
    width: 350px;
    // height: 200px;
  }
  .card-show {
    position: absolute;
    top: 14px;
    right: 16px;
  }
  .total {
    text-align: right;
    .total-num {
      margin-right: 15px;
      color: #fd715a;
    }
  }
  .inbound-title {
    position: relative;
    padding-left: 10px;
    line-height: 32px;
    &::before {
      position: absolute;
      top: 50%;
      left: 0;
      display: block;
      width: 3px;
      height: 14px;
      content: '';
      background: #0052cc;
      -webkit-transform: translateY(-50%);
      transform: translateY(-50%);
    }
  }
}

// ::v-deep .ivu-card-body{
//     padding: 0;
// }

.basicInfo {
  width: 60%;
  margin-left: 60px;
  .basic-item {
    width: 50%;
  }
  .remark {
    width: 100%;
  }
}

.buttonGroup {
  text-align: center;
}

.ml-10 {
  margin-left: 10px;
}

.mr-10 {
  margin-right: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

::v-deep .block-header span {
  font-size: 12px;
  padding: 0;
  font-weight: normal;
}

.edit-button {
  position: absolute;
  top: 3px;
  right: 5px;
}

.demo-spin-container {
  display: inline-block;
  width: 100%;
  height: 100%;
}
</style>
