<template>
  <el-popover
    placement="bottom"
    width="900"
    transfer
    :visible-arrow="false"
    :poper-options="popOptions"
    popper-class="pop-common"
    @show="onShow"
    :disabled="isDetail"
    v-model="showPop"
    trigger="click"
  >
    <slot slot="reference" style="width: 100%"></slot>
    <div class="popover-content-box">
      <div class="header-box">
        <h4 class="title">{{ title }}</h4>
        <div class="search-box">
          <span>搜索：</span>
          <Input v-model="queryFormData.code" placeholder="请输入销售单号" @keyup.enter.native="getList">
            <Button slot="append" icon="ios-search" @click="getList"></Button>
          </Input>
        </div>
      </div>
      <div class="table-wrapper">
        <Table :columns="tableCols" :data="list" ref="section-table" height="300" :loading="tableLoading">
          <!-- 供应商名称 -->
          <template slot-scope="{ row, index }" slot="checkBox">
            <Checkbox :value="list[index].checked" @on-change="changeSelectOrder(row, index)"></Checkbox>
          </template>
          <!-- 供应商名称 -->
          <template slot-scope="{ row }" slot="supplier_name">
            {{ (row.supplier_info && row.supplier_info.supplier_name) || '-' }}
          </template>

          <!-- 销售金额 -->
          <template slot-scope="{ row }" slot="total_price">
            {{ row.payment_fee ? `￥${row.payment_fee}` : '-' }}
          </template>

          <!-- 备注 -->
          <template slot-scope="{ row }" slot="remark">
            {{ row.remark || '-' }}
          </template>

          <!-- 创建人 -->
          <template slot-scope="{ row }" slot="operator">
            {{ row.operator || '-' }}
          </template>

          <!-- 销售时间 -->
          <template slot-scope="{ row }" slot="deal_time">
            {{ row.deal_time | date_format('YYYY-MM-DD') }}
          </template>
          <!-- 创建时间 -->
          <template slot-scope="{ row }" slot="create_time">
            {{ row.create_time | date_format('YYYY-MM-DD') }}
          </template>

          <!-- 更新时间 -->
          <template slot-scope="{ row }" slot="update_time">
            {{ row.update_time | date_format('YYYY-MM-DD') }}
          </template>
        </Table>
        <div class="block_20"></div>
        <KPage
          :total="total"
          :page-size="+queryFormData.pageSize"
          :current="+queryFormData.page"
          @on-change="handleCurrentChange"
          @on-page-size-change="handleSizeChange"
          style="text-align: center"
          :show-sizer="false"
        />
      </div>
      <div class="bottom-btn-wrapper">
        <Button @click="onCancel">取消</Button>
        <Dvd />
        <Dvd />
        <Dvd />
        <Button type="primary" @click="onConfirm">确定</Button>
      </div>
    </div>
  </el-popover>
</template>

<script>
// import S from '@/utils/util'
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  code: '', // 销售单编号
  audit_status: '90',
  stock_status: 'PART,COMPLETE',
  return_status: '2,3',
  r: ''
};
export default {
  name: 'select-purchase-popper',
  mixins: [],

  components: {},

  props: {
    title: {
      type: String,
      default: '选择销售退货单'
    },
    apiName: {
      type: String,
      default: 'getErpOrderList'
    },
    code: {
      type: String,
      default: ''
    },
    // 审核状态；10 待审核，90 审核完成，70 拒绝
    audit_status: {
      type: String,
      default: () => '90'
    },
    isDetail: {
      type: Boolean,
      default: () => false
    }
  },

  data() {
    return {
      popOptions: { boundariesElement: 'body', gpuAcceleration: false },
      tableLoading: false,
      list: [],
      tableCols: [
        { slot: 'checkBox', align: 'center', fixed: 'left' },
        { title: '销售单编码', key: 'code', align: 'center' },
        { title: '销售日期', slot: 'deal_time', align: 'center' },
        { title: '客户姓名', key: 'cg_name', align: 'center' },
        { title: '销售金额', slot: 'total_price', align: 'center' },
        { title: '备注', slot: 'remark', align: 'center' },
        { title: '审核状态', key: 'audit_status_text', align: 'center' },
        { title: '库存状态', key: 'stock_status_text', align: 'center' },
        { title: '创建人', slot: 'operator', align: 'center' },
        { title: '创建时间', slot: 'create_time', align: 'center' },
        { title: '更新时间', slot: 'update_time', align: 'center' }
      ],
      queryFormData: {
        ...init_query_form_data
      },
      total: 0,
      selected_items: {},
      showPop: false
    };
  },

  computed: {},

  watch: {
    showPop: {
      immediate: true,
      handler(val) {
        if (val) {
          this.queryFormData = { ...init_query_form_data };
          this.list &&
            this.list.some((item, index) => {
              if (item.code == this.code) {
                this.changeSelectOrder('', index);
                return true;
              }
            });
        } else {
          this.selected_items = {};
        }
      }
    }
  },

  created() {},

  mounted() {},

  methods: {
    changeSelectOrder(row, index) {
      let isChecked = this.list[index].checked;
      this.list.map(item => (item.checked = false));
      this.list[index].checked = !isChecked;
      if (isChecked) {
        this.selected_items = {};
      } else {
        this.selected_items = this.list[index];
      }
    },
    onCancel() {
      this.showPop = false;
    },
    onConfirm() {
      this.$emit('selectOrder', this.selected_items);
      this.showPop = false;
    },
    onSearch() {
      this.queryFormData.page = 1;
      this.getList();
    },

    getList() {
      this.tableLoading = true;
      let params = {
        ...this.queryFormData
      };
      this.$api[this.apiName](params).then(res => {
        this.tableLoading = false;
        this.list = this.handler(res.list);
        this.total = res.total;
      });
    },
    handler(list) {
      if (!list) return [];
      list.forEach((item, index) => {
        item.checked = false;
        if (item.code == this.code || this.selected_items.code == item.code) {
          item.checked = true;
          this.selected_items = item;
        }
      });
      return list;
    },
    handleSizeChange(val) {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.queryFormData.page = val;
      this.getList();
    },
    onShow() {
      this.getList();
      this.showPop = true;
    }
  },

  destroyed() {}
};
</script>

<style scoped lang="less">
:deep(.el-popover__reference-wrapper) {
  width: 100%;
}

.popover-content-box {
  .header-box {
    .title {
      font-weight: 600;
      line-height: 50px;
      padding: 0 20px;
      font-size: 16px;
      border-bottom: 1px solid rgb(223, 225, 230);
    }

    .search-box {
      display: flex;
      align-items: center;
      padding: 10px 20px;

      .el-input {
        width: 200px;
      }

      .el-button {
        margin-left: 10px;
      }
    }
  }

  .bottom-btn-wrapper {
    height: 50px;
    display: flex;
    justify-content: flex-end;
    padding: 10px 20px;
    border-top: 1px solid rgb(223, 225, 230);
    margin-top: 20px;
  }
}
</style>
<style lang="less">
.pop-common {
  padding: 0 !important;
}
</style>
