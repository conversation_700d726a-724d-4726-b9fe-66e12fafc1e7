<template>
  <div>
    <Modal
      :value="visible"
      :title="orderId ? '编辑销售退货单' : '新建销售退货订单'"
      width="900px"
      @on-cancel="cancelHandle"
      :mask-closable="false"
    >
      <Form
        label-position="top"
        class="common-modal-form"
        label-colon
        :model="formData"
        :rules="formDataValidateRules"
        ref="salesOrderForm"
      >
        <div class="section-header">
          <div class="section-mark"></div>
          <div class="section-title">基本信息</div>
        </div>

        <div class="create-section">
          <FormItem label="销售退货单编号" class="common-form-item">
            <Input v-model="formData.code" placeholder="不填系统将自动生成" maxlength="20" :disabled="!!orderId" />
          </FormItem>

          <FormItem label="销售单编号" prop="order_code" class="common-form-item">
            <SelectSalesOrder
              class="flex"
              ref="selectSaleOrder"
              @selectOrder="selectOrder"
              :code="formData.order_code"
              :is-detail="!!orderId"
            >
              <el-select
                :value="formData.order_code"
                style="width: 100%"
                :disabled="!!orderId"
                @visible-change="selectChange($event, 'selectSaleOrder')"
                size="small"
                popper-class="rxj-pop-select"
              >
                <el-option
                  v-for="(item, index) in selectOrderList"
                  :value="item"
                  :label="item"
                  :key="index + item"
                ></el-option>
              </el-select>
            </SelectSalesOrder>
          </FormItem>
          <FormItem label="仓库名称" class="common-form-item" prop="warehouse_code">
            <SelectWarehousePopper
              class="flex"
              ref="selectWarehouse"
              @selectSup="selectWarehouse"
              :code="formData.warehouse_code"
            >
              <el-select
                :multiple="false"
                v-model="formData.warehouse_code"
                :multiple-limit="1"
                style="width: 100%"
                @visible-change="selectChange($event, 'selectWarehouse')"
                size="small"
                @on-change="supplierChange"
                popper-class="rxj-pop-select"
              >
                <el-option
                  v-for="(item, index) in selectWarehouseList"
                  :value="item.code"
                  :label="item.name"
                  :key="index + item.code"
                ></el-option>
              </el-select>
            </SelectWarehousePopper>
          </FormItem>
          <!--          <FormItem label="退款金额" prop="payment_fee" class="common-form-item">-->
          <!--            <InputNumber :min="0" v-model="formData.payment_fee" :active-change="false"  placeholder="请输入支付金额" style="width: 100%;"/>-->
          <!--          </FormItem>-->
          <FormItem label="退货日期" prop="plan_date" class="common-form-item">
            <DatePicker
              type="date"
              ref="refund-time"
              style="width: 100%"
              placeholder="请选择退款日期"
              :value="formData.plan_date"
              @on-change="date => (formData.plan_date = date)"
            ></DatePicker>
          </FormItem>

          <FormItem label="退货原因" prop="reason" class="common-form-item">
            <Input
              v-model="formData.reason"
              placeholder="请输入退款原因"
              type="textarea"
              maxlength="50"
              show-word-limit
              :autosize="{ maxRows: 2, minRows: 2 }"
              style="width: 100%"
            />
          </FormItem>
          <FormItem label="备注" class="common-form-item">
            <Input
              v-model="formData.remark"
              placeholder="请输入备注"
              type="textarea"
              maxlength="50"
              show-word-limit
              :autosize="{ maxRows: 2, minRows: 2 }"
            ></Input>
          </FormItem>
        </div>
        <div class="section-header mt10">
          <div class="section-mark"></div>
          <div class="section-title">产品信息</div>
        </div>

        <div class="table-wrapper">
          <Table class="mt10" :loading="tableLoading" :columns="product_tableCols" :data="product_list">
            <template slot-scope="{ row, index }" slot="spec">
              {{ row.spec || '-' }}
            </template>
            <template slot-scope="{ row, index }" slot="unit">
              {{ row.unit || '-' }}
            </template>
            <!-- 采购数量 -->
            <template slot-scope="{ row, index }" slot="return_quantity">
              <InputNumber
                style="width: 100%"
                :min="0"
                v-model="product_list[index].return_quantity"
                :precision="0"
                :active-change="false"
                placeholder="退货数量"
                :max="+row.refundable_quantity"
                :disabled="!Number(row.refundable_quantity)"
                @on-focus="removeZero($event, 'return_quantity', index)"
                @on-change="val => changeUnitNum(val, index)"
              ></InputNumber>
            </template>

            <!-- 采购单价 -->
            <template slot-scope="{ row, index }" slot="price">
              <InputNumber
                style="width: 100%"
                :precision="2"
                :active-change="false"
                :min="0"
                v-model="product_list[index].price"
                :disabled="!Number(row.refundable_quantity)"
                placeholder="退货单价"
                @on-focus="removeZero($event, 'price', index)"
                @on-change="val => changeUnitPrice(val, index)"
              ></InputNumber>
            </template>

            <!-- 合计 -->
            <template slot-scope="{ row, index }" slot="total_price"> ￥{{ row.total_price }} </template>

            <template slot-scope="{ row, index }" slot="action">
              <a @click="deleteProduct(index)">删除</a>
            </template>
          </Table>
          <div class="mt10 flex flex-item-end">
            <span class="custom-label mr10">已选中产品：{{ product_list.length }}</span>
            <span class="custom-label">总金额：{{ getTotalAmount }} 元</span>
          </div>
        </div>

        <div class="create-section">
          <FormItem label="快递公司" class="common-form-item" prop="express_code" :rules="customRules.express_code">
            <Select v-model="formData.express_code" placeholder="请选择快递公司">
              <Option v-for="item in getExpressList" :key="item.code" :value="item.code">{{ item.name }}</Option>
            </Select>
          </FormItem>

          <FormItem label="快递单号" class="common-form-item" prop="express_no" :rules="customRules.express_no">
            <Input v-model="formData.express_no" placeholder="请输入快递单号"></Input>
          </FormItem>

          <FormItem label="客户姓名" class="common-form-item" prop="sender_name" :rules="customRules.sender_name">
            <Input v-model="formData.sender_name" placeholder="请输入收件人姓名"></Input>
          </FormItem>

          <FormItem label="手机号" class="common-form-item" prop="sender_mobile" :rules="customRules.sender_mobile">
            <Input v-model="formData.sender_mobile" placeholder="请输入收件人手机号" />
          </FormItem>
          <div class="flex" style="width: 100%">
            <FormItem
              :rules="customRules.sender_county"
              label="客户地址"
              prop="sender_county"
              class="common-form-item"
              style="width: 300px"
            >
              <div class="addWrap">
                <div class="addressBox">
                  <!--								<v-region v-model="selectedAddress"-->
                  <!--								          @values="regionChange" :disabled="!$route.query.isEdit &&!!$route.query.id"></v-region>-->
                  <el-cascader
                    v-model="selectedAddress"
                    :options="regionData"
                    placeholder="请选择联系地址"
                    size="small"
                    clearable
                    popper-class="address-com"
                    style="width: 300px"
                    @change="regionChange"
                  >
                  </el-cascader>
                </div>
              </div>
            </FormItem>

            <div class="addressInput ml10" style="width: 100%">
              <FormItem
                label="详细地址"
                prop="sender_address"
                :rules="customRules.sender_address"
                class="common-form-item"
                style="width: 100%"
              >
                <Input v-model="formData.sender_address" placeholder="详细地址" />
              </FormItem>
            </div>
          </div>
        </div>
      </Form>

      <div slot="footer">
        <Button @click="cancelHandle">取消</Button>
        <Button type="primary" @click="confirmHandle" :loading="submitLoading">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import S from 'utils/util';
import { $operator } from '@/utils/operation';
import SelectSalesOrder from './SelectSalesOrder';
import SelectWarehousePopper from '@/components/select-warehouse-popper/select-warehouse-popper';
import { CodeToText, regionData, TextToCode } from '@/utils/chinaMap';
// import {CodeToText, regionData,Te} from '@/utils/chinaMap'

const initFormData = {
  id: '', //销售退款单编号；如果不填写，创建时系统会自动生成
  order_code: '', //采购单编号
  reason: '', //退款原因
  warehouse_code: '', //仓库编号
  remark: '', //备注
  plan_date: '', //销售平台
  sender_province: '', //发件人省
  sender_city: '', //发件人市
  sender_county: '', //发件人区
  sender_address: '', //发件人详细地址
  sender_name: '', //发件人姓名
  sender_mobile: '', //发件人手机号
  express_code: '', //快递公司
  express_no: '' //发件物流单号
};

export default {
  name: 'EditPurchaseOrder',
  mixins: [],
  components: { SelectSalesOrder, SelectWarehousePopper },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    orderId: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      formData: { ...initFormData },
      formDataValidateRules: {
        order_code: [{ required: true, message: '请选择销售单', trigger: 'blur' }],
        warehouse_code: [{ required: true, message: '请选择仓库', trigger: 'blur,change' }]
        // platform: [{ required: true, message: '请选择销售平台', trigger: 'change' },],
        // deal_time: [{ required: true, message: '请选择拍单时间', trigger: 'change' },],
        // pay_time: [{ required: true, message: '请选择支付时间', trigger: 'change' },],
        // payment_fee: [{ required: true,type: 'number', message: '请输入支付金额', trigger: 'change' },],
      },
      regionData,
      submitLoading: false, // 弹窗确定的loading
      selectWarehouseList: [], // 选中的仓库
      tableLoading: false,
      product_tableCols: [
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '可退数量', key: 'refundable_quantity', align: 'center' },
        { title: '退货数量', slot: 'return_quantity', align: 'center' },
        {
          title: '采购单价',
          key: 'purchase_price',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.purchase_price ? '￥ ' + row.purchase_price : '-')
        },
        { title: '退货单价', slot: 'price', align: 'center' },
        { title: '合计', slot: 'total_price', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' }
      ],
      customRules: {
        express_code: [{ required: false, message: '请选择物流公司', trigger: 'blur' }],
        express_no: [{ required: false, message: '请输入快递单号', trigger: 'blur' }],
        sender_name: [{ required: false, message: '请输入客户姓名', trigger: 'change' }],
        sender_mobile: [
          { required: false, message: '请输入客户手机号', trigger: 'change', pattern: /^(?:(?:\+|00)86)?1[3-9]\d{9}$/ }
        ],
        sender_county: [{ required: false, message: '请选择收货地址', trigger: 'change' }],
        sender_address: [{ required: false, message: '请选择收货地址', trigger: 'change' }]
      },
      product_list: [], // 商品数据
      // 选择商品
      selectedAddress: [], //用于回显的地址
      selectOrderList: [], //选中的销售单号、用于回显
      expressList: [],
      isJd: false,
      order_amount: 0, // 当前订单的交易总金额
      return_amount: 0 // 当前订单的已退总金额
    };
  },

  computed: {
    getTotalAmount() {
      return this.product_list.reduce((pre, cur) => {
        console.log('-> %c pre,cur  === %o', 'font-size: 15px;color: green;', pre, cur);
        return $operator.add(pre, cur.total_price);
      }, 0);
    },
    getExpressList() {
      if (this.isJd) {
        return this.expressList.filter(item => item.code === 'JD');
      } else {
        return this.expressList;
      }
    }
  },

  watch: {
    visible: {
      immediate: true,
      handler(val) {
        if (val && this.orderId) {
          this.getSalesReturnOrderInfo();
        }
      }
    }
  },

  created() {
    this.getLogisticsCompany();
  },

  mounted() {},

  methods: {
    selectChange(e, node) {
      // poppver的ref
      let nodeList = ['selectSaleOrder', 'selectWarehouse'];
      // select date 的ref
      let selectNodeList = ['refund-time'];
      if (e) {
        let hideList = nodeList.filter(item => item !== node);
        hideList.forEach(item => {
          this.$refs[item] && (this.$refs[item].showPop = false);
        });
        selectNodeList.forEach(item => {
          this.$refs[item] && (this.$refs[item].visible = false);
        });
      }
    },
    //选择订单
    selectOrder(data) {
      if (!S.isEmptyObject(data)) {
        console.log('-> %c data  === %o', 'font-size: 15px;color: green;', data);
        this.selectOrderList = [data.code];
        console.log('-> %c this.selectOrderList  === %o', 'font-size: 15px;color: green;', this.selectOrderList);
        const warehouse_info = data.warehouse_info;
        this.formData.warehouse_code = warehouse_info.warehouse_code;
        this.formData.warehouse_name = warehouse_info.warehouse_name;
        this.selectWarehouseList = [{ code: this.formData.warehouse_code, name: this.formData.warehouse_name }];
        this.isJd = warehouse_info.is_jd === '1';
        this.formData.cg_name = data.cg_name;

        this.formData.order_code = data.code;
        this.getErpOrderProductDetail({ code: data.code, is_return: 1 });
        this.$nextTick(() => {
          this.dynamicValidate(warehouse_info.is_jd === '1');
          this.$refs.salesOrderForm.validateField('order_code');
        });
        console.log(
          '-> %c this.formDataValidateRules  === %o',
          'font-size: 15px;color: green;',
          this.formDataValidateRules
        );
      }
    },
    // 动态校验发货信息
    dynamicValidate(isRequired) {
      this.isJd = isRequired;
      for (const rule in this.customRules) {
        const ruleInfo = this.customRules[rule];
        this.$set(this.customRules, rule, [{ ...ruleInfo[0], required: isRequired }]);
      }
      if (!isRequired) {
        this.$nextTick(() => {
          this.$refs.salesOrderForm.validateField('express_code');
          this.$refs.salesOrderForm.validateField('express_no');
        });
      }
    },
    //订单回显
    getErpOrderProductDetail(data = {}, isEdit = false) {
      this.$api.getErpOrderProductDetail(data).then(res => {
        console.log('-> %c res  === %o ', 'font-size: 15px;color: green;', res);
        const products = res.products;
        res.list.map(item => {
          item.p_qty = Number(item.p_qty);
          item.r_qty = Number(item.quantity);
          item.name = products[item.product_id].name;
          item.spec = products[item.product_id].spec;
          item.unit = products[item.product_id].unit;
          item.product_code = products[item.product_id].code;
          item.refundable_quantity = Number(item.refundable_quantity); // 可退数量
          item.purchase_price = item.price;
          item.price = this.orderId ? Number(item.edit_price) : Number(item.price); // 编辑时显示创建退货单的
          item.total_price = this.orderId
            ? $operator.multiply(item.edit_quantity, item.edit_price)
            : $operator.multiply(item.refundable_quantity, item.price);
          item.return_quantity = this.orderId ? Number(item.edit_quantity) : Number(item.refundable_quantity);
        });
        this.product_list = res.list;
        if (!isEdit) {
          const consignee_info = res.consignee_info;
          this.formData.sender_name = consignee_info.cg_name;
          this.formData.sender_mobile = consignee_info.cg_mobile;
          this.formData.sender_address = consignee_info.cg_address;
          this.formData.sender_province = consignee_info.cg_province;
          this.formData.sender_city = consignee_info.cg_city;
          this.formData.sender_county = consignee_info.cg_county;
          this.formData.express_code = consignee_info.express_code || '';
          this.formData.express_no = consignee_info.express_no || '';
          if (consignee_info.cg_county) {
            const province = TextToCode[consignee_info.cg_province];
            this.selectedAddress = [
              province.code,
              province[consignee_info.cg_city].code,
              province[consignee_info.cg_city][consignee_info.cg_county].code
            ];
          } else {
            this.selectedAddress = [];
          }
        }
        this.order_amount = Number(res.order_amount) || 0;
        this.return_amount = Number(res.return_amount) || 0;
        console.log('-> %c this.product_list  === %o', 'font-size: 15px;color: green;', this.product_list);
      });
    },
    //改变单价
    changeUnitPrice(val, index) {
      if (!val) {
        val = 0;
      }
      this.product_list[index].price = val;
      this.calcPrice(index);
    },
    //改变单价
    changeUnitNum(val, index) {
      if (!val) {
        this.product_list[index].return_quantity = 0;
      }
      this.product_list[index].return_quantity = val;
      this.calcPrice(index);
    },

    // 去零
    removeZero(val, key, index) {
      if (Number(val.target.value) == 0) {
        this.product_list[index].key = null;
      }
    },
    // 计算价格
    calcPrice(index) {
      const { price, return_quantity } = this.product_list[index];
      console.log('=>(EditReturnOrderDialog.vue:294) this.product_list[index]', this.product_list[index]);
      console.log('=>(EditReturnOrderDialog.vue:295) price && return_quantity', price);
      console.log('=>(EditReturnOrderDialog.vue:297) && return_quantity', return_quantity);
      if (return_quantity) {
        this.product_list[index].total_price = $operator.multiply(price, return_quantity);
      }
    },

    // 校验产品是否存在数量，单价，折扣未填写的情况
    validProductCalc() {
      let isUnValid = false;
      if (!this.product_list.length) {
        this.$Message.error('请选择商品');
        isUnValid = true;
        return true;
      }
      let prodNameList = [];
      this.product_list &&
        this.product_list.forEach(item => {
          if (!item.return_quantity) {
            prodNameList.push(item.name);
          }
        });
      if (prodNameList.length > 0) {
        let nameList = prodNameList.join('、');
        this.$Message.error(`商品【${nameList}】退货数量不得为0`);
        isUnValid = true;
        return true;
      }
      // console.log('-> this.getTotalAmount, this.return_amount', this.getTotalAmount, this.return_amount);
      // let returnTotal = $operator.add(this.getTotalAmount, this.return_amount); // 当前退货金额 + 当前表单退货总金额
      // console.log('-> returnTotal', returnTotal);
      // console.log('-> this.order_amount', this.order_amount);
      // if (returnTotal > this.order_amount) {
      //   this.$Message.error('可退金额不足');
      //   isUnValid = true;
      //   return true;
      // }
      return isUnValid;
    },

    // 添加商品
    addProductE() {
      this.editProductVisible = true;
    },
    // 删除商品
    deleteProduct(index) {
      this.product_list.splice(index, 1);
    },
    // 获取勾选的商品
    // 创建/编辑
    confirmHandle() {
      console.log(this.formData);
      // const {deal_time} = this.formData
      this.$refs.salesOrderForm.validate(valid => {
        console.log('-> %c valid  === %o ', 'font-size: 15px;color: green;', valid);
        console.log(this.formData);
        if (valid) {
          // 商品计算未完成,不允许提交
          if (this.validProductCalc()) {
            return;
          }
          this.editSalesReturnOrder();
        } else {
          this.$Message.error('请填写完整');
        }
      });
    },

    // 选中仓库
    selectWarehouse(val) {
      console.log('-> val', val);
      this.selectWarehouseList = [];
      this.formData.warehouse_code = '';
      if (!S.isEmptyObject(val)) {
        this.selectWarehouseList.push(val);
        this.formData.warehouse_code = val.code;
      }
      if (val.is_jd === '1' && this.formData.express_code !== 'JD') {
        this.formData.express_code = '';
        this.formData.express_no = '';
      }
      this.dynamicValidate(val.is_jd === '1');
      this.$refs.salesOrderForm.validateField('warehouse_code');
    },

    supplierChange(val) {
      console.log('val', val);
    },

    // 处理商品的价格数据，作为参数
    handlerProductDetails() {
      console.log('=>(EditReturnOrderDialog.vue:341) this.product_list', this.product_list);
      let detail = this.product_list.map(item => {
        return {
          product_code: item.product_code,
          quantity: item.return_quantity,
          price: item.price,
          total_price: item.total_price
        };
      });
      console.log('=>(EditReturnOrderDialog.vue:341) detail', detail);
      return detail;
    },

    // api - 创建采购订单
    editSalesReturnOrder() {
      this.submitLoading = true;
      let params = {
        ...this.formData,
        id: this.orderId,
        detail: this.handlerProductDetails()
      };
      console.log('=>(EditReturnOrderDialog.vue:356) params', params);
      let apiName = this.orderId ? 'editSalesReturnOrder' : 'createSalesReturnOrder';
      this.$api[apiName](params)
        .then(res => {
          console.log('-> %c res  === %o ', 'font-size: 15px;color: green;', res);
          this.$Message.success(`${this.orderId ? '编辑' : '创建'}销售退款单成功`);
          this.cancelHandle();
          this.$emit('refresh');
          this.submitLoading = false;
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
          this.submitLoading = false;
        });
    },

    // 关闭弹窗,清除数据
    cancelHandle() {
      this.formData = { ...initFormData };
      this.selectedAddress = [];
      this.$emit('update:visible', false);
      this.$refs.salesOrderForm.resetFields();
      this.product_list = [];
      this.isJd = false;
    },
    getSalesReturnOrderInfo() {
      this.$api.getErpReturnOrderInfo({ id: this.orderId }).then(res => {
        console.log('-> %c res  === %o', 'font-size: 15px;color: green;', res);
        this.formData.id = res.id;
        this.formData.code = res.code;
        this.formData.order_code = res.order_code;
        this.formData.plan_date = res.plan_date;
        this.formData.remark = res.remark;
        this.formData.reason = res.reason;
        this.formData.warehouse_code = res.warehouse.code;
        this.formData.warehouse_name = res.warehouse.name;
        this.selectWarehouseList = [{ code: this.formData.warehouse_code, name: this.formData.warehouse_name }]; // 回显仓库名称
        this.formData.cg_name = res.cg_name;
        this.formData.sender_name = res.sender_name;
        this.formData.sender_mobile = res.sender_mobile;
        this.formData.sender_address = res.sender_address;
        this.formData.sender_province = res.sender_province;
        this.formData.sender_city = res.sender_city;
        this.formData.sender_county = res.sender_county;
        this.formData.express_code = res.express_code;
        this.formData.express_no = res.express_no;
        if (res.sender_county) {
          const province = TextToCode[res.sender_province];
          this.selectedAddress = [
            province.code,
            province[res.sender_city].code,
            province[res.sender_city][res.sender_county].code
          ];
        } else {
          this.selectedAddress = [];
        }

        console.log('-> %c this.selectedAddress  === %o', 'font-size: 15px;color: green;', this.selectedAddress);
        if (res.warehouse && res.warehouse.is_jd === '1') {
          this.dynamicValidate(res.warehouse.is_jd === '1');
        }
        // this.getReturnOrderProductDetail()
        this.getErpOrderProductDetail(
          { code: this.formData.order_code, order_return_code: this.formData.code, is_return: 1, is_edit: 1 },
          true
        );
      });
    },
    // todo 未做报废逻辑，不使用该接口进行数据回显
    getReturnOrderProductDetail() {
      this.$api.getErpReturnOrderProductDetail({ id: this.orderId }).then(res => {
        console.log('-> %c res  === %o', 'font-size: 15px;color: green;', res);
        const products = res.products;
        res.list.map(item => {
          item.p_qty = Number(item.p_qty);
          item.return_quantity = Number(item.quantity);
          item.name = products[item.product_id].name;
          item.spec = products[item.product_id].spec;
          item.unit = products[item.product_id].unit;
          item.price = Number(item.price);
          item.purchase_price = item.price;
          item.total_price = $operator.multiply(item.quantity, item.price);
        });
        this.product_list = res.list;
      });
    },
    regionChange(address) {
      console.log('-> %c address  === %o ', 'font-size: 15px;color: green;', address);
      if (address.length) {
        const province = {
          name: CodeToText[address[0]],
          code: address[0]
        };
        const city = {
          name: CodeToText[address[1]],
          code: address[1]
        };
        const county = {
          name: CodeToText[address[2]],
          code: address[2]
        };
        console.log(province, city, county);
        this.formData.sender_province = province.name || '';
        this.formData.sender_city = city.name || '';
        this.formData.sender_county = county.name || '';
      } else {
        this.formData.sender_province = '';
        this.formData.sender_city = '';
        this.formData.sender_county = '';
      }
    },
    getLogisticsCompany() {
      this.$api.getErpOutboundExpress().then(res => {
        console.log('-> %c res  === %o', 'font-size: 15px;color: green;', res);
        this.expressList = res.express;
      });
    }
  },

  destroyed() {}
};
</script>

<style scoped lang="less">
@import url('../../common/modal.less');
</style>
