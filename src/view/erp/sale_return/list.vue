<template>
  <div class="purchase-list-wrapper">
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch" label-colon>
      <Row>
        <Col>
          <FormItem>
            <Input v-model="queryFormData.q" placeholder="销售退货单编号/销售单编号" clearable />
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Select v-model="queryFormData.audit_status" style="width: 180px" placeholder="请选择审核状态" clearable>
              <Option v-for="item in auditStatus" :value="item.code" :key="item.code">{{ item.name }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Select v-model="queryFormData.push_status" style="width: 180px" placeholder="推送状态" clearable>
              <Option v-for="item in pushStatus" :value="item.code" :key="item.code">{{ item.name }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Input v-model="queryFormData.apply_code" placeholder="请输入售后单号" clearable />
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Input v-model="queryFormData.pur_order_code" placeholder="请输入采购单号" clearable />
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col>
          <FormItem style="text-align: left">
            <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
            <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
            <!-- <Button type="primary" class="mr10" @click="createOrder">新建销售退货单</Button> -->
          </FormItem>
        </Col>
      </Row>
    </Form>

    <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 295">
      <template slot-scope="{ row }" slot="return_code">
        {{ row.code }}
      </template>

      <template slot-scope="{ row }" slot="order_code">
        <KLink
          v-if="row.order_code"
          :to="{ path: '/erp/sale_order/detail', query: { code: row.order_code } }"
          target="_blank"
          >{{ row.order_code }}</KLink
        >
        <span v-else>-</span>
      </template>

      <template slot-scope="{ row }" slot="total_price"> ￥{{ row.total_price }} </template>
      <template slot-scope="{ row }" slot="plan_date">
        {{ row.plan_date || '-' }}
      </template>
      <template slot-scope="{ row }" slot="cg_name">
        {{ row.cg_name || '-' }}
      </template>
      <template slot-scope="{ row }" slot="remark">
        {{ row.remark || '-' }}
      </template>
      <template slot-scope="{ row }" slot="apply_code">
        {{ row.apply_code || '-' }}
      </template>
      <template slot-scope="{ row }" slot="pur_order_code">
        {{ row.pur_order_code || '-' }}
      </template>
      <template slot-scope="{ row }" slot="audit_status_text">
        <!--        <span :style="getAuditColor(row.audit_status)">{{ row.audit_status_text }}</span>-->
        <status-text :status="row.audit_status"
          ><span>{{ row.audit_status_text }}</span></status-text
        >
      </template>
      <template slot-scope="{ row }" slot="create_time">
        {{ row.create_time | date_format }}
      </template>
      <template slot-scope="{ row }" slot="update_time">
        {{ row.update_time | date_format }}
      </template>
      <template slot-scope="{ row }" slot="push_status">
        <span v-if="row.push_status === '4'"
          >{{ row.push_status_text }}
          <Tooltip placement="bottom" max-width="200" :content="row.hangup_text">
            <Icon type="md-help-circle" style="font-size: 16px" class="cursor" />
          </Tooltip>
        </span>
        <span v-else>{{ row.push_status_text }}</span>
      </template>
      <template slot-scope="{ row }" slot="action">
        <a v-if="row.audit_status == '70'" class="mr10" @click="editReturnOrder(row)">编辑</a>
        <a @click="goDetail(row)">详情</a>
      </template>
    </Table>

    <div class="block_20"></div>
    <KPage
      :total="total"
      :page-size="+queryFormData.pageSize"
      :current="+queryFormData.page"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
      style="text-align: center"
    />
    <EditReturnOrderDialog :order-id="orderId" :visible.sync="editVisible" @refresh="onSearch"></EditReturnOrderDialog>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'utils/util';
import EditReturnOrderDialog from './compontents/EditReturnOrderDialog';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  q: '',
  push_status: '', //推送状态
  audit_status: '', //审核状态
};

export default {
  name: 'sale-return-list',
  mixins: [search],
  components: { EditReturnOrderDialog },
  data() {
    return {
      apiName: 'getErpReturnOrderList',
      queryFormData: {
        ...init_query_form_data,
      },
      pushStatus: [
        { name: '不推送', code: '0' },
        { name: '待推送', code: '1' },
        { name: '挂起', code: '4' },
        { name: '已推送', code: '9' },
      ],
      tableCols: [
        { title: '销售退货单编号', slot: 'return_code', align: 'center', width: 110 },
        { title: '销售单编号', slot: 'order_code', width: 165, align: 'center' },
        { title: '客户姓名', slot: 'cg_name', width: 150, align: 'center' },
        { title: '退款金额', slot: 'total_price', width: 90, align: 'center' },
        { title: '退货时间', slot: 'plan_date', width: 150, align: 'center' },
        { title: '备注', slot: 'remark', width: 120, align: 'center' },
        { title: '审核状态', width: 80, slot: 'audit_status_text' },
        { title: '入库状态', key: 'stock_status', width: 100, align: 'center' },
        { title: '推送状态', slot: 'push_status', width: 150, align: 'center', width: 80 },
        {
          title: '创建人',
          key: 'operator',
          align: 'center',
          width: 100,
          render: (h, { row }) => h('span', {}, row.operator || '-'),
        },
        { title: '售后单号', slot: 'apply_code', width: 140, align: 'center' },
        { title: '采购单号', slot: 'pur_order_code', width: 180, align: 'center' },
        { title: '创建时间', slot: 'create_time', align: 'center', width: 140 },
        { title: '更新时间', slot: 'update_time', align: 'center', width: 140 },
        { title: '操作', slot: 'action', align: 'center', width: 100, fixed: 'right' },
        // { title: '负责人', key: 'operator', align: 'center' },
      ],
      list_count: {},
      auditStatus: [
        { code: '10', name: '审核中' },
        { code: '90', name: '审核完成' },
        { code: '70', name: '已拒绝' },
      ], //审核状态选项
      orderId: '', //当前编辑的销售订单ID
      editVisible: false, ///编辑弹窗flag
    };
  },
  computed: {
    getAuditColor() {
      return status => {
        switch (status) {
          case '10':
            return { color: '#2db7f5' };
          case '90':
            return { color: '#19be6b' };
          case '70':
            return { color: '#ed4014' };
        }
      };
    },
  },
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    // 编辑销售退货单
    editReturnOrder(row) {
      console.log('-> %c row  === %o', 'font-size: 15px;color: green;', row);
      this.orderId = row.id;
      this.editVisible = true;
    },
    // 新建销售退货单
    createOrder() {
      this.orderId = '';
      this.editVisible = true;
    },
    refresh() {
      this.loadList();
    },

    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },
    goDetail(row) {
      this.$router.push({
        path: '/erp/sale_return/detail',
        query: {
          id: row.id,
        },
      });
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  },
};
</script>

<style scoped lang="less">
.supplier-list-wrapper {
}
</style>
