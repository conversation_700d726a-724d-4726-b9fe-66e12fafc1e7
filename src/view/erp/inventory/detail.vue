<template>
  <div style="padding-bottom: 30px">
    <Form ref="form" :model="form" :rules="rules" :label-width="80" :disabled="disabledBool">
      <Row>
        <Col span="6">
          <FormItem label="盘点类型" prop="preparedPerson" :required="true">
            <Select :disabled="!!id" v-model="form.type" placeholder="请选择盘点类型" clearable @on-change="changeType">
              <Option v-for="item in inventory_type_list" :value="item.id" :key="item.kw">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col span="6">
          <FormItem label="盘点仓库" :required="true">
            <Select
              v-model="form.warehouse_code"
              placeholder="请选择仓库"
              clearable
              @on-change="changeWare"
              :disabled="!form.type"
            >
              <Option v-for="item in warehouseList" :value="item.code" :key="item.code">{{ item.name }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col span="6">
          <FormItem label="盘点时间" prop="stocktake_at">
            <DatePicker
              format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              :value="form.stocktake_at"
              :options="dateOptions"
              @on-change="changeTime"
              @on-ok="validateTimePicker"
              :clearable="false"
              style="width: 100%"
              :disabled="timeShow || !!tablePageList.length"
            ></DatePicker>
          </FormItem>
        </Col>
        <Col span="6">
          <FormItem label="制单人" prop="preparedPerson">
            <Input type="text" :value="id ? form.operator : form.preparedPerson" placeholder="" disabled />
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="8">
          <FormItem label="盘点名称" :required="true">
            <Input type="text" v-model="form.title" placeholder="请输入盘点名称" maxlength="30" show-word-limit />
          </FormItem>
        </Col>
        <Col span="16">
          <FormItem label="盘点备注">
            <Input
              type="textarea"
              v-model="form.remark"
              placeholder="请输入盘点备注"
              :autosize="true"
              maxlength="100"
              show-word-limit
            />
          </FormItem>
        </Col>
      </Row>

      <Row>
        <Col span="16" v-if="form.status === '2'">
          <FormItem label="驳回原因">
            <p style="color: red; word-wrap: break-word">{{ form.audit_reason }}</p>
          </FormItem>
        </Col>
      </Row>
      <div class="block-header flex flex-item-align flex-item-between" style="margin-top: 0">
        <div>盘点明细</div>
        <div class="flex" v-if="!disabledBool">
          <a class="space6" @click="batchCount">批量盘点</a>
          <SelectProductPopper
            class="flex flex-item-align"
            ref="selectProduct"
            @selectBatchList="selectedList"
            title="选择产品"
            :warehouse_code="form.warehouse_code"
            :defaultParams="{
              warehouse_code: form.warehouse_code,
              is_query_history_stock: form.type,
              history_time: form.stocktake_at,
              status: 'ENABLE'
            }"
            :batchCodeList="[...tableList]"
          >
            <a style="font-weight: normal; font-size: 12px" class="space6" @click="addCount">添加盘点产品</a>
          </SelectProductPopper>
        </div>
      </div>

      <Table
        :columns="tableCols"
        :data="showTableList"
        size="small"
        border
        stripe
        :loading="tableLoading"
        :height="$store.state.app.clientHeight - 440"
      >
        <!-- 序号 -->
        <template slot-scope="{ row }" slot="index">
          {{ getTableIndex(row) }}
        </template>
        <!-- 产品编号 -->
        <template slot-scope="{ row }" slot="product_code">
          {{ row?.product_code || '-' }}
        </template>

        <!-- 产品名称 -->
        <template slot-scope="{ row }" slot="product_name">
          {{ row?.product_name || '-' }}
        </template>
        <!-- 规格 -->
        <template slot-scope="{ row }" slot="spec">
          {{ row?.spec || '-' }}
        </template>
        <!-- 单位 -->
        <template slot-scope="{ row }" slot="unit">
          {{ row?.unit || '-' }}
        </template>
        <!-- 产地 -->
        <!-- <template slot-scope="{ row }" slot="prov_name"> -->
        <!--   {{ row?.prov_name || '-' }} -->
        <!-- </template> -->
        <!-- 供应商 -->
        <template slot-scope="{ row }" slot="supplier_name">
          {{ row?.supplier_name || '-' }}
        </template>
        <!-- 批号 -->
        <!-- <template slot-scope="{ row }" slot="batch_code"> -->
        <!--   {{ row?.batch_code || '-' }} -->
        <!-- </template> -->
        <!-- 账面库存 -->
        <template slot-scope="{ row }" slot="est_stock_num">
          {{ row?.est_stock_num || '-' }}
        </template>
        <!-- 实际库存 -->
        <template slot-scope="{ row }" slot="real_stock_num">
          <InputNumber :min="0" :value="getNumValue(row)" @input="changeRealStockNum(row, $event)" />
        </template>
        <!-- 盈亏数量 -->
        <template slot-scope="{ row, index }" slot="diff_num">
          <div>
            <p v-if="getProfitPrice(row, index) === 0" style="margin-right: 12px" class="flex flex-item-align">
              <svg-icon style="font-size: 20px" iconClass="flat"></svg-icon>
              <span style="margin-left: 4px">{{ getProfitPrice(row, index) }}</span>
            </p>
            <p v-if="getProfitPrice(row, index) > 0" class="flex flex-item-align">
              <svg-icon style="font-size: 20px" iconClass="profit"></svg-icon>
              <span style="margin-left: 4px">{{ getProfitPrice(row, index) }}</span>
            </p>
            <p v-if="getProfitPrice(row, index) < 0" class="flex flex-item-align">
              <svg-icon style="font-size: 20px" iconClass="deficit"></svg-icon>
              <span style="margin-left: 4px">{{ getProfitPrice(row, index) }}</span>
            </p>
            <span v-if="getProfitPrice(row, index) === null">-</span>
          </div>
        </template>
        <!-- 平均成本单价 -->
        <!-- <template slot-scope="{ row }" slot="real_price"> -->
        <!--   {{ row?.real_price || '-' }} -->
        <!-- </template> -->
        <!-- 盈亏金额 -->
        <!-- <template slot-scope="{ row, index }" slot="diff_amount"> -->
        <!--   <!-- {{ row?.diff_amount || '-' }} -->
        -->
        <!--   {{ getDiffAmount(row, index) }} -->
        <!-- </template> -->

        <template slot-scope="{ row }" slot="operation">
          <span v-if="disabledBool">
            <a style="color: #d7d9de">删除</a>
          </span>
          <span v-else>
            <a @click="delListItemHandle(row)">删除</a>
          </span>
        </template>
      </Table>
      <div class="flex flex-item-between" style="margin-top: 10px">
        <Page
          :total="tableFormData.total"
          :page-size="tableFormData.pageSize"
          :current="tableFormData.page"
          :page-size-opts="[10, 20, 50, 80, 100]"
          @on-change="onPageChange"
          @on-page-size-change="onPageSizeChange"
          show-sizer
          show-elevator
          show-total
          transfer
          :disabled="false"
        >
        </Page>

        <div>
          <!-- 累计盈亏金额: <span style="color: red">{{ getTotalAmount() }}</span> -->
        </div>
      </div>
    </Form>

    <div class="fixed-bottom-wrapper">
      <Button @click="back">返回</Button>
      <span v-if="!look">
        <span v-if="form.status === '0'" v-eleControl="'EGW6xpXyyM'">
          <Button style="margin: 0 20px" type="error" @click="showRefuseModal">审核驳回 </Button>
          <Button type="primary" @click="passCheck">审核通过 </Button>
        </span>
        <span v-if="!id || form.status === '2'">
          <Button v-if="id" type="error" style="margin-left: 20px" @click="invalid">作废</Button>
          <Button v-if="tableList.length" style="margin-left: 20px" @click="clear">清空</Button>
          <Button v-if="id" type="primary" style="margin-left: 20px" @click="update">提交</Button>
          <Button v-else type="primary" style="margin-left: 20px" @click="submit" :loading="uploading">提交</Button>
        </span>
      </span>
    </div>

    <KBatchUpload
      modalTitle="批量盘点"
      :import-visible.sync="importVisible"
      :excelKeyMap="excelKeyMap"
      :downloadApiName="downloadApiName"
      :downloadTextList="downloadTextList"
      :reportColumns="reportColumns"
      :chunkNum="20"
      :importApiName="importApiName"
      :restParams="{ warehouse_code: form.warehouse_code, type: form.type, stocktake_at: form.stocktake_at }"
      @emitSuccessList="emitSuccessList"
    ></KBatchUpload>

    <refuse-reason-modal :visible.sync="refuseModalVisible" :auditSalesOrder="review"></refuse-reason-modal>
    <refuse-reason-modal
      :isAudit="false"
      :visible.sync="auditModalVisible"
      :invalidSalesOrder="invalidFunc"
    ></refuse-reason-modal>
  </div>
</template>

<script>
import * as runtime from '@/utils/runtime'; // Runtime information
import moment from 'moment';
import KExcelUpload from '@/components/k-excel-upload/inventoryExcel.vue';
import excelBatchUpload from '@/mixins/excelBatchUpload';
import downloadExceL from '@/mixins/downloadExcel';
import SelectProductPopper from './components/productSelect.vue';
import KBatchUpload from '@/components/BatchUpload/KBatchUpload';
import RefuseReasonModal from '@/components/RefuseReasonModal';
import { cloneDeep, chunk } from 'lodash';
import { $operator } from '@/utils/operation';
import S from 'utils/util';

export default {
  name: 'detail',
  components: {
    KExcelUpload,
    SelectProductPopper,
    KBatchUpload,
    RefuseReasonModal,
  },
  mixins: [excelBatchUpload, downloadExceL],
  data() {
    return {
      form: {
        stocktake_at: moment().format('YYYY-MM-DD HH:mm:ss'),
        preparedPerson: runtime.getUser().name,
      },
      id: '',
      tableFormData: {
        page: 1,
        pageSize: 20,
        total: 0,
      },
      rules: {},
      dateOptions: {
        disabledDate: date => {
          if (this.lastTime?.is_30_days === '1') {
            let lastDate = this.lastTime.stocktake_at;
            function getDayDisble() {
              if (moment(date).valueOf() >= moment(lastDate).valueOf()) {
                return false;
              } else {
                if (!!(moment(date).valueOf() === moment(lastDate).startOf('days').valueOf())) {
                  return false;
                }

                if (!!(moment(date).startOf('days').valueOf() === moment(lastDate).startOf('days').valueOf())) {
                  return false;
                }

                return true;
              }
            }
            return (date && date.valueOf() >= Date.now()) || getDayDisble();
          }
          let beforeDays = -30;
          return (date && date.valueOf() > Date.now()) || date.valueOf() < moment().add(beforeDays, 'd').valueOf();
        },
      },
      warehouseList: [],
      excelUploadLoading: false, // 数据上传的loading
      exportLoading: false,
      importVisible: false, // 导入弹窗
      hasHandleExcelList: [], // 准备导入的数据
      tableList: [],
      tablePageList: [], // 分页表格数据
      isImportSuccess: false, // 是否导入成功
      refuseModalVisible: false, // 审核驳回弹窗
      auditModalVisible: false,
      excelUploadApiName: 'createErpStockList',
      downloadTextList: [
        { label: '通用型盘点模板', apiName: 'downLoadErpStockTemplate', downParams: { warehouse_code: '' } },
        // { label: '供应商盘点模板', disabled: true },
        // { label: '货区货架盘点模板', disabled: true }
      ],
      tableCols: [
        { title: '序号', slot: 'index' },
        { title: '产品编号', slot: 'product_code' },
        { title: '产品名称', slot: 'product_name' },
        { title: '规格', slot: 'spec' },
        { title: '单位', slot: 'unit' },
        // { title: '产地', slot: 'prov_name' },
        { title: '供应商', slot: 'supplier_name' },
        // { title: '批号', slot: 'batch_code' },
        { title: '账面库存', slot: 'est_stock_num' },
        { title: '实际库存', slot: 'real_stock_num', width: 100 },
        { title: '盈亏数量', slot: 'diff_num' },
        // { title: '平均成本单价', slot: 'real_price', width: 100 },
        // { title: '盈亏金额', slot: 'diff_amount' },
        { title: '操作', slot: 'operation' },
      ],
      tableLoading: false,
      downloadApiName: 'downLoadErpStockTemplate', // 导出模板API
      importApiName: 'stockImportDetec', // 导入模板API
      // validateApiName: 'validateProOutOrder',// 导入模板API
      excelKeyMap: {
        产品名称: { key: 'product_name' },
        产品编码: { key: 'product_code' },
        规格: { key: 'spec' },
        // 产地: { key: 'prov_name' },
        供应商: { key: 'supplier_name' },
        // 批号: { key: 'batch_code' },
        账面库存: { key: 'est_stock_num' },
        实际库存: { key: 'real_stock_num', required: true },
      },
      reportColumns: [
        { type: 'index', title: '序号', width: 60, align: 'center' },
        { title: '产品名称', key: 'product_name', align: 'center' },
        { title: '失败原因', key: 'fail_msg', align: 'center' },
      ],
      uploading: false,
      inventory_type_list: [],
      lastTime: {},
    };
  },
  created() {
    this.getErpWarehousePullList();
    this.getWarehouseOptions();
    let id = this.$route.query.id;
    let look = this.$route.query.look;

    this.look = Boolean(look);

    if (id) {
      this.id = id;
      this.getErpStockDetail();
    }
  },
  computed: {
    isActiveBtn() {
      if (this.hasHandleExcelList.length > 0 && !this.isImportSuccess) {
        return false;
      } else {
        return true;
      }
    },
    getProfitPrice() {
      return function (row) {
        let diff_num = null;
        let index = this.tableList.findIndex(item => item.id === row.id);
        // console.log(row.real_stock_num);

        if (row.real_stock_num !== null) {
          let num1 = Number(row.real_stock_num);
          let num2 = Number(row.est_stock_num);

          if (!isNaN($operator.subtract(num1, num2))) {
            diff_num = $operator.subtract(num1, num2);
          }
          this.$set(this.tableList[index], 'diff_num', diff_num);
          return this.tableList[index].diff_num;
        } else {
          this.$set(this.tableList[index], 'diff_num', diff_num);
          return null;
        }
      };
    },
    // getDiffAmount() {
    //   return function (row) {
    //     let num1 = Number(row.real_price);
    //     let num2 = Number(row.diff_num);
    //     console.log(num1, num2);
    //     console.log(isNaN($operator.subtract(num1, num2)));
    //
    //     let diff_amount = 0;
    //     if (!isNaN($operator.multiply(num1, num2))) {
    //       diff_amount = $operator.multiply(num1, num2);
    //     }
    //
    //     let index = this.tableList.findIndex(item => item.id === row.id);
    //     this.$set(this.tableList[index], 'diff_amount', diff_amount);
    //     return this.tableList[index].diff_amount;
    //   };
    // },
    // getTotalAmount() {
    //   return function () {
    //     let total = 0;
    //     this.tableList.forEach(item => {
    //       if (item.diff_amount) {
    //         total = $operator.add(total, item.diff_amount);
    //       }
    //     });
    //     return total;
    //   };
    // },
    showTableList() {
      let page = this.tableFormData.page;
      return this.tablePageList[page - 1];
    },
    getTableIndex() {
      return function (row) {
        let index = this.tableList.findIndex(item => item.id === row.id);
        return index + 1;
      };
    },
    disabledBool() {
      // 详情和待审核返回true
      return this.look || this.form.status === '0';
    },
    timeShow() {
      // 选择跨月盘点时，允许选择最近30天内的某一天进行盘点。其他时间无法选中
      // 选择月末盘点时，盘点时间为当前时间，且盘点时间输入框禁用。
      // 切换盘点类型，重置盘点时间为初始状态（未选中）
      if (!this.form.type || this.form.type === '1') {
        return true;
      }

      return false;
    },
  },
  watch: {
    importVisible(val) {
      if (!val) {
        this.hasHandleExcelList = [];
        this.isImportSuccess = false;
        this._initUploadData();
      }
    },
    hasHandleExcelList(val) {
      if (val) {
        // 如果重新选择了表格,将参数置为false,视为重新上传
        this.isImportSuccess = false;
        this._initUploadData();
      }
    },
  },
  methods: {
    changeTime(time) {
      this.form.stocktake_at = time;
    },
    getLastTime() {
      this.$api.getLaststocktaketime({ warehouse_code: this.form.warehouse_code }).then(res => {
        this.lastTime = res;
      });
    },
    getWarehouseOptions() {
      this.$api.getWarehouseOptions().then(res => {
        this.inventory_type_list = S.descToArrHandle(res.typeDesc);
      });
    },
    getErpStockDetail() {
      this.$api.getErpStockDetail({ id: this.id }).then(res => {
        let form = cloneDeep(res);
        // this.tableList = form.product_list;
        this.afterSetTableList(form.product_list);
        delete form.product_list;
        form.stocktake_at = form.stocktake_at;
        this.form = { ...this.form, ...form };
        this.getLastTime();
      });
    },
    // api - 仓库列表
    getErpWarehousePullList() {
      this.$api.getErpWarehouseList({ pageSize: 999 }).then(res => {
        this.warehouseList = res.list;
      });
    },
    emitSuccessList(hasHandleExcelList) {
      let hasHandleExcelListCopy = cloneDeep(hasHandleExcelList);
      hasHandleExcelListCopy.forEach(item => {
        if (item.real_stock_num === '') {
          item.real_stock_num = null;
        } else {
          item.real_stock_num = Number(item.real_stock_num);
        }
      });
      this.hasHandleExcelList = hasHandleExcelListCopy;
      this.afterSetTableList(cloneDeep(hasHandleExcelListCopy));
    },
    // api-上传处理后的excel数据
    batchCount() {
      if (!this.form.warehouse_code) {
        this.$Message.error('请选择盘点仓库');
        return;
      }

      this.downloadTextList[0].downParams = {
        warehouse_code: this.form.warehouse_code,
        history_time: this.form.stocktake_at,
        is_query_history_stock: this.form.type,
      };
      this.importVisible = true;
    },
    addCount() {
      if (!this.form.warehouse_code) {
        this.$Message.error('请选择盘点仓库');
        return;
      }
    },
    back() {
      if (this.id) {
        this.$router.back();
        return;
      }
      let content = '';
      let checkJson = ['warehouse_code', 'title', 'remark'];
      let checkBool = false;
      for (const key in this.form) {
        if (checkJson.includes(key) && this.form[key]) {
          checkBool = true;
          break;
        }
      }
      // console.log(checkBool);
      // console.log(cloneDeep(this.form));
      if (this.tableList.length > 0 || checkBool) {
        let title = '<p>当前盘点单已有数据，是否返回？</p><p>返回后数据将被清空</p>';
        this.$Modal.confirm({
          title,
          onOk: () => {
            this.$router.back();
          },
        });
      } else {
        this.$router.back();
      }
    },

    clear() {
      let title = '当前盘点单已有数据，是否清空？';
      this.$Modal.confirm({
        title,
        onOk: () => {
          this.tableList = [];
          this.tablePageList = [];
        },
      });
    },

    validate() {
      if (!this.form.type) {
        this.$Message.error('请选择盘点类型');
        return false;
      }

      if (!this.tableList.length) {
        this.$Message.error('请添加盘点产品');
        return false;
      }

      if (this.tableList.some(v => v.real_stock_num === null)) {
        this.$Message.error('请填写实际库存数量');
        return false;
      }

      if (!this.form.title) {
        this.$Message.error('请输入盘点名称');
        return false;
      }

      if (!this.validateTimePicker()) {
        return false;
      }

      return true;
    },
    validateTimePicker() {
      console.log(this.form.stocktake_at);
      // console.log(this.)
      if (this.lastTime?.is_30_days === '1') {
        let formPickerValue = moment(this.form.stocktake_at).valueOf();
        let lastPickerValue = moment(this.lastTime.stocktake_at).valueOf();
        if (formPickerValue < lastPickerValue) {
          this.$Message.error('盘点时间不能早于上次盘点时间');
          return false;
        }
        return true;
      }
      return true;
    },
    submit() {
      if (!this.validate()) {
        return;
      }

      let params = this.getFormParams();

      this.uploading = true;
      this.$api
        .createErpStockList(params)
        .then(res => {
          this.$Message.success('提交成功');
          this.$router.back();
          this.uploading = false;
        })
        .catch(err => {
          this.uploading = false;
        });
    },
    update() {
      if (!this.validate()) {
        return;
      }

      let params = this.getFormParams();
      params.id = this.id;

      this.$api
        .editErpStockList(params)
        .then(res => {
          this.$Message.success('提交成功');
          this.$router.back();
        })
        .catch(err => {
        });
    },

    getFormParams() {
      let params = {};
      let subJson = ['title', 'warehouse_code', 'remark', 'type', 'stocktake_at'];
      subJson.forEach(key => {
        params[key] = this.form[key];
      });
      params.product_list = this.getSubmitList(this.tableList);
      return params;
    },

    getSubmitList(list) {
      let arr = [];
      let subJson = [
        'id',
        'product_name',
        'product_code',
        'spec',
        'supplier_name',
        // 'batch_code',
        'est_stock_num',
        'real_stock_num',
        'unit',
        'diff_num',
        // 'real_price',
        // 'diff_amount',
      ];
      list.forEach(item => {
        let obj = {};
        subJson.forEach(key => {
          obj[key] = item[key];
        });
        arr.push(obj);
      });
      return arr;
    },

    selectedList(list) {
      let copyList = cloneDeep(list);
      let tableList = cloneDeep(this.tableList);

      // 导入的时候会有三个字段和列表提交的不一致
      const initKey = {
        product_name: 'name',
        product_code: 'code',
        spec: 'spec',
        unit: 'unit',
        est_stock_num: 'stock_total',
      };

      copyList.forEach(item => {
        Object.keys(initKey).forEach(key => {
          item[key] = item[initKey[key]];
          // delete item[initKey[key]];
        });
        item.real_stock_num = null;
      });

      // 添加前检测重复
      copyList.forEach((item, index) => {
        let findItem = tableList.find(tableItem => tableItem.id === item.id);
        if (findItem) {
          copyList.splice(index, 1, findItem);
        }
      });

      if (copyList.length) {
        this.$Message.success('添加成功');
      } else {
        this.$Message.info('未选择产品');
      }
      this.afterSetTableList([...copyList]);
    },
    afterSetTableList(tableData) {
      this.tableList = tableData;

      this.tableFormData.total = tableData.length;
      let pageSize = this.tableFormData.pageSize;
      let page = this.tableFormData.page;
      let list2Arr = chunk(tableData, pageSize);
      if (page > list2Arr.length && list2Arr.length > 0) {
        page = list2Arr.length;
        this.tableFormData.page = page;
      }
      this.tablePageList = list2Arr;
      // this.tableList = list2Arr[page - 1];
    },
    delListItemHandle(row) {
      let index = this.tableList.findIndex(listItem => listItem.id === row.id);
      this.tableList.splice(index, 1);
      this.afterSetTableList(this.tableList);
    },
    onPageChange(page, pageSize) {
      this.tableFormData.page = page;
      this.afterSetTableList(this.tableList);
    },
    onPageSizeChange(pageSize) {
      this.tableFormData.pageSize = pageSize;
      this.tableFormData.page = 1;
      this.afterSetTableList(this.tableList);
      // const copyList = cloneDeep(this.localList);
      // this.list = copyList.slice(0, pageSize);
    },
    changeRealStockNum(row, value) {
      let index = this.tableList.findIndex(item => item.id === row.id);
      this.tableList[index].real_stock_num = value;
    },
    getNumValue(row) {
      let index = this.tableList.findIndex(item => item.id === row.id);
      // this.tableList[index].real_stock_num = Number(value);
      // console.log(this.tableList[index].real_stock_num, 'this is value');
      // console.log(Number(this.tableList[index].real_stock_num));
      // return Number(this.tableList[index].real_stock_num);
      return this.tableList[index].real_stock_num;
    },
    // 审核驳回
    showRefuseModal() {
      this.refuseModalVisible = true;
    },
    invalid() {
      this.auditModalVisible = true;
    },
    invalidFunc(actions, reason) {
      console.log(reason);

      const params = {
        id: this.id,
        reason,
      };
      this.$api.invalidErpStockList(params).then(
        res => {
          this.$Message.success('作废成功');
          this.$router.back();
          // this.getErpStockDetail();
        },
      );
    },
    changeWare() {
      this.tableList = [];
      this.tablePageList = [];
      this.getLastTime();
      this.initFormStocktake_at();
    },
    changeType() {
      if (this.form.type === '1') {
        this.initFormStocktake_at();
      }
      if (!this.form.type) {
        this.form.warehouse_code = '';
      }
      this.tableList = [];
      this.tablePageList = [];
    },
    initFormStocktake_at() {
      this.form.stocktake_at = moment().format('YYYY-MM-DD HH:mm:ss');
    },

    passCheck() {
      this.$Modal.confirm({
        title: '通过审核',
        content: '您确定要通过该审核吗？',
        onOk: () => {
          this.review('PASS');
        },
      });
    },

    review(actions, reason) {
      const params = {
        id: this.id,
        status: 1,
      };
      let isPass = true;
      if (reason) {
        params.audit_reason = reason;
        params.status = 2;
        isPass = false;
      }
      this.$api.reviewErpStockList(params).then(
        res => {
          this.$Message.success(`${isPass ? '通过审核成功' : '驳回审核成功'}`);
          this.$router.back();
          // this.getErpStockDetail();
        },
      );
    },
  },
};
</script>

<style lang="less" scoped>
.download {
  margin-left: 20px;
  color: rgb(155, 141, 141);
  border-bottom: 1px solid #ccc;
}

.cursor {
  cursor: pointer;
}

.hover {
  &:hover {
    color: #155bd4 !important;
  }
}
</style>
