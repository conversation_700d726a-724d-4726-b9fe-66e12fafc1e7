<template>
  <el-popover
    placement="bottom"
    width="800"
    transfer
    :visible-arrow="false"
    :poper-options="popOptions"
    popper-class="pop-common"
    @show="onShow"
    v-model="showPop"
    :disabled="isDetail"
    trigger="click"
  >
    <slot slot="reference" style="width: 100%"></slot>
    <div class="popover-content-box">
      <div class="header-box">
        <h4 class="title">选择产品</h4>
        <div class="search-box">
          <span>搜索：</span>
          <Input v-model="queryFormData.q" placeholder="请输入产品名称/编码/条形码" @keyup.enter.native="onSearch">
            <Button slot="append" icon="ios-search" @click="onSearch"></Button>
          </Input>
        </div>
      </div>
      <div class="table-wrapper">
        <Table
          :columns="tableCols"
          :data="list"
          ref="section-table"
          :height="300"
          :loading="tableLoading"
          @on-select-all="selectAll"
          @on-select="select"
          @on-select-all-cancel="selectAllCancel"
          @on-select-cancel="selectCancel"
        >
          <!-- 勾选 -->
          <template slot-scope="{ row, index }" slot="checkBox">
            <Checkbox :value="list[index].checked" @on-change="changeSelectOrder(row, index)"></Checkbox>
          </template>

          <template slot-scope="{ row }" slot="name">
            <svg-icon iconClass="toxic" className="poison-flag" v-if="row.toxic_drugs == 'true'"></svg-icon>
            {{ row.name || '-' }}
          </template>

          <template slot-scope="{ row }" slot="spec">
            {{ row.spec || '-' }}
          </template>

          <!-- 质检报告 -->
          <!-- <template slot-scope="{ row }" slot="quality_inspection_report"> -->
          <!--            {{ row.quality_inspection_report || '-' }}-->
          <!--   <Picture -->
          <!--     style="margin: 10px 0 0 10px" -->
          <!--     accept="image/jpg,image/jpeg,image/png,.pdf" -->
          <!--     :format="['jpg', 'jpeg', 'png', 'pdf']" -->
          <!--     v-model="row.quality_inspection_report" -->
          <!--     :isQueryDetail="true" -->
          <!--   ></Picture> -->
          <!-- </template> -->
          <!-- 出库数量（输入后，默认选中） -->
          <!--          <template slot-scope="{row, index}" slot="quantity">-->
          <!--            <InputNumber-->
          <!--              :value="list[index].quantity"-->
          <!--              :ref="'quantity'+index"-->
          <!--              style="width: 100%"-->
          <!--              :min="0"-->
          <!--              :max="Number(row.batch_code_stock)"-->
          <!--              :precision="0"-->
          <!--              placeholder="请输入"-->
          <!--              @on-change="changeQuantity(row, index)"-->
          <!--            ></InputNumber>-->
          <!--          </template>-->
        </Table>
        <div class="block_20"></div>
        <div style="position: relative">
          <KPage
            :total="total"
            :page-size="+queryFormData.pageSize"
            :current="+queryFormData.page"
            @on-change="handleCurrentChange"
            @on-page-size-change="handleSizeChange"
            style="text-align: center"
            :show-sizer="false"
          />
          <div style="position: absolute; right: 15px; top: 8px">已选中：{{ quantityTotal }}</div>
        </div>
      </div>
      <div class="bottom-btn-wrapper">
        <Button @click="onCancel">取消</Button>
        <Dvd />
        <Dvd />
        <Dvd />
        <Button type="primary" @click="onConfirm">确定</Button>
      </div>
    </div>
  </el-popover>
</template>

<script>
// import S from '@/utils/util'
import Picture from '@/components/upload/picture';
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  product_code: '',
  q: '',
};
export default {
  name: 'select-batch-popper',
  mixins: [],

  components: { Picture },

  props: {
    title: {
      type: String,
      default: '选择批次产品',
    },
    apiName: {
      type: String,
      default: 'getErpProductList',
    },
    code: {
      type: String,
      default: '',
    },
    isDetail: {
      type: Boolean,
      default: () => false,
    },
    defaultParams: {
      type: Object,
      default: () => {},
    },
    // product_list: {
    //   type: Array,
    //   default: () => []
    // },
    batchCodeList: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      popOptions: { boundariesElement: 'body', gpuAcceleration: false },
      tableLoading: false,
      list: [],
      tableCols: [
        { type: 'selection', align: 'center', fixed: 'left', width: 80 },
        { title: '产品编号', key: 'code', align: 'center' },
        { title: '产品条码', key: 'barcode', align: 'center' },
        { title: '产品名称', slot: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', key: 'unit', align: 'center' },
        // { title: '生产日期', key: 'production_date', align: 'center', width: 100 },
        // { title: '失效日期', key: 'expiration_date', align: 'center', width: 100 },
        // { title: '批号', key: 'batch_code', align: 'center' },
        { title: '供应商', key: 'supplier_name', align: 'center' },
        // { title: '质检报告', slot: 'quality_inspection_report', align: 'center', width: 100 },
        // { title: '批次库存', key: 'batch_code_stock', align: 'center', width: 100 }
        // { title: '出库数量', slot: 'quantity', align: 'center', fixed: 'right',width: 100, },
      ],
      queryFormData: {
        ...init_query_form_data,
      },
      total: 0,
      selected_items: {},
      selectedList: [], // 选中的数据
      showPop: false,
    };
  },

  computed: {
    quantityTotal() {
      return this.selectedList.length;
    },
  },

  watch: {
    showPop: {
      immediate: true,
      handler(val) {
        if (val) {
          this.getList();
          this.selectedList = this._.cloneDeep(this.batchCodeList);
        } else {
          this.queryFormData = { ...init_query_form_data };
          this.selectedList = [];
        }
      },
    },
  },

  created() {},

  mounted() {},

  methods: {
    changeSelectOrder(row, index) {
      let isChecked = this.list[index].checked;
      this.list.map(item => (item.checked = false));
      this.list[index].checked = !isChecked;
      if (isChecked) {
        this.selected_items = {};
      } else {
        this.selected_items = this.list[index];
      }
    },
    onCancel() {
      this.showPop = false;
    },
    onConfirm() {
      this.$emit('selectBatchList', this.selectedList);
      this.showPop = false;
    },
    onSearch() {
      // this.selectedList = [];
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = 20;
      this.getList();
    },
    getList() {
      this.tableLoading = true;
      let params = {
        ...this.queryFormData,
        ...this.defaultParams,
        // product_code: this.product_code,
        // warehouse_code: this.warehouse_code,
      };
      this.$api[this.apiName](params).then(res => {
        this.tableLoading = false;
        this.list = this.handler(res.list);
        this.total = res.total;
      });
    },
    handler(list) {
      if (!list) return [];
      list.forEach((item, index) => {
        this.selectedList.forEach(checkItem => {
          if (checkItem.id === item.id) {
            item._checked = true;
          }
        });

        // this.batchCodeList.forEach(batch_item => {
        //   if (batch_item.id === item.id) {
        //     item._checked = true;
        //   }
        // });
        // item.quantity = null;
        // if (item.batch_code == this.batch_code || this.selected_items.code == item.batch_code) {
        //   item._checked = true;
        //   this.selectedList.push(item);
        // }
        // this.batchCodeList.forEach((batch_item, batch_key) => {
        //   if (batch_item.batch_code && batch_item.batch_code === item.batch_code) {
        //     item._checked = true;
        //     item.quantity = batch_item.quantity;
        //   }
        // });
      });
      return list;
    },
    handleSizeChange(val) {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.queryFormData.page = val;
      this.getList();
    },
    onShow() {
      if (this.defaultParams.warehouse_code) {
        this.showPop = true;
      } else {
        this.showPop = false;
      }
    },
    changeQuantity(row, index) {
      this.list[index].quantity = this.$refs['quantity' + index].currentValue;
      if (!row._checked) {
        this.select(this.list, this.list[index]);
      }
      this.selectedList.forEach(item => {
        if (item.batch_code === row.batch_code) {
          item.quantity = this.$refs['quantity' + index].currentValue;
        }
      });
    },

    // 表格的选中
    selectAll(val) {
      // this.selectedList = [];
      val &&
        val.forEach((item, index) => {
          let real_index = this.list.findIndex(items => items.id === item.id); // 该全选方法兼容了选项禁用的方法
          this.$set(this.list, real_index, { ...item, _checked: true });
          if (!item._checked) {
            this.selectedList.push(item);
          }
        });
    },
    select(val, row) {
      let _listIndex = this.getIndex('list', row);
      this.$set(this.list, _listIndex, { ...row, _checked: true });
      this.selectedList.push(row);
    },
    selectAllCancel(val) {
      this.list &&
        this.list.forEach((item, index) => {
          let _selectIndex = this.getIndex('selectedList', item);
          if (_selectIndex >= 0) {
            this.$set(this.list, index, { ...item, _checked: false });
            this.selectedList.splice(_selectIndex, 1);
          }
        });
    },
    selectCancel(val, row) {
      let _selectIndex = this.getIndex('selectedList', row);
      let _listIndex = this.getIndex('list', row);
      this.$set(this.list, _listIndex, { ...row, _checked: false });
      this.selectedList.splice(_selectIndex, 1);
    },
    getIndex(key, row) {
      let _index = -1;
      this[key].some((item, index) => {
        if (item.id === row.id) {
          _index = index;
        }
      });
      return _index;
    },
  },

  destroyed() {},
};
</script>

<style scoped lang="less">
:deep(.el-popover__reference-wrapper) {
  width: 100%;
}

.popover-content-box {
  .header-box {
    .title {
      font-weight: 600;
      line-height: 50px;
      padding: 0 20px;
      font-size: 16px;
      border-bottom: 1px solid rgb(223, 225, 230);
    }

    .search-box {
      display: flex;
      align-items: center;
      padding: 10px 20px;

      .el-input {
        width: 200px;
      }

      .el-button {
        margin-left: 10px;
      }
    }
  }

  .bottom-btn-wrapper {
    height: 50px;
    display: flex;
    justify-content: flex-end;
    padding: 10px 20px;
    border-top: 1px solid rgb(223, 225, 230);
    margin-top: 20px;
  }
}
</style>
<style lang="less">
.pop-common {
  padding: 0 !important;
}
</style>
