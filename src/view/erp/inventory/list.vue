<template>
  <div>
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <Col>
          <FormItem>
            <Input v-model="queryFormData.title" placeholder="请输入盘点名称" clearable />
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Input v-model="queryFormData.operator" placeholder="请输入盘点人" clearable />
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Select v-model="queryFormData.warehouse_code" placeholder="请选择仓库" clearable>
              <Option v-for="item in warehouseList" :value="item.code" :key="item.code">{{ item.name }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Select v-model="queryFormData.type" placeholder="请选择盘点类型" clearable>
              <Option v-for="item in warehouseOptions" :value="item.id" :key="item.kw">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Select v-model="queryFormData.status" placeholder="请选择审核状态" clearable>
              <Option v-for="item in reviewDescOptions" :value="item.id" :key="item.kw">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col>
          <FormItem>
            <Select v-model="queryFormData.invalid_status" placeholder="请选择作废状态" clearable>
              <Option v-for="item in invalidDescOptions" :value="item.id" :key="item.kw">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col>
          <FormItem style="text-align: left">
            <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
            <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
            <Button type="primary" @click="createWarehouse" v-eleControl="'ExMXNoGNPr'">创建盘点单</Button>
          </FormItem>
        </Col>
      </Row>
    </Form>

    <Table
      :loading="tableLoading"
      :columns="tableCols"
      border
      stripe
      :data="list"
      :height="$store.state.app.clientHeight - 295"
      :row-class-name="disableClassName"
    >
      <template slot-scope="{ row }" slot="warehouse_name">
        {{ row.warehouse_name || '-' }}
      </template>
      <template slot-scope="{ row }" slot="type_txt">
        {{ row.type_txt || '-' }}
      </template>
      <template slot-scope="{ row }" slot="stocktake_at">
        {{ row.stocktake_at || '-' }}
      </template>
      <template slot-scope="{ row }" slot="title">
        {{ row.title || '-' }}
      </template>
      <template slot-scope="{ row }" slot="remark">
        {{ row.remark || '-' }}
      </template>
      <template slot-scope="{ row }" slot="operator">
        {{ row.operator || '-' }}
      </template>
      <template slot-scope="{ row }" slot="created_at">
        {{ row.created_at || '-' }}
      </template>
      <template slot-scope="{ row }" slot="status_txt">
        <!-- {{ row.status_txt || '-' }} -->
        <span class="audit-text">
          <i class="status-dot" :style="getStatusTextColor(row.status)"></i>
          {{ row.status_txt }}
          <span v-if="row.status === '2'"
            >(<span style="color: red">{{ row.audit_reason || '-' }}</span
            >)</span
          >
        </span>
      </template>
      <template slot-scope="{ row }" slot="invalid_status">
        <span v-if="row.invalid_status === '1'" style="font-weight: bold">{{ row.invalid_status_text }}</span>
        <span v-else>-</span>
      </template>
      <template slot-scope="{ row }" slot="audited_at">
        {{ row.audited_at || '-' }}
      </template>
      <template slot-scope="{ row }" slot="audit_operator">
        {{ row?.audit_operator || '-' }}
      </template>
      <template slot-scope="{ row }" slot="action">
        <a
          @click="editSupplier(row)"
          v-if="row.status === '2' && row.invalid_status !== '1'"
          style="margin-left: 10px"
          v-eleControl="['E24ryPV1Yx', row.status === '2' && row.invalid_status !== '1' ? '' : '-']"
          >编辑</a
        >
        <a
          @click="editSupplier(row)"
          v-eleControl="['EGW6xpXyyM', row.status === '0' ? '' : '-']"
          v-if="row.status === '0'"
          style="margin-left: 10px"
          >审核</a
        >
        <a
          @click="editSupplier(row, true)"
          style="margin-left: 10px"
          v-eleControl="['E24ryPV1Yx', row.status === '0' ? '' : '-']"
          >详情</a
        >

        <!-- <a @click="editSupplier(row)" v-if="row.status === '2'" style="margin-left: 10px">编辑</a> -->
        <!-- <a @click="editSupplier(row)" v-if="row.status === '0'" style="margin-left: 10px">审核</a> -->
        <!-- <a @click="editSupplier(row, true)" style="margin-left: 10px">详情</a> -->
      </template>
    </Table>

    <div class="block_20"></div>
    <KPage
      :total="total"
      :page-size="+queryFormData.pageSize"
      :current="+queryFormData.page"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
      style="text-align: center"
    />
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'utils/util';
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '', // 名称
  r: '',
};

export default {
  name: 'list',
  components: {},
  mixins: [search],
  data() {
    return {
      apiName: 'getErpStockList',
      queryFormData: {
        ...init_query_form_data,
      },

      tableCols: [
        { title: '盘点单号', key: 'code', align: 'left' },
        { title: '盘点仓库', slot: 'warehouse_name', align: 'left' },
        { title: '盘点类型', slot: 'type_txt', align: 'left' },
        { title: '盘点时间', slot: 'stocktake_at', align: 'left' },
        { title: '盘点名称', slot: 'title', align: 'left' },
        { title: '盘点备注', slot: 'remark', align: 'left' },
        { title: '盘点人', slot: 'operator', align: 'left' },
        { title: '创建时间', slot: 'created_at', align: 'left' },
        { title: '审核状态', slot: 'status_txt', align: 'left' },
        { title: '作废状态', slot: 'invalid_status', align: 'left' },
        { title: '审核时间', slot: 'audited_at', align: 'left' },
        { title: '审批人', slot: 'audit_operator', align: 'left' },
        { title: '操作', slot: 'action', align: 'left' },
      ],
      list_count: {},
      warehouseList: [],
      warehouseOptions: [],
      reviewDescOptions: [],
      invalidDescOptions: [],
    };
  },
  computed: {
    getStatusTextColor() {
      /**
       * @method getStatusTextColor
       * @param {status} type  Number - description
       * @description: 0 -- 待审核  1 -- 审核通过  2 -- 审核驳回
       * @author: yangyi
       * @date: 9/2/22
       */
      return status => {
        switch (status) {
          case '0': //建议采购
            return {
              background: '#ffad33',
            };
          case '1': //库存充足
            return {
              background: '#19be6b',
            };
          case '2': //库存不足
            return {
              background: '#ed4014',
            };
          case '3': // 后续会拆分出去（使用在详情的状态中）
            return {
              background: '#0EBE98',
            };
        }
      };
    },
  },
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.getErpWarehousePullList();
    this.getWarehouseOptions();
  },
  mounted() {},
  methods: {
    // api - 仓库列表
    getErpWarehousePullList() {
      this.$api.getErpWarehouseList({ pageSize: 999 }).then(res => {
        this.warehouseList = res.list;
      });
    },
    getWarehouseOptions() {
      this.$api.getWarehouseOptions().then(res => {
        // this.warehouseOptions = res;
        this.warehouseOptions = S.descToArrHandle(res.typeDesc);
        this.invalidDescOptions = S.descToArrHandle(res.invalidDesc);
        this.reviewDescOptions = S.descToArrHandle(res.statusDesc);
      });
    },

    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },
    createWarehouse() {
      this.$router.push({
        path: '/erp/inventory/detail',
      });
    },
    editSupplier(row, look) {
      this.$router.push({
        path: '/erp/inventory/detail',
        query: {
          id: row.id,
          look,
        },
      });
      console.log(row);
    },
    disableClassName(row) {
      if (row.status === 'DISABLE') {
        return 'disableTableRow';
      }
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  },
};
</script>

<style scoped lang="less">
.audit-text {
  .status-dot {
    display: inline-block;
    border-radius: 50%;
    width: 10px;
    height: 10px;
    margin-right: 4px;
  }
}
</style>

<style lang="less" scoped>
// current page common less
.ml10 {
  margin-left: 10px;
}
</style>
