<template>
  <div class="container">
    <div class="demo-spin-container" v-if="detailLoading">
      <Spin fix></Spin>
    </div>

    <Tabs v-else :value="tab" @on-click="changeTab" :animated="false">
      <!-- 详细资料 -->
      <TabPane label="详细资料" name="detail">
        <div class="block-header">
          基础信息
          <Button type="primary" class="edit-button" @click="showEditModal" v-if="formData.audit_status == '70'"
            >编辑基础信息</Button
          >
        </div>
        <div class="flex flex-between">
          <Form :model="formData" label-position="right" :label-width="100" class="basicInfo">
            <Row :gutter="40">
              <Col span="12">
                <FormItem label="库存调拨单号:">
                  {{ formData.code }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="预计调拨时间:">
                  {{ formData.transfer_date }}
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="实际变动时间:">
                  {{ formData.actual_time | date_format('YYYY-MM-DD HH:mm:ss') }}
                </FormItem>
              </Col>
              <!-- <Col span="12">
                  <FormItem label="调拨名称:">
                    <span>{{formData.name}}</span>
                  </FormItem>
                </Col> -->
              <Col span="12">
                <FormItem label="调出仓库:">
                  <span v-if="formData.outbound_warehouse_info">{{
                    formData.outbound_warehouse_info.warehouse_name
                  }}</span>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="调入仓库:">
                  <span v-if="formData.inbound_warehouse_info">{{
                    formData.inbound_warehouse_info.warehouse_name
                  }}</span>
                </FormItem>
              </Col>
              <Col span="12">
                <FormItem label="备注:">
                  {{ formData.remark || '-' }}
                </FormItem>
              </Col>
            </Row>
          </Form>
        </div>
        <div class="block-header">系统信息</div>
        <Form :model="formData" label-position="right" :label-width="100" class="basicInfo">
          <Row :gutter="40">
            <Col span="12">
              <FormItem label="创建人:">
                {{ formData.operator }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="创建时间:">
                {{ formData.create_time | date_format }}
              </FormItem>
            </Col>
            <Col span="12">
              <FormItem label="更新时间:">
                {{ formData.update_time | date_format }}
              </FormItem>
            </Col>
          </Row>
        </Form>
        <div class="block-header">产品</div>
        <Table :columns="tableCols" :data="list">
          <template slot-scope="{ row, index }" slot="code">
            <KLink v-if="row.code" :to="{ path: '/erp/product/detail', query: { id: row.id } }" target="_blank">{{
              row.code
            }}</KLink>
            <span v-else>{{ row.code }}</span>
          </template>

          <template slot-scope="{ row, index }" slot="spec">
            {{ row.spec || '-' }}
          </template>
          <template slot-scope="{ row, index }" slot="unit">
            {{ row.unit || '-' }}
          </template>
          <template slot-scope="{ row, index }" slot="note">
            {{ row.note || '-' }}
          </template>
        </Table>
        <div class="block_20"></div>
        <div class="block_20"></div>
      </TabPane>

      <!-- 操作记录 -->
      <TabPane label="操作记录" name="operationRecord">
        <operationlog-record :b_type="b_type" :b_id="b_id" :isRecord="isRecord"></operationlog-record>
      </TabPane>
    </Tabs>

    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Button v-if="formData.audit_status === '10'" style="margin: 0 20px" type="error" @click="showRefuseModal"
        >审核驳回
      </Button>
      <Button v-if="formData.audit_status === '10'" type="primary" @click="passCheck">审核通过 </Button>
    </div>

    <edit-allot-modal :id="allotId" :visible.sync="editVisible" @refresh="init"></edit-allot-modal>
    <refuse-reason-modal :visible.sync="refuseModalVisible" :auditSalesOrder="review"></refuse-reason-modal>
  </div>
</template>

<script>
import OperationlogRecord from '../components/operationlog-record';
import EditAllotModal from './components/EditAllotModal';
import RefuseReasonModal from '@/components/RefuseReasonModal';
const init_query_form_data = {
  // page: 1,
  // pageSize: 20,
  id: '', // 采购订单id
  code: '' // 采购订单编号
};

export default {
  data() {
    return {
      tab: 'detail',
      formData: {
        name: '1'
      },
      editVisible: false,
      tableCols: [
        { title: '产品编码', slot: 'code', align: 'center' },
        { title: '产品条码', key: 'barcode', align: 'center' },
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '调拨数量', key: 'quantity', align: 'center' },
        { title: '备注', slot: 'note', align: 'center' }
      ],
      list: [],
      total: 0,
      queryFormData: { ...init_query_form_data },
      b_type: 19,
      b_id: 0,
      isRecord: 0,
      detailLoading: false,
      refuseModalVisible: false,
      allotId: ''
    };
  },
  created() {},
  mounted() {
    this.init();
  },
  components: {
    OperationlogRecord,
    RefuseReasonModal,
    EditAllotModal
  },
  methods: {
    init() {
      if (this.$route.query.id || this.$route.query.code) {
        this.getErpTransferInfo(this.$route.query.id, this.$route.query.code);
      }
    },
    // tabs事件
    changeTab(name) {
      if (name == 'operationRecord') {
        this.isRecord++;
      }
    },
    getErpTransferInfo(id, code) {
      this.detailLoading = true;
      let params = { id, code };
      this.$api.getErpTransferInfo(params).then(res => {
        this.formData = res;
        this.$router.replace({
          query: {
            ...this.$route.query,
            id: res.id
          }
        });
        this.queryFormData.id = this.$route.query.id;
        this.b_id = Number(this.$route.query.id);
        this.detailLoading = false;
        this.getErpTransferDetail();
      });
    },
    getErpTransferDetail() {
      let params = { ...this.queryFormData };
      this.$api.getErpTransferDetail(params).then(res => {
        this.handlerDetail(res.detail, res.products);
      });
    },
    // 处理明细数据，回显采购订单
    handlerDetail(list, products) {
      console.log('-> %c list, products  === %o', 'font-size: 15px;color: green;', list, products);
      let productDetailList = [];
      list &&
        list.forEach(item => {
          productDetailList.push({
            ...products[item.product_id],
            quantity: item.quantity,
            price: item.price,
            total_price: item.total_price,
            note: item.note
          });
        });
      console.log('-> %c productDetailList  === %o', 'font-size: 15px;color: green;', productDetailList);
      this.list = productDetailList;
    },

    // 编辑
    showEditModal() {
      this.allotId = this.$route.query.id;
      this.editVisible = true;
    },

    submitRefuseReason() {
      this.review('REJECT', this.reason);
    },

    back() {
      this.$router.back();
    },

    // 审核按钮
    passCheck() {
      this.$Modal.confirm({
        title: '通过审核',
        content: '您确定要通过该审核吗？',
        onOk: () => {
          this.review('PASS');
        }
      });
    },

    // 审核驳回
    showRefuseModal() {
      this.refuseModalVisible = true;
    },

    /**
     * @description 审核/驳回的接口
     * @param { action } 审核的状态
     * @param { reason } 驳回的原因
     * */
    review(action, reason) {
      const params = {
        id: this.$route.query.id,
        action,
        reason: ''
      };
      let isPass = true;
      if (reason) {
        params.reason = reason;
        isPass = false;
      }
      this.$api.changeErpTransferStatus(params).then(
        res => {
          this.$Message.success(`${isPass ? '通过审核成功' : '驳回审核成功'}`);
          this.init();
        },
      );
    }
  }
};
</script>

<style lang="less" scoped>
.container {
  //position: relative;
  .total {
    text-align: right;
    .total-num {
      margin-right: 15px;
      color: #fd715a;
    }
  }
}

.basicInfo {
  width: 60%;
  margin-left: 60px;
  .basic-item {
    width: 50%;
  }
  .remark {
    width: 100%;
  }
}

.buttonGroup {
  text-align: center;
}

.ml-10 {
  margin-left: 10px;
}

.mr-10 {
  margin-right: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.edit-button {
  position: absolute;
  top: 3px;
  right: 5px;
}

::v-deep .block-header span {
  font-size: 12px;
  padding: 0;
  font-weight: normal;
}
</style>
