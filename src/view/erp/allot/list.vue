<template>
  <div>
    <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <Col>
          <FormItem>
            <Input v-model="queryFormData.code" placeholder="请输入调拨单号" clearable />
          </FormItem>
        </Col>

        <!-- <Col>
          <FormItem>
            <Input v-model="queryFormData.name" placeholder="请输入调拨名称" clearable/>
          </FormItem>
        </Col> -->

        <Col>
          <FormItem>
            <Select v-model="queryFormData.audit_status" placeholder="请选择状态" clearable>
              <Option v-for="item in statusList" :value="item.id" :key="item.id">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <Select v-model="queryFormData.push_status" style="width: 180px" placeholder="推送状态" clearable>
              <Option v-for="item in pushStatus" :value="item.code" :key="item.code">{{ item.name }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col>
          <FormItem>
            <DatePicker
              v-model="queryFormData.st"
              type="date"
              format="yyyy-MM-dd"
              @on-change="st => (queryFormData.st = st)"
              placeholder="调拨开始时间"
            ></DatePicker>
            -
            <DatePicker
              v-model="queryFormData.et"
              type="date"
              format="yyyy-MM-dd"
              @on-change="et => (queryFormData.et = et)"
              placeholder="调拨结束时间"
            ></DatePicker>
          </FormItem>
        </Col>

        <Col>
          <FormItem style="text-align: left">
            <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
            <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
            <Button type="primary" @click="createAllot">新建调拨单</Button>
          </FormItem>
        </Col>
      </Row>
    </Form>

    <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 240">
      <!-- <template slot-scope="{row}" slot="name">
        {{row.name || '-'}}
      </template> -->

      <template slot-scope="{ row }" slot="audit_status">
        <status-text :status="row.audit_status"
          ><span>{{ row.audit_status_text }}</span></status-text
        >
      </template>

      <template slot-scope="{ row }" slot="create_time">
        {{ row.create_time | date_format }}
      </template>

      <template slot-scope="{ row }" slot="remark">
        {{ row.remark || '-' }}
      </template>
      <template slot-scope="{ row }" slot="push_status">
        <span v-if="row.push_status === '4'"
          >{{ row.push_status_text }}
          <Tooltip placement="bottom" max-width="200" :content="row.hangup_text">
            <Icon type="md-help-circle" style="font-size: 16px" class="cursor" />
          </Tooltip>
        </span>
        <span v-else>{{ row.push_status_text }}</span>
      </template>
      <template slot-scope="{ row }" slot="action">
        <a @click="editAllot(row)" v-if="row.audit_status == '70'">编辑</a>
        <Dvd v-if="row.audit_status == '70'"></Dvd>
        <a @click="toDetail(row)">详情</a>
      </template>
    </Table>

    <div class="block_20"></div>
    <KPage
      :total="total"
      :page-size="+queryFormData.pageSize"
      :current="+queryFormData.page"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
      style="text-align: center"
    />
    <edit-allot-modal :id="allotId" :visible.sync="editVisible" @refresh="refresh"></edit-allot-modal>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'utils/util';
import EditAllotModal from './components/EditAllotModal';
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  code: '', // 调拨单号
  // name: '',// 名称
  audit_status: '', // 审核状态
  push_status: '', // 推送状态
  st: '',
  et: '',
  r: ''
};

export default {
  name: 'list',
  components: {
    EditAllotModal
  },
  mixins: [search],
  data() {
    return {
      apiName: 'getErpTransferList',
      queryFormData: {
        ...init_query_form_data
      },
      pushStatus: [
        { name: '不推送', code: '0' },
        { name: '待推送', code: '1' },
        { name: '挂起', code: '4' },
        { name: '已推送', code: '9' }
      ],
      tableCols: [
        { title: '调拨单号', key: 'code', align: 'center' },
        // { title: '调拨名称', slot: 'name', align: 'center' },
        { title: '调拨时间', key: 'transfer_date', align: 'center' },
        { title: '调出仓库', key: 'outbound_warehouse_name', align: 'center' },
        { title: '调入仓库', key: 'inbound_warehouse_name', align: 'center' },
        { title: '备注', slot: 'remark', align: 'center' },
        { title: '状态', slot: 'audit_status', align: 'center' },
        { title: '推送状态', slot: 'push_status', align: 'center', width: 80 },
        { title: '创建人', key: 'operator', align: 'center' },
        { title: '创建时间', slot: 'create_time', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' }
      ],
      list_count: {},
      allotId: '',
      editVisible: false,
      statusList: [] // 状态列表
    };
  },
  computed: {},
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.getErpTransferOptions();
  },
  mounted() {},
  methods: {
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },
    createAllot() {
      this.allotId = '';
      this.editVisible = true;
    },
    refresh() {
      this.loadList();
    },
    editAllot(row) {
      this.allotId = row.id;
      this.editVisible = true;
    },
    // 详情
    toDetail(row) {
      this.$router.push({
        path: '/erp/allot/detail',
        query: {
          id: row.id
        }
      });
    },
    // api - 获取调拨单枚举值
    getErpTransferOptions() {
      this.$api.getErpTransferOptions().then(res => {
        this.statusList = S.descToArrHandle(res.prodAuditStatusDesc);
      });
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  }
};
</script>

<style scoped lang="less"></style>

<style lang="less" scoped>
// current page common less
.ml10 {
  margin-left: 10px;
}
</style>
