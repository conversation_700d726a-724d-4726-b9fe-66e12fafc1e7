<template>
  <div>
    <Modal
      :value="visible"
      :title="id ? '编辑库存调拨（驳回后可编辑）' : '新建库存调拨单'"
      width="900px"
      @on-cancel="cancelHandle"
      :mask-closable="false"
    >
      <Form
        label-position="top"
        class="common-modal-form"
        :model="formData"
        :rules="formDataValidateRules"
        ref="allotRef"
      >
        <div class="section-header">
          <div class="section-mark"></div>
          <div class="section-title">基本信息</div>
        </div>

        <div class="create-section">
          <FormItem label="调拨单号 " prop="code" class="common-form-item">
            <Input v-model="formData.code" placeholder="不填系统将自动生成" maxlength="20" :disabled="true"></Input>
          </FormItem>

          <!-- <FormItem label="调拨名称 " prop="name" class="common-form-item">
            <Input v-model="formData.name" placeholder="请输入调拨名称"></Input>
          </FormItem> -->

          <FormItem label="预计调拨时间" prop="transfer_date" class="common-form-item">
            <DatePicker
              type="date"
              ref="allot-time"
              :value="formData.transfer_date"
              style="width: 100%"
              placeholder="请选择调拨时间"
              @on-change="date => (formData.transfer_date = date)"
            ></DatePicker>
          </FormItem>

          <FormItem label="调出仓库" prop="outbound_warehouse_code" class="common-form-item">
            <select-warehouse-popper
              class="flex"
              ref="selectOuterWarehouse"
              @selectSup="selectWarehouse($event, 'selectOuterWarehouseList', 'outbound_warehouse_code', true)"
              status="ENABLE"
              :code="formData.outbound_warehouse_code"
            >
              <el-select
                :multiple="false"
                v-model="formData.outbound_warehouse_code"
                :multiple-limit="1"
                style="width: 100%"
                @visible-change="selectChange($event, 'selectOuterWarehouse')"
                size="small"
                popper-class="rxj-pop-select"
              >
                <el-option
                  v-for="(item, index) in selectOuterWarehouseList"
                  :value="item.code"
                  :label="item.name"
                  :key="index + item.code"
                ></el-option>
              </el-select>
            </select-warehouse-popper>
          </FormItem>

          <FormItem label="调入仓库" prop="inbound_warehouse_code" class="common-form-item">
            <select-warehouse-popper
              class="flex"
              ref="selectEnterWarehouse"
              @selectSup="selectWarehouse($event, 'selectEnterWarehouseList', 'inbound_warehouse_code')"
              status="ENABLE"
              :code="formData.inbound_warehouse_code"
            >
              <el-select
                :multiple="false"
                v-model="formData.inbound_warehouse_code"
                :multiple-limit="1"
                style="width: 100%"
                @visible-change="selectChange($event, 'selectEnterWarehouse')"
                size="small"
                popper-class="rxj-pop-select"
              >
                <el-option
                  v-for="(item, index) in selectEnterWarehouseList"
                  :value="item.code"
                  :label="item.name"
                  :key="index + item.code"
                ></el-option>
              </el-select>
            </select-warehouse-popper>
          </FormItem>

          <FormItem label="备注" class="common-form-item">
            <Input
              v-model="formData.remark"
              type="textarea"
              maxlength="50"
              show-word-limit
              :autosize="{ maxRows: 2, minRows: 2 }"
            ></Input>
          </FormItem>
        </div>
      </Form>

      <div class="section-header mt10">
        <div class="section-mark"></div>
        <div class="section-title">产品信息</div>
      </div>
      <div class="flex flex-item-end">
        <SelectProductPopper
          class="flex"
          ref="selectProduct"
          @selectedList="selectedList"
          :product_list="product_list"
          :warehouse_code="formData.outbound_warehouse_code"
          :isDetail="!formData.outbound_warehouse_code"
        >
          <Button type="primary" @click="addProductE" :disabled="!formData.outbound_warehouse_code">添加商品</Button>
        </SelectProductPopper>
      </div>

      <Table class="mt10" :loading="tableLoading" :columns="product_tableCols" :data="product_list">
        <!-- 产品规格 -->
        <template slot-scope="{ row, index }" slot="spec">
          {{ row.spec || '-' }}
        </template>

        <!-- 产品单位 -->
        <template slot-scope="{ row, index }" slot="unit">
          {{ row.unit || '-' }}
        </template>

        <!-- 调拨数量 -->
        <template slot-scope="{ row, index }" slot="quantity">
          <InputNumber
            v-model="product_list[index].quantity"
            :ref="'quantity' + index"
            style="width: 100%"
            :active-change="false"
            :min="0"
            :precision="0"
            placeholder="请输入"
            @on-blur="removeZero('quantity', index)"
          ></InputNumber>
        </template>

        <!-- 备注 -->
        <template slot-scope="{ row, index }" slot="note">
          <Input style="width: 100%" v-model="product_list[index].note" placeholder="请输入备注"></Input>
        </template>

        <template slot-scope="{ row, index }" slot="operator">
          <a @click="deleteProduct(index)">删除</a>
        </template>
      </Table>

      <div slot="footer">
        <Button @click="cancelHandle">取消</Button>
        <Button type="primary" @click="confirmHandle" :loading="submitLoading">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import S from 'utils/util';
import { $operator } from '@/utils/operation';
import SelectPopper from '@/components/select-popper/select-popper';
import SelectWarehousePopper from '@/components/select-warehouse-popper/select-warehouse-popper';
import SelectPurchasePopper from '@/components/select-purchase-popper/select-purchase-popper';
import SelectReturnPopper from '@/components/select-order-return-popper/select-order-return-popper';
import SelectProductPopper from '@/components/select-product-popper/select-product-popper';
import moment from 'moment';

const initFormData = {
  code: '', // 调拨单号
  // name: '', // 调拨名称
  transfer_date: '', // 调拨日期
  outbound_warehouse_code: '', // 调出仓库编号
  inbound_warehouse_code: '', // 调入仓库编号
  remark: '' //备注
};

export default {
  name: 'list',
  mixins: [],
  components: { SelectPopper, SelectPurchasePopper, SelectReturnPopper, SelectWarehousePopper, SelectProductPopper },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    },
    code: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      formData: { ...initFormData },
      formDataValidateRules: {
        transfer_date: [{ required: true, message: '请选择调拨时间', trigger: 'blur,change' }],
        outbound_warehouse_code: [{ required: true, message: '请选择调出仓库', trigger: 'blur,change' }],
        inbound_warehouse_code: [{ required: true, message: '请选择调入仓库', trigger: 'blur,change' }]
      },
      submitLoading: false, // 弹窗确定的loading

      selectOuterWarehouseList: [], // 选中的调出仓库
      selectEnterWarehouseList: [], // 选中的调入仓库

      tableLoading: false,
      product_tableCols: [
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', slot: 'unit', align: 'center' },
        { title: '库存数量', key: 'stock_total', align: 'center' },
        { title: '调拨数量', slot: 'quantity', align: 'center' },
        { title: '备注', slot: 'note', align: 'center' },
        { title: '操作', slot: 'operator', align: 'center' }
      ],

      product_list: [], // 商品数据

      // 选择商品
      editProductVisible: false // 选择商品弹窗
    };
  },

  computed: {},

  watch: {
    visible: {
      immediate: true,
      handler(val) {
        if (val) {
          this.formData.transfer_date = moment(new Date()).format('YYYY-MM-DD');
          if (this.id) {
            this.getErpTransferInfo();
            this.getErpTransferDetail();
          }
        } else {
        }
      }
    }
  },

  created() {},

  mounted() {},

  methods: {
    selectChange(e, node) {
      // poppver的ref
      let nodeList = ['selectOuterWarehouse', 'selectEnterWarehouse', 'selectProduct'];
      // select date 的ref
      let selectNodeList = ['allot-time'];
      if (e) {
        let hideList = nodeList.filter(item => item !== node);
        hideList.forEach(item => {
          this.$refs[item] && (this.$refs[item].showPop = false);
        });
        selectNodeList.forEach(item => {
          this.$refs[item] && (this.$refs[item].visible = false);
        });
      }
    },

    // 去零
    removeZero(key, index) {
      let currentValue = this.$refs[key + index].currentValue;
      if (Number(currentValue) == 0) {
        this.product_list[index].key = null;
        this.$refs[key + index].currentValue = null;
      }
    },

    // 校验产品是否存在数量未填写的情况
    validProductCalc() {
      let isUnValid = false;
      if (!this.product_list.length) {
        this.$Message.error('商品不可为空');
        isUnValid = true;
        return true;
      }
      this.product_list &&
        this.product_list.some(item => {
          if (!item.quantity) {
            this.$Message.error(`商品【${item.name}】请填写完整`);
            isUnValid = true;
            return true;
          }
        });
      return isUnValid;
    },

    // 添加商品
    addProductE() {
      this.selectChange(true);
      this.editProductVisible = true;
    },
    // 删除商品
    deleteProduct(index) {
      this.product_list.splice(index, 1);
    },
    // 获取勾选的商品
    selectedList(list) {
      this.product_list = [];
      list &&
        list.forEach((item, index) => {
          this.$set(this.product_list, index, {
            ...item,
            quantity: item.quantity || null
          });
        });
    },
    // 创建库存调拨单
    confirmHandle() {
      this.$refs['allotRef'].validate(valid => {
        if (valid) {
          // 商品计算未完成,不允许提交
          if (this.validProductCalc()) {
            return;
          }
          this.id ? this.EditTransfer() : this.erpAllotCreate();
        } else {
          this.$Message.error('请填写完整');
        }
      });
    },

    // 处理商品的价格数据，作为参数
    handlerAllotDetails() {
      let detail = [];
      this.product_list &&
        this.product_list.forEach(item => {
          detail.push({
            // product_id: item.id,
            product_code: item.code,
            quantity: item.quantity,
            note: item.note
          });
        });
      return detail || [];
    },

    handlerDetail(list, products) {
      let productDetailList = [];
      list &&
        list.forEach(item => {
          if (products[item.product_id] && $operator.subtract(Number(item.quantity), Number(item.p_qty))) {
            productDetailList.push({
              ...products[item.product_id],
              surplus_quantity: $operator.subtract(Number(item.quantity), Number(item.p_qty)),
              quantity: null,
              price: null,
              original_price: item.price, // 原价
              total_price: 0
            });
          }
        });
      return productDetailList || [];
    },

    // api - 创建库存调拨单
    erpAllotCreate() {
      this.submitLoading = true;
      let params = {
        ...this.formData,
        detail: JSON.stringify(this.handlerAllotDetails())
      };
      this.$api
        .createTransfer(params)
        .then(res => {
          this.$Message.success('创建成功');
          this.cancelHandle();
          this.$emit('refresh');
          this.submitLoading = false;
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
          this.submitLoading = false;
        });
    },

    // 编辑库存调拨单
    EditTransfer() {
      this.submitLoading = true;
      let params = {
        id: this.id,
        ...this.formData,
        detail: JSON.stringify(this.handlerAllotDetails())
      };
      this.$api
        .EditTransfer(params)
        .then(res => {
          this.$Message.success('编辑成功');
          this.cancelHandle();
          this.$emit('refresh');
          this.submitLoading = false;
        })
        .catch(err => {
          this.$Message.error(err.errmsg);
          this.submitLoading = false;
        });
    },

    // 关闭弹窗,清除数据
    cancelHandle() {
      this.formData = { ...initFormData };
      this.$refs.allotRef.resetFields();
      this.product_list = [];
      this.$emit('update:visible', false);
      this.$emit('changeCreate', false);
    },

    /**
     * @description: 调出仓库和调入仓库不可为同一个仓库
     * @param { warehouseInfoObj } 选中的仓库对象
     * @param { warehouseListKey } 需要回显的仓库list的字段名
     * @param { code_key } 需要回显仓库code在formData中的字段名
     * @return { isEqual } 调出仓库和调入仓库是否为同一个仓库
     *
     */
    isEqualWarehouse(warehouseInfoObj, warehouseListKey, code_key) {
      let isEqual = false;
      // 调入仓库和调出仓库不可为同一个仓库，将后一个重复的仓库数据置空，并且予以提示
      let warehouse_code = warehouseInfoObj.code || warehouseInfoObj.warehouse_code;
      console.log('warehouse_code', warehouse_code);
      // 选择调出仓库
      if (code_key == 'outbound_warehouse_code' && this.formData.inbound_warehouse_code == warehouse_code) {
        isEqual = true;
      }

      // 选择调入仓库
      if (code_key == 'inbound_warehouse_code' && this.formData.outbound_warehouse_code == warehouse_code) {
        isEqual = true;
      }
      if (isEqual == true) {
        this[warehouseListKey] = [];
        this.formData[code_key] = '';
        this.$Message.error('调入仓库不可和调出仓库为同一个仓库');
      }
      return isEqual;
    },

    /**
     * @description: 选中仓库
     * @param { warehouseInfoObj } 选中的仓库对象
     * @param { warehouseListKey } 需要回显的仓库list的字段名
     * @param { code_key } 需要回显仓库code在formData中的字段名
     * @param { isClear } 是否要清除产品信息
     */
    selectWarehouse(warehouseInfoObj, warehouseListKey, code_key, isClear = false) {
      // 是否为统一仓库
      if (this.isEqualWarehouse(warehouseInfoObj, warehouseListKey, code_key)) {
        return;
      }

      if (isClear) {
        this.product_list = [];
      }

      // 回显前先置空
      this[warehouseListKey] = [];
      this.formData[code_key] = '';

      if (!S.isEmptyObject(warehouseInfoObj)) {
        // 回显仓库方法
        this.echoWarehouse(warehouseListKey, code_key, warehouseInfoObj);
      }
      this.$forceUpdate();
      this.$refs.allotRef.validateField(code_key);
    },

    /**
     * @description: 回显仓库
     * @param { warehouseListKey } 需要回显的仓库list的字段名
     * @param { code_key } 需要回显仓库code在formData中的字段名
     * @param { warehouseInfoObj } { code, name } 回显仓库对应的仓库名称和仓库code
     * */
    echoWarehouse(
      warehouseListKey = 'warehouseList',
      code_key = 'warehouse_code',
      warehouseInfoObj = { code: '', name: '' }
    ) {
      if (!warehouseListKey) {
        throw Error('需要回显的仓库list的字段名不可为空');
      }
      if (!code_key) {
        throw Error('需要回显仓库code在formData中的字段名不可为空');
      }
      this[warehouseListKey] = [
        {
          code: warehouseInfoObj.code || warehouseInfoObj.warehouse_code,
          name: warehouseInfoObj.name || warehouseInfoObj.warehouse_name
        }
      ];
      this.formData[code_key] = warehouseInfoObj.code || warehouseInfoObj.warehouse_code;
    },

    // api-获取调拨单信息
    getErpTransferInfo() {
      let params = {
        id: this.id
      };
      this.$api.getErpTransferInfo(params).then(
        res => {
          this.formData.code = res.code;
          this.formData.name = res.name;
          // 回显调出仓库数据
          this.echoWarehouse('selectOuterWarehouseList', 'outbound_warehouse_code', res.outbound_warehouse_info);
          // 回显调入仓库数据
          this.echoWarehouse('selectEnterWarehouseList', 'inbound_warehouse_code', res.inbound_warehouse_info);
          this.formData.transfer_date = res.transfer_date;
          this.formData.remark = res.remark;
        },
        rej => this.$Message.error(rej.errmsg)
      );
    },

    // api-获取调拨单产品明细
    getErpTransferDetail() {
      this.product_list = [];
      let params = {
        id: this.id
      };
      this.$api.getErpTransferDetail(params).then(
        res => {
          res.detail.forEach(item => {
            this.product_list.push({
              ...res.products[item.product_id],
              ...item,
              quantity: Number(item.quantity)
            });
          });
        },
        rej => this.$Message.error(rej.errmsg)
      );
    }
  },

  destroyed() {}
};
</script>

<style scoped lang="less">
@import url('../../common/modal.less');
</style>
