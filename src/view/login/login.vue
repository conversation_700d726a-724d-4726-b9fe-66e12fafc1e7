<template>
  <div class="login-wrapper flex flex-item-center">
    <div class="login-box">
      <div class="login-top flex flex-item-center">
        <div class="login-t-r">
          <h3 class="login-t-title">树家ERP管理平台</h3>
        </div>
      </div>
      <div class="login-btm">
        <div v-if="!forgetPassword" class="login-right">
          <div class="nav flex flex-item-between">
            <div :class="{ cur: status == 0 }" class="nav-password" @click="changeTap(0)">密码登录</div>
            <div :class="{ cur: status == 1 }" class="nav-code" @click="changeTap(1)">验证码登录</div>
          </div>
          <div class="nav-bar">
            <div ref="cur" :style="barStyle" class="cur"></div>
          </div>
          <div class="login-input">
            <Form :auto-complete="false" @submit.native.prevent @keyup.enter.native="onLogin">
              <FormItem v-if="status == 0" style="margin-top: 26px">
                <Input
                  ref="pwdInput"
                  v-model.trim="loginData.mobile"
                  clearable
                  placeholder="请输入手机号或用户名"
                  size="large"
                  type="tel"
                ></Input>
              </FormItem>
              <!-- 手机号码 -->
              <FormItem v-if="status == 1" style="margin-top: 20px">
                <Input
                  ref="authInput"
                  v-model="loginData.mobile"
                  clearable
                  placeholder="请输入手机号"
                  size="large"
                  type="number"
                ></Input>
              </FormItem>
              <!-- 密码 -->
              <FormItem v-if="status == 0" clearable style="margin-top: 20px">
                <Input
                  v-model="loginData.password"
                  clearable
                  placeholder="请输入密码"
                  size="large"
                  type="password"
                ></Input>
              </FormItem>
              <!-- 验证码 -->

              <FormItem v-if="status == 1" style="margin-top: 20px">
                <Input v-model="loginData.auth_code" placeholder="请输入验证码" size="large" type="number"></Input>
                <div class="login-auth">
                  <vac ref="vac" :auto-start="false" :left-time="60000" @finish="onCountDownFinish">
                    <span slot="process" slot-scope="{ timeObj }">
                      {{ timeObj.ceil.s }}
                    </span>
                    <span slot="before" @click="onCountDownStart">获取验证码</span>
                    <span slot="finish" @click="onCountDownStart">重新获取</span>
                  </vac>
                </div>
              </FormItem>

              <Button
                style="width: 100%; height: 41px; font-size: 16px; margin-top: 26px"
                type="primary"
                @click="onLogin"
                >登录
              </Button>
              <div v-if="status == 0" class="login-msg" style="cursor: default">
                <span @click="goForget" style="cursor: pointer">忘记密码？</span>
              </div>
            </Form>
          </div>
        </div>
        <div v-if="forgetPassword" class="login-right changepsd">
          <div class="forget-tit">找回密码</div>
          <div class="login-refund">
            <Form ref="psdForm" :model="loginData" :rules="ruleLoginData">
              <FormItem prop="mobile" style="margin-top: 20px">
                <Input
                  v-model="loginData.mobile"
                  clearable
                  placeholder="请输入手机号"
                  size="large"
                  type="number"
                ></Input>
              </FormItem>

              <FormItem prop="auth_code" style="margin-top: 20px">
                <Input v-model="loginData.auth_code" placeholder="请输入验证码" size="large" type="number"></Input>
                <div class="login-auth">
                  <vac ref="vac2" :auto-start="false" :left-time="60000" size="large" @finish="onCountDownFinish">
                    <span slot="process" slot-scope="{ timeObj }">
                      {{ timeObj.ceil.s }}
                    </span>
                    <span slot="before" @click="onCountDownStart">获取验证码</span>
                    <span slot="finish" @click="onCountDownStart">重新获取</span>
                  </vac>
                </div>
              </FormItem>
              <FormItem prop="new_password" style="margin-top: 20px">
                <Input
                  v-model="loginData.new_password"
                  clearable
                  placeholder="输入新密码(6-20位字母数字组合)"
                  size="large"
                  type="password"
                ></Input>
              </FormItem>
              <FormItem prop="confirm_password" style="margin-top: 20px">
                <Input
                  v-model="loginData.confirm_password"
                  clearable
                  placeholder="确认新密码"
                  size="large"
                  type="password"
                ></Input>
              </FormItem>
              <Button
                style="width: 100%; height: 42px; font-size: 16px; margin-top: 12px"
                type="primary"
                @click="changePassWord"
                >保存
              </Button>
            </Form>
          </div>
          <div class="golgointxt" style="cursor: default">
            <span @click="goLogin" style="cursor: pointer">去登录</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/utils/util'; // Some commonly used tools
import * as runtime from '@/utils/runtime'; // Runtime information
/* eslint-disable */
import './index.less';
import Vue from 'vue';
import config from '@/config';
import VueAwesomeCountdown from 'vue-awesome-countdown';
Vue.use(VueAwesomeCountdown, 'vac');
import globalConfig from '@/config/index'
// Vue.use(VueAwesomeCountdown, 'vac')

let windowHeight = parseInt(window.outerHeight);

export default {
  name: 'index',

  data() {
    return {
      pageHeight: '',
      countdowning: false,
      // mobile: '',
      // auth_code: '',
      loginData: {
        mobile: '',
        auth_code: '',
        password: '', //密码
        new_password: '', //新密码
        confirm_password: '', //再次密码
      },
      status: 0, //1,验证码登录，0,密码登录
      forgetPassword: false,
      ruleLoginData: {
        mobile: [{ required: true, message: '请填写手机号码', trigger: 'blur' }],
        auth_code: [{ required: true, message: '请填写验证码', trigger: 'blur' }],
        new_password: [{ required: true, message: '请填新密码', trigger: 'blur' }],
        confirm_password: [{ required: true, message: '请再次填写新密码', trigger: 'blur' }],
      },
      barOffset: '',
      timer: null
    };
  },
  computed: {
    barStyle() {
      return {
        transform: `translate3d(${this.barOffset}px,0px,0px)`,
      };
    },
  },
  created() {
    // this.getWindowHeight();
    // window.addEventListener('resize', this.getWindowHeight);
    this.autoLogin()

  },

  methods: {
    autoLogin() {
      let id, isToDetail, isToPatientDetail, clinic_mr_id
      // console.log( S.uri( window.location.href ).query() )
      // S.uri( window.location.href ).query() && ()

      // if ( id ) {
      // this.$store.commit( 'app/SET_TOKEN', true )
      this.login()
      // } else {
      //   this.autoJump()
      // }
    },
    login() {
      console.log(runtime.getLoginInfo())
      if (!runtime.isLogin()) {
        this.autoJump();
      }else{
        // this.$router.replace('/')
        // location.href = '/';
        window.reload('/');
      }
      // this.timer = setInterval(() => {
      //   this.percent += 70;
      // }, 600);

    },
    autoJump() {
      // if ( this.hasToken ) {
      //   return
      // }
      // let env = process.env.VUE_APP_NODE_ENV
      let isDev = process.env.VUE_APP_CMD === 'serve'
      if (isDev) {
        const info ={"uid":"9aE78b","name":"张楠楠","mobile":"***********","account":"987","expires":1,"is_administrator":"0"}
        runtime.writeLoginCookie(info)
        // this.$router.replace('/')
        return
      }
      console.log('跳转到登录页面')
      const a = document.createElement('a')
      a.target = '_self'
      a.href = `${globalConfig.PlatDomain}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      // if (env === 'production') {
      // 	window.location.href = 'https://clinic.rsjxx.com'
      // } else if (env === 'test') {
      // 	window.location.href = 'https://clinic2test.rsjxx.com'
      // 	// window.location.href = 'http://127.0.0.1:3000'
      // 	console.log('要跳了')
      // }else if ( env === 'test-74' ) {
      // 	window.location.href = 'https://74clinic.rsjxx.com'
      // }
    },
    getWindowHeight() {
      this.pageHeight = windowHeight - 110 + 'px';
    },
    // 切换登录方式
    changeTap(state) {
      this.status = state;
      for (let key in this.loginData) {
        this.loginData[key] = '';
      }
      if (state === 0) {
        this.barOffset = '0';
        // this.$nextTick(() => {
        // 	this.$refs.pwdInput.focus()
        // })
      } else {
        this.barOffset = '165';
        // this.$nextTick(() => {
        // 	this.$refs.authInput.focus()
        // })
      }
    },
    // 忘记密码
    goForget() {
      for (let key in this.loginData) {
        this.loginData[key] = '';
      }
      this.forgetPassword = true;
    },
    // 获取验证码
    onCountDownStart() {
      if (this.loginData.mobile.trim() == '') {
        this.$Message.error('请输入手机号');
        return;
      }
      this.$api
        .sendauthcode({ mobile: this.loginData.mobile })
        .then(() => {
          this.$Message.success('发送成功');
          if (!this.forgetPassword) {
            this.$refs.vac.startCountdown(true);
          } else {
            this.$refs.vac2.startCountdown(true);
          }
          this.countdowning = true;
        })
        .catch(error => {

        });
    },
    onCountDownFinish() {
      this.countdowning = false;
    },
    // 登录
    onLogin() {
      let from = this.$route.query.from || '/';
      if (this.status == 1) {
        if (this.loginData.mobile.trim() == '') {
          this.$Message.error('请输入手机号');
          return;
        }
        if (this.loginData.auth_code.trim() == '') {
          this.$Message.error('请输入验证码');
          return;
        }
        let telreg = this.regRole(this.loginData.mobile);
        if (!telreg) {
          this.$Message.error('请输入正确的号码');
          return;
        }
        let { mobile, auth_code } = this.loginData;
        this.$api
          .login({ mobile, auth_code })
          .then(userInfo => {
            console.log('-> userInfo', userInfo);
            let info = {
              uid: userInfo['mer_uid'],
              name: userInfo['user_name'],
              mobile: userInfo['mobile'],
              account: userInfo['account'],
              expires: userInfo['expires'] ? Number(userInfo['expires']) : 7,
              is_administrator: userInfo['is_administrator'],
            };
            runtime.writeLoginCookie(info);
            this.$Message.success('登录成功');

            try {
              let url = S.uri().path(decodeURIComponent(from)).build();
              console.log(url);
              location.href = url;
            } catch (e) {
              location = '/';
            }
          })
          .catch(error => {

          });
      } else {
        if (!this.loginData.mobile) {
          this.$Message.error('请先输入账号');
          return;
        }
        if (!this.loginData.password) {
          this.$Message.error('请先输入密码');
          return;
        }

        const p = JSON.stringify({
          password: this.loginData.password,
          expired_time: Date.parse(new Date()) / 1000,
        });
        console.log('%c=>(login.vue:307) p', 'font-size: 18px;color: #FF7043 ;', p);
        let query = {
          account: this.loginData.mobile,
          password: S.encrypt(
            JSON.stringify({
              password: this.loginData.password,
              expired_time: Date.parse(new Date()) / 1000,
            })
          ),
          version: config.cryptoVersion,
        };

        this.$api
          .accountlogin(query)
          .then(
            userInfo => {
              this.$Message.success('登录成功');
              let info = {
                uid: userInfo['mer_uid'],
                name: userInfo['user_name'],
                mobile: userInfo['mobile'],
                account: userInfo['account'],
                expires: userInfo['expires'] ? Number(userInfo['expires']) : 7,
                is_administrator: userInfo['is_administrator'],
              };
              runtime.writeLoginCookie(info);
              try {
                let url = S.uri().path(decodeURIComponent(from)).build();
                console.log(url);
                location.href = url;
              } catch (e) {
                location = '/';
              }
            },
            err => {
            }
          )
          .catch(e => {});
      }
    },
    // 修改密码
    changePassWord() {
      this.$refs.psdForm.validate(valid => {
        if (valid) {
          if (this.loginData.new_password != this.loginData.confirm_password) {
            this.$Message.error('两次密码不一致');
            return;
          }
          this.getRetrievepass();
        } else {
          this.$Message.error('请完善信息');
        }
      });
    },
    getRetrievepass() {
      let telreg = this.regRole(this.loginData.mobile);
      if (!telreg) {
        this.$Message.error('请输入正确的号码');
        return;
      }
      let query = {
        mobile: this.loginData.mobile,
        auth_code: this.loginData.auth_code,
        new_password: S.encrypt(
          JSON.stringify({
            password: this.loginData.new_password,
            expired_time: Date.parse(new Date()) / 1000,
          })
        ),
        confirm_password: S.encrypt(
          JSON.stringify({
            password: this.loginData.confirm_password,
            expired_time: Date.parse(new Date()) / 1000,
          })
        ),
        version: config.cryptoVersion,
      };
      this.$api
        .changeRetrievepass(query)
        .then(
          res => {
            console.log(res);
            this.$Message.success('修改密码成功');
            this.forgetPassword = false;
            for (let key in this.loginData) {
              this.loginData[key] = '';
            }
          },
          err => {
          }
        )
        .catch(error => {});
    },
    regRole(tel) {
      let flag;
      let reg = /^1[3456789]\d{9}$/;
      flag = reg.test(tel);
      return flag;
    },
    goLogin() {
      for (let key in this.loginData) {
        this.loginData[key] = '';
      }
      this.forgetPassword = false;
    },
  },

  destroyed() {
    window.removeEventListener('resize', this.getWindowHeight);
  },
};
</script>

<style lang="less" scoped>
.ivu-select-single {
}
</style>
