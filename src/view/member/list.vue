<template>
  <div>
    <div class="form-warpper">
      <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
        <Row>
          <Col>
            <FormItem>
              <Input v-model="queryFormData.name" placeholder="请输入员工姓名" clearable />
            </FormItem>
          </Col>

          <Col>
            <FormItem>
              <Input v-model="queryFormData.mobile" placeholder="请输入员工手机号" clearable />
            </FormItem>
          </Col>

          <Col>
            <FormItem>
              <Select v-model="queryFormData.roleid" transfer placeholder="请选择员工角色" filterable clearable>
                <Option v-for="item in roleList" :value="item.id" :key="item.id">{{ item.name }}</Option>
              </Select>
            </FormItem>
          </Col>
          <Col>
            <FormItem>
              <Select v-model="queryFormData.status" placeholder="请选择状态" clearable>
                <Option v-for="item in statusList" :value="item.id" :key="item.id">{{ item.desc }}</Option>
              </Select>
            </FormItem>
          </Col>

          <Col>
            <FormItem>
              <DatePicker
                type="daterange"
                clearable
                format="yyyy-MM-dd"
                placeholder="请选择创建时间"
                v-model="timeRange"
                @on-change="times => handleTimeChange(times)"
                class="time-range"
              ></DatePicker>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col>
            <FormItem style="text-align: left">
              <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
              <Button type="default" class="mr10" @click="onResetSearch">重置</Button>
              <Button type="primary" @click="onOpenAddModal()" v-eleControl="'EM8Mn7yYE7'">
                <Icon size="14" type="md-add" />
                添加员工
              </Button>
            </FormItem>
          </Col>
        </Row>
      </Form>
    </div>

    <Table :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 331" :loading="tableLoading">
      <template slot="name" slot-scope="{ row }">
        {{ row.name }}
      </template>
      <template slot="mobile" slot-scope="{ row }">
        {{ row.mobile }}
      </template>
      <template slot="account" slot-scope="{ row }">
        {{ row.account }}
      </template>
      <template slot="role" slot-scope="{ row }">
        <span v-for="(role, index) in member_roles[row.id]" :key="index">{{
          index == member_roles[row.id].length - 1 ? role.name : role.name + '、'
        }}</span>
      </template>
      <template slot-scope="{ row }" slot="weAppRole">
        {{ row.weapp_role_name || '-' }}
      </template>
      <template slot="status" slot-scope="{ row }">
        <span v-if="row.status == 'OFF'" class="text-danger">{{ statusDesc[row.status].desc }}</span>
        <span v-else>{{ statusDesc[row.status].desc }}</span>
      </template>
      <template slot="create_time" slot-scope="{ row }">
        {{ row.create_time | date_format }}
      </template>
      <template slot="operate" slot-scope="{ row }">
        <div v-eleControl="['EM8Mn7yYE7', '-']">
          <a @click="showChangeModel(row.id)">修改密码</a>
          <Divider type="vertical" />
          <a @click="onOpenAddModal(row.id)">编辑</a>
          <Divider type="vertical" />
          <Poptip v-if="row.status == 'OFF'" confirm title="确定启用？" transfer @on-ok="onStatus(row.id, 'ENABLED')">
            <a>启用</a>
          </Poptip>
          <Poptip v-else confirm title="确定停用？" transfer @on-ok="onStatus(row.id, 'DISABLED')">
            <a>停用</a>
          </Poptip>
        </div>
      </template>
    </Table>

    <div class="block_20"></div>

    <KPage
      :current="+queryFormData.page"
      :page-size="+queryFormData.pageSize"
      :total="+total"
      style="text-align: center"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
    />
    <div v-if="addModal">
      <Modal
        v-model="addModal"
        :loading="addModalLoading"
        :mask-closable="false"
        :title="addModalTitle"
        :width="430"
        class="ks-add-modal"
        @on-visible-change="roleVisibleChange"
      >
        <KWidget :labelWidth="80" label="姓名:">
          <Input v-model="addFormData.name" placeholder="请输入姓名" type="text" />
        </KWidget>
        <KWidget :labelWidth="80" label="手机号:">
          <Input v-model="addFormData.mobile" placeholder="请输入手机号" type="text" />
        </KWidget>
        <KWidget :labelWidth="80" label="账号:">
          <Input v-model="addFormData.account" placeholder="请输入账号" type="text" />
        </KWidget>
        <KWidget v-if="!isedit" :labelWidth="80" label="密码:">
          <Input v-model="addFormData.password" placeholder="请输入密码" type="password" />
        </KWidget>
        <KWidget :labelWidth="80" label="角色:">
          <Select v-model="addFormData.role_ids" placeholder="请选择角色" filterable :multiple="true">
            <Option v-for="(item, key) in roles" :key="key" :value="item.id">{{ item.name }}</Option>
          </Select>
        </KWidget>
        <KWidget label="小程序角色:" :labelWidth="80">
          <Select v-model="addFormData.weapp_role" clearable placeholder="请选择小程序角色">
            <Option v-for="(item, key) in weapp_roles" :value="item.id" :key="key">{{ item.desc }}</Option>
          </Select>
        </KWidget>
        <template slot="footer">
          <div>
            <Button type="default" @click="addModal = false">取消</Button>
            <Button type="primary" @click="onAdd">确定</Button>
          </div>
        </template>
      </Modal>
    </div>
    <!--     Modal -->

    <Modal
      v-model="psdModel"
      :loading="psdModalLoading"
      :mask-closable="false"
      :width="430"
      class="ks-add-modal"
      title="修改密码"
      @on-visible-change="psdVisibleChange"
    >
      <KWidget :labelWidth="70" label="姓名:">
        <div style="line-height: 30px; font-size: 16px">{{ psdInfo.name }}</div>
      </KWidget>
      <KWidget :labelWidth="70" label="新密码:">
        <Input v-model="psdInfo.new_password" type="password" />
      </KWidget>
      <KWidget :labelWidth="70" label="确认密码:">
        <Input v-model="psdInfo.confirm_password" type="password" />
      </KWidget>
      <template slot="footer">
        <div>
          <Button type="default" @click="closepsdModal">取消</Button>
          <Button type="primary" @click="changePassWord">确定</Button>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/utils/util'; // Some commonly used tools
import io from '@/utils/request'; // Http request
/* eslint-disable */
import config from '@/config';
import search from '@/mixins/search';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  mobile: '',
  roleid: '',
  status: '',
  st: '',
  et: '',
  r: '',
};

let init_add_form_data = {
  id: '',
  name: '',
  mobile: '',
  role_ids: '',
  account: '',
  password: '',
  weapp_role: '',
};

const ACTIONS = act => {
  S.log(act, 'act');
  return { ENABLED: '启用', DISABLED: '停用' }[act];
};

export default {
  name: 'list',
  mixins: [search],
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      tableCols: [
        { title: '姓名', slot: 'name' },
        { title: '手机号', slot: 'mobile' },
        { title: '账号', slot: 'account' },
        { title: '角色', slot: 'role' },
        { title: '小程序端角色', slot: 'weAppRole' },
        { title: '状态', slot: 'status', width: 120 },
        { title: '创建时间', slot: 'create_time', width: 150 },
        { title: '操作', slot: 'operate', width: 150 },
      ],
      tableLoading: false,
      list: [],
      total: 0,
      statusDesc: {},
      roles: null,
      add_roles: {},
      edit_roles: [],
      member_roles: {}, // 用户角色的列表
      addModal: false,
      addModalTitle: '添加成员',
      addModalLoading: true,
      addFormData: { ...init_add_form_data },
      isedit: true,
      psdModel: false,
      psdModalLoading: true,
      psdInfo: {
        id: '',
        name: '',
        new_password: '',
        confirm_password: '',
      },
      roleList: [],
      statusList: [],
      timeRange: [],
      weapp_roles: [],
    };
  },

  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.getMemberOptions();
  },

  methods: {
    psdVisibleChange(visible) {
      console.log('-> visible', visible);
      if (!visible) {
        for (const psdInfoKey in this.psdInfo) {
          this.psdInfo[psdInfoKey] = '';
        }
      }
    },
    roleVisibleChange(visible) {
      if (!visible) this.roles = this.add_roles;
      console.log(this.roles);
    },
    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
    },

    // onPageChange: function (page, pageSize) {
    // 	this.queryFormData.page = page
    // 	this.queryFormData.pageSize = pageSize
    // 	this.submitQueryForm()
    // },
    //
    showChangeModel(id) {
      io.get('/pms_plat/member.info', { data: { id } }).then(res => {
        this.psdInfo.id = res.member.id;
        this.psdInfo.name = res.member.name;
        this.psdModel = true;
      });
    },
    changePassWord() {
      if (this.psdInfo.new_password.trim() == '' || this.psdInfo.confirm_password.trim() == '') {
        this.$Message.error('请填写完信息');
        this.psdModalLoading = false;
        return;
      }
      if (this.psdInfo.new_password != this.psdInfo.confirm_password) {
        this.$Message.error('两次密码需填写一致');
        this.psdModalLoading = false;
        return;
      }
      let query = {
        id: this.psdInfo.id,
        new_password: S.encrypt(
          JSON.stringify({
            password: this.psdInfo.new_password,
            expired_time: Date.parse(new Date()) / 1000,
          })
        ),
        confirm_password: S.encrypt(
          JSON.stringify({
            password: this.psdInfo.confirm_password,
            expired_time: Date.parse(new Date()) / 1000,
          })
        ),
        version: config.cryptoVersion,
      };
      this.$api
        .getChgpass(query)
        .then(
          res => {
            this.psdModel = false;
            this.$Message.success('修改密码成功');
            this.psdInfo.new_password = '';
            this.psdInfo.confirm_password = '';
          },
          err => {
            return;
          }
        )
        .catch(e => {});
    },
    closepsdModal() {
      this.psdModel = false;
      this.psdInfo.new_password = '';
      this.psdInfo.confirm_password = '';
    },
    onOpenAddModal: function (id) {
      if (!id) {
        this.addModalTitle = '添加员工';
        this.isedit = false;
        this.addFormData = { ...init_add_form_data };
      } else {
        this.addModalTitle = '编辑员工';
        this.isedit = true;
        io.get('/pms_plat/member.info', { data: { id } })
          .then(data => {
            this.addFormData.id = data.member.id;
            this.edit_roles = data.all_roles;
            this.roles = data.all_roles;
            this.addFormData.name = data.member.name;
            this.addFormData.mobile = data.member.mobile;
            this.addFormData.role_ids = data.member.role_list.map(item => item.roleid);
            this.addFormData.account = data.member.account;
            this.addFormData.password = '';
            this.addFormData.weapp_role = data.member.weapp_role;
          })
          .catch(error => {
            console.log('-> error', error);

          });
      }
      this.addModal = true;
    },

    onAdd: function () {
      let formData = { ...this.addFormData };
      console.log('asdas');
      if (!formData.name.trim()) {
        this.$Message.error('请填写员工姓名');
        this.addModalLoading = false;
        return;
      }
      if (!this.isedit) {
        if (!formData.account.trim()) {
          this.$Message.error('请填写员工账号');
          this.addModalLoading = false;
          return;
        }
        if (!formData.password) {
          this.$Message.error('请填写密码');
          this.addModalLoading = false;
          return;
        }
      }

      if (formData.role_ids.length == 0) {
        this.$Message.error('请选择角色');
        this.addModalLoading = false;
        return;
      }
      let telreg = this.regRole(this.addFormData.mobile);
      if (!telreg) {
        this.addModalLoading = false;
        this.$Message.error('请输入正确的号码');
        return;
      }
      let params = {
        ...formData,
        password: S.encrypt(
          JSON.stringify({
            password: this.addFormData.password,
            expired_time: Date.parse(new Date()) / 1000,
          })
        ),
        version: config.cryptoVersion,
      };
      if (this.isedit) {
        // 如果是编辑，password不要传
        this.$delete(params, 'password');
      }
      io.post('/pms_plat/member.save', params)
        .then(
          () => {
            this.$Message.success('保存成功');
            this.addModalLoading = false;
            this.addModal = false;
            this.submitQueryForm(true);
          },
          err => {
          }
        )
        .catch(error => {
          this.addModalLoading = false;

        });
    },

    onStatus: function (id, act) {
      io.post('/pms_plat/member.status', {
        id: id,
        act: act,
      })
        .then(() => {
          this.$Message.success(ACTIONS(act) + '成功');
          this.submitQueryForm();
        })
        .catch(error => {

        });
    },

    getsList: function () {
      this.tableLoading = true;
      io.get('/pms_plat/member.list', { data: this.queryFormData })
        .then(data => {
          let list = this.handleList(data.members);
          this.list = list;
          this.total = data.total;
          this.statusDesc = data.statusDesc;
          this.member_roles = data.roles;
          this.roles = data.all_roles;
          this.add_roles = data.all_roles;
          this.tableLoading = false;
        })
        .catch(error => {

        });
    },

    handleList: function (list) {
      return list;
    },

    submitQueryForm: function (replace) {
      // 通过修改url参数，触发路由前置守卫(beforeRouteUpdate)，在前置守卫中获取列表数据
      this.queryFormData.r = S.random(6); // 只有在参数发生变化时才会触发前置守卫；所以添加随机数，保证url参数一定有修改
      if (replace) {
        this.$router.replace({ query: this.queryFormData });
      } else {
        this.$router.push({ query: this.queryFormData });
      }
    },
    regRole(tel) {
      let flag;
      let reg = /^1[3456789]\d{9}$/;
      flag = reg.test(tel);
      return flag;
    },
    getMemberOptions() {
      this.$api
        .getMemberOptions()
        .then(res => {
          console.log('-> res', res);
          this.roleList = res.all_roles;
          console.log('-> res.statusDesc', res.statusDesc);
          this.statusList = S.descToArrHandle(res.statusDesc);
          this.weapp_roles = S.descToArrHandle(res.weappRoleDesc);
          console.log('-> statusDesc', this.statusDesc);
        })
    },
  },

  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    this.getTimeRange();
    next();
  },
};
</script>

<style lang="less"></style>
