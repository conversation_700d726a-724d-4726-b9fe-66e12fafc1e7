<template>
  <div class="container">
    <div class="mb10 flex flex-item-align">
      <Button type="primary" @click="createLogin"> <Icon size="14" type="md-add" /> 新建登录鉴权</Button>
    </div>

    <Table :columns="tableColumns" :data="list" :height="$store.state.app.clientHeight - 240">
      <template slot-scope="{ row }" slot="status_desc">
        <status-text :status="row.status"
          ><span>{{ row.status_desc }}</span></status-text
        >
      </template>
      <template slot-scope="{ row }" slot="effect_time">
        {{ row.begin_time | date_format }} - {{ row.end_time | date_format }}
      </template>
      <template slot-scope="{ row }" slot="operator">
        {{ row.operator || '-' }}
      </template>
      <template slot-scope="{ row }" slot="create_time">
        {{ row.create_time | date_format }}
      </template>
      <template slot-scope="{ row }" slot="action">
        <a class="mr10" v-copy="copyText" @click="copyMessage(row)">复制信息</a>
        <a class="mr10" @click="showLoginDetail(row)">登录明细</a>
        <a v-if="row.status === 'enable'" @click="loginDisable(row)">设为失效</a>
      </template>
    </Table>

    <div class="block_20"></div>
    <KPage
      :total="total"
      :page-size="+queryFormData.pageSize"
      :current="+queryFormData.page"
      @on-change="handleCurrentChange"
      @on-page-size-change="handleSizeChange"
      style="text-align: center"
    />
    <login-detail :visible.sync="loginVisible" :id="loginId"></login-detail>
  </div>
</template>

<script>
import LoginDetail from './componets/loginDetail';
import search from '@/mixins/search';
import S from 'utils/util';
import moment from 'moment';
const init_query_form_data = {
  page: 1,
  pageSize: 20
};
export default {
  name: 'authentication-list',
  components: {
    LoginDetail
  },
  mixins: [search],
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  watch: {
    loginVisible: {
      handler(val) {
        if (!val) {
          this.loginId = '';
        }
      }
    }
  },
  data() {
    return {
      apiName: 'getAuthorizationList',
      queryFormData: {
        ...init_query_form_data
      },
      tableColumns: [
        // {type: 'index', width: 60, align: 'center', title: '序号'},
        { title: '环境类型', align: 'center', key: 'plat_desc' },
        { title: '生效时间', align: 'center', slot: 'effect_time', width: 200 },
        { title: '登录手机号', align: 'center', key: 'mobile', width: 100 },
        { title: '登录验证码', align: 'center', key: 'auth_code' },
        { title: '最大登录次数', align: 'center', key: 'max_times', width: 100 },
        { title: '当前次数', align: 'center', key: 'times' },
        { title: '状态', align: 'center', slot: 'status_desc' },
        { title: '申请人', align: 'center', slot: 'operator' },
        { title: '申请时间', align: 'center', slot: 'create_time', width: 80 },
        { title: '操作', align: 'center', slot: 'action', width: 200 }
      ],
      list: [],
      loginVisible: false,
      loginId: '',
      copyText: ''
    };
  },
  methods: {
    createLogin() {
      this.$router.push({
        path: '/member/authentication/create'
      });
    },
    copyMessage(row) {
      this.$Message.success('复制成功');
      let end_time = moment(Number(row.end_time) * 1000).format('YYYY-MM-DD HH:mm:ss');
      const txt =
        '--培训环境信息--\n' +
        `有效期：${end_time}之前有效\n` +
        '登录链接：http://74clinic.rsjxx.com\n' +
        `登录手机号：${row.mobile}\n` +
        `验证码：${row.auth_code}`;
      this.copyText = txt;
    },
    showLoginDetail(row) {
      this.loginId = row.id;
      console.log('=>(list.vue:75) this.id', this.id);
      this.loginVisible = true;
    },
    loginDisable(row) {
      this.$Modal.confirm({
        title: '失效提示',
        content: '您确定使该登录手机号失效吗？',
        onOk: () => {
          this.getAuthorizationDisable(row);
        }
      });
    },

    getAuthorizationDisable(row) {
      let params = { authorization_id: row.id };
      this.$api.getAuthorizationDisable(params).then(res => {
        this.$Message.success('登录手机号已失效');
        this.loadList();
      });
    }
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.loadList();
    next();
  }
};
</script>

<style lang="less" scoped></style>
