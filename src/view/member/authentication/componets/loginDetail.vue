<template>
  <Modal :value="visible" width="900px" title="登录明细" @on-cancel="cancelHandle" :mask-closable="false" footer-hide>
    <div style="height: 100%">
      <div class="flex flex-item-end mb10">
        <span class="mr10">最大登录次数：{{ max_times }}</span
        ><span>已登录次数：{{ times }}</span>
      </div>
      <Table :columns="tableColumns" :data="list">
        <template slot-scope="{ row }" slot="create_time">
          {{ row.create_time | date_format }}
        </template>
      </Table>

      <div class="block_20"></div>
      <KPage
        :total="total"
        :page-size="+queryFormData.pageSize"
        :current="+queryFormData.page"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
        style="text-align: center"
      />
    </div>
  </Modal>
</template>

<script>
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  authorization_id: ''
};
export default {
  watch: {
    id: {
      handler(val) {
        if (val) {
          this.queryFormData.authorization_id = val;
          this.getAuthorizationRecord();
        }
      }
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      tableColumns: [
        { type: 'index', width: 60, align: 'center', title: '序号' },
        { title: '登录IP', align: 'center', key: 'ip' },
        { title: '浏览器', align: 'center', key: 'browser' },
        { title: '登录时间', align: 'center', slot: 'create_time' }
      ],
      list: [],
      max_times: 0,
      times: 0,
      queryFormData: {
        ...init_query_form_data
      },
      total: 0
    };
  },
  methods: {
    cancelHandle() {
      this.$emit('update:visible', false);
      this.queryFormData = { ...init_query_form_data };
    },
    getAuthorizationRecord(val) {
      let params = { ...this.queryFormData };
      this.$api.getAuthorizationRecord(params).then(res => {
        this.list = res.list;
        this.max_times = res.authorization && res.authorization.max_times;
        this.times = res.authorization && res.authorization.times;
        this.total = res.total;
      });
    },
    handleSizeChange(val) {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = val;
      this.getAuthorizationRecord();
    },
    handleCurrentChange(val) {
      this.queryFormData.page = val;
      this.getAuthorizationRecord();
    }
  }
};
</script>

<style lang="less" scoped>
::v-deep .ivu-modal-body {
  padding: 20px 30px;
  height: calc(~'100% - 110px');
  overflow-y: auto;
}
::v-deep .ivu-modal {
  height: calc(~'100% - 100px') !important;
}
::v-deep .ivu-modal-content {
  height: calc(~'100% - 100px');
}
</style>
