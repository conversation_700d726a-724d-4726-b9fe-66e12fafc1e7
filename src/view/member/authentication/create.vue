<template>
  <div class="container">
    <Form :model="queryFormData" label-position="right" :label-width="100" class="formCenter">
      <FormItem label="鉴权环境类型：">
        <Select v-model="queryFormData.plat" style="width: 200px">
          <Option v-for="item in typeList" :value="item.kw" :key="item.kw">{{ item.desc }}</Option>
        </Select>
      </FormItem>
      <FormItem label="演示门店名称：">
        <Select v-model="queryFormData.plat_id" style="width: 200px">
          <Option v-for="item in storeList" :value="item.id" :key="item.id">{{ item.name }}</Option>
        </Select>
      </FormItem>
      <FormItem label="创建人：">
        <Input v-model="queryFormData.operator" style="width: 200px" disabled></Input>
      </FormItem>
      <FormItem label="登录手机号：">
        <Input v-model="queryFormData.mobile" style="width: 200px" readonly></Input>
        <Button class="ml10" @click="getLoginMobile">生成</Button>
      </FormItem>
      <FormItem label="最大登录次数：">
        <InputNumber
          :value="queryFormData.max_times"
          style="width: 200px"
          :min="0"
          :max="100"
          :precision="0"
          @on-change="val => (this.queryFormData.max_times = val)"
          @on-blur="removeZero"
        ></InputNumber>
      </FormItem>
      <FormItem label="鉴权生效时段：">
        <DatePicker
          v-model="queryFormData.begin_time"
          type="datetime"
          placeholder="请选择创建日期"
          style="width: 200px"
          disabled
        ></DatePicker>
      </FormItem>
      <FormItem label="鉴权失效时段：">
        <DatePicker
          :value="queryFormData.end_time"
          :options="options"
          type="datetime"
          placeholder="请选择创建日期"
          style="width: 200px"
          @on-change="val => (this.queryFormData.end_time = val)"
        ></DatePicker>
      </FormItem>
      <FormItem>
        <Button class="mr10" @click="cancel">取消</Button>
        <Button type="primary" @click="confirm">确认并启用</Button>
      </FormItem>
    </Form>
  </div>
</template>

<script>
import moment from 'moment';
import S from '@/utils/util';
import { getUname } from '@/utils/runtime';

const init_query_form_data = {
  env: '',
  plat: '',
  plat_id: '',
  mobile: '',
  max_times: 1,
  begin_time: '',
  end_time: ''
};
export default {
  created() {
    this.queryFormData.begin_time = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
    this.queryFormData.end_time = moment(new Date()).endOf('day').format('YYYY-MM-DD HH:mm:ss');
    console.log('=>(create.vue:59) this.queryFormData.end_time', this.queryFormData.end_time);
    this.queryFormData.operator = getUname();
    this.getAuthorizationOption();
  },

  data() {
    return {
      queryFormData: { ...init_query_form_data },
      typeList: [],
      storeList: [],
      options: {
        disabledDate(date) {
          // return date && date.valueOf() < Date.now() - 86400000;//不能选今天之前的日期
          // return date && date.valueOf() > Date.now() - 86400000;//不能选今天和今天之后的日期
          let time = moment(new Date()).endOf('day').format('YYYY-MM-DD HH:mm:ss');
          let end_time = new Date(time);
          return (date && date.valueOf() < Date.now() - 86400000) || (date && date.valueOf() > end_time); //不能选今天之前的日期
        }
      }
    };
  },
  methods: {
    cancel() {
      this.$router.push({
        path: '/member/authentication/list'
      });
    },

    getLoginMobile() {
      this.$api.getLoginMobile().then(res => {
        console.log('=>(create.vue:83) res', res.mobile);
        this.queryFormData.mobile = res.mobile;
      });
    },

    getAuthorizationOption() {
      this.$api.getAuthorizationOption().then(res => {
        console.log('=>(create.vue:82) res', res);
        this.typeList = S.descToArrHandle(res.platDesc);
        this.queryFormData.plat = this.typeList[0].kw;
        this.storeList = res.platList[this.typeList[0].kw];
        console.log('=>(create.vue:86) this.storeList', this.storeList);
        this.queryFormData.plat_id = this.storeList[0].id;
      });
    },

    removeZero() {
      console.log('=>(create.vue:103) this.queryFormData.max_times', this.queryFormData.max_times);
      if (!this.queryFormData.max_times) {
        this.queryFormData.max_times = 1;
      }
    },

    confirm() {
      if (!this.queryFormData.mobile) {
        this.$Message.error('请生成一个登录手机号');
        return;
      }
      let params = { ...this.queryFormData };
      this.$api
        .getAuthorizationSetting(params)
        .then(res => {
          console.log('=>(create.vue:112) res', res);
          this.$Message.success('创建成功');
          this.$router.push({
            path: '/member/authentication/list'
          });
        })
        .catch(err => {
        });
    },

    changeMaxTimes(val) {}
  }
};
</script>

<style lang="less" scoped>
.formCenter {
  position: absolute;
  left: 50%;
  transform: translate(-50%);
}
</style>
