<template>
  <div>
    <!--    <p>-->
    <!--      <Button type="primary" @click="onOpenCreateRoleModal()">创建角色</Button>-->
    <!--    </p>-->
    <div class="form-warpper_left mb10" v-eleControl="'EomOMWQeVe'">
      <Button type="primary" @click="onOpenCreateRoleModal()">
        <Icon size="14" type="md-add" />
        创建角色
      </Button>
    </div>
    <Table :loading="rolesTableLoading" :columns="rolesTableCols" :data="roles">
      <template slot-scope="{ row }" slot="id"> {{ row.id }} </template>
      <template slot-scope="{ row }" slot="name">
        <a @click="onOpenCreateRoleModal(row)">{{ row.name }}</a>
      </template>
      <template slot-scope="{ row }" slot="set_permission">
        <router-link
          :to="{ path: '/platform/role/permission', query: { roleid: row.id } }"
          >权限管理</router-link
        >
      </template>
      <!--      <template slot-scope="{row}" slot="set_users"> <a href="javascript:;">设置用户</a> </template>-->
      <template slot-scope="{ row }" slot="created"> {{ row.created }} </template>
    </Table>

    <!-- Modal -->
    <Modal
      v-model="createRoleModal"
      :title="modalTitle"
      @on-ok="ok"
      width="300"
      :mask-closable="false"
      :loading="createRoleModalLoading"
    >
      <Input type="text" v-model="formData.name" placeholder="填写角色名称" />
    </Modal>
  </div>
</template>

<script>
import io from 'utils/request';
import { getIsAdministrator } from 'utils/runtime';

let init_formData = {
  roleid: '',
  name: '',
};

export default {
  name: 'list',
  data() {
    return {
      roles: [],
      rolesTableCols: [
        { title: 'ID', slot: 'id' },
        { title: '角色名称', slot: 'name' },
        { title: '权限管理', slot: 'set_permission' },
        // {title: '设置用户', slot: 'set_users'},
        { title: '创建时间', slot: 'created' },
      ],
      rolesTableLoading: false,
      createRoleModal: false,
      createRoleModalLoading: false,
      formData: {
        name: '',
      },
      modalTitle: '新增角色',
      is_administrator: '0', // 是否是超管  '1'：是， '0'：否
    };
  },
  created() {
    this.getsRoles();
    this.is_administrator = getIsAdministrator();
  },
  methods: {
    getsRoles: function (callback = function () {}) {
      this.rolesTableLoading = true;
      io.get('/pms_plat/permission.role.list').then(
        data => {
          this.roles = this.handerRoles(data.roles);
          this.rolesTableLoading = false;
          callback();
        },
        reject => {
        }
      );
    },

    onOpenCreateRoleModal: function (row) {
      this.clearFormData();
      if (row) {
        this.formData.roleid = row.id;
        this.formData.name = row.name;
        this.modalTitle = '编辑角色';
      } else {
        this.modalTitle = '创建角色';
      }
      console.log('-> this.formData', this.formData);
      this.createRoleModal = true;
    },

    ok() {
      if (this.formData.roleid) {
        this.editRoleName();
      } else {
        this.onCreateRole();
      }
    },

    onCreateRole: function () {
      let formData = { ...this.formData };

      if (formData.name.trim() == '') {
        // this.$Message.error('请填写url')
        this.createRoleModalLoading = false;
      }

      io.post('/pms_plat/permission.role.add', formData).then(
        () => {
          this.$Message.success('创建成功');
          this.getsRoles();
        },
        reject => {
          this.addModalLoading = false;

        }
      );
    },

    editRoleName() {
      let params = { ...this.formData };

      if (this.formData.name.trim() == '') {
        // this.$Message.error('请填写url')
        this.createRoleModalLoading = false;
      }
      this.$api
        .editRoleName(params)
        .then(res => {
          this.$Message.success('编辑成功');
          this.getsRoles();
        })
        .catch(err => {
          this.addModalLoading = false;
        });
    },

    handerRoles: function (roles) {
      return roles;
    },

    clearFormData: function () {
      this.formData = { ...init_formData };
    },
  },
  watch: {
    createRoleModalLoading: function () {
      this.$nextTick(() => (this.createRoleModalLoading = true));
    },
  },
};
</script>

<style scoped></style>
