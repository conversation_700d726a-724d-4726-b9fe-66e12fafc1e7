<template>
  <div>
    <div style="width: 400px">
      <div class="widget-form-group">
        <div class="widget-form-label">姓名:</div>
        <div class="widget-form-content">
          <Input type="text" v-model="formData.username" disabled />
        </div>
      </div>
      <div class="widget-form-group">
        <div class="widget-form-label">原始密码:</div>
        <div class="widget-form-content">
          <Input type="password" v-model="formData.oldPassword" />
        </div>
      </div>
      <div class="widget-form-group">
        <div class="widget-form-label">新密码:</div>
        <div class="widget-form-content">
          <Input type="password" v-model="formData.newPassword" />
        </div>
      </div>
      <div class="widget-form-group">
        <div class="widget-form-label">确认密码:</div>
        <div class="widget-form-content">
          <Input type="password" v-model="formData.confirmPassword" />
        </div>
      </div>
      <div class="widget-form-group">
        <div class="widget-form-label"></div>
        <div class="widget-form-content">
          <Button type="primary" @click="onSave" :loading="statemanager.saveBtnLoading">修改</Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
import S from 'utils/util'; // Some commonly used tools
import io from 'utils/request'; // Http request
import * as runtime from 'utils/runtime'; // Runtime information
/* eslint-disable */

export default {
  name: 'udpate',

  data() {
    return {
      orgnization: [],
      formData: {
        uid: '',
        username: '',
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },

      statemanager: {
        saveBtnLoading: false
      }
    };
  },

  created() {
    this.formData.uid = runtime.getUid();

    io.get('pms_plat/permission.member.orgnization').then(data => {
      this.orgnization = data;
    });

    io.get('pms_plat/permission.member.get', { data: { uid: this.formData.uid } }).then(data => {
      this.formData.username = data.name;
    });
  },

  methods: {
    onSave: function () {
      let formData = { ...this.formData };

      this.statemanager.saveBtnLoading = true;

      io.post('pms_plat/permission.member.updatepassword', formData)
        .then(
          () => {
            this.$Message.success('修改成功');
          },
          error => {

          }
        )
        .finally(() => {
          this.statemanager.saveBtnLoading = false;
        });
    }
  },

  watch: {}
};
</script>

<style scoped></style>
