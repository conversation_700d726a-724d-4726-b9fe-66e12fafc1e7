<template>
  <div>
    <p>
      <Button type="primary" to="/platform/member/create">创建帐号</Button>
      <Button to="/platform/role/list">角色管理</Button>
    </p>

    <Table :loading="membersTableLoading" :row-class-name="rowClassName" :columns="membersTableCols" :data="members">
      <template slot-scope="{ row }" slot="uid"> {{ row.uid }} </template>
      <template slot-scope="{ row }" slot="name"> {{ row.name }} </template>
      <template slot-scope="{ row }" slot="department"> {{ row.department }} </template>
      <template slot-scope="{ row }" slot="email"> {{ row.email }} </template>
      <template slot-scope="{ row }" slot="role">
        <span v-for="role in row.roles" :key="role.roleid" class="space4">
          [<a :href="'/platform/role/permission?roleid=' + role.roleid">{{ role.name }}</a
          >]
        </span>
        <a @click="onOpenSetRoleModal(row.uid)">设置</a>
      </template>
      <template slot-scope="{ row }" slot="createtime"> {{ row.create_time }} </template>
      <template slot-scope="{ row }" slot="operate">
        <router-link :to="{ path: '/platform/member/edit', query: { uid: row.uid } }" class="space4"
          >修改信息</router-link
        >
        <Poptip confirm transfer title="确定重置密码?" @on-ok="onResetPassword(row.uid)">
          <a href="javascript:;" class="space4">重置密码</a>
        </Poptip>
        <Poptip v-if="row.is_delete == 0" confirm transfer title="确认删除?" @on-ok="onDelMember(row.uid, 'del')">
          <a href="javascript:;" class="space4">删除</a>
        </Poptip>
        <Poptip v-else confirm transfer title="确认恢复?" @on-ok="onDelMember(row.uid, 'restore')">
          <a href="javascript:;" class="space4">恢复</a>
        </Poptip>
      </template>
    </Table>

    <Modal v-model="openSetRoleModal" title="选择角色" @on-ok="onSetRole" :mask-closable="false">
      <Table ref="selection" :columns="rolesTableCols" :data="roles" @on-selection-change="onSelectionRoles">
        <template slot-scope="{ row }" slot="id"> {{ row.id }} </template>
        <template slot-scope="{ row }" slot="name"> {{ row.name }} </template>
        <template slot-scope="{ row }" slot="permission">
          <a :href="'/platform/role/permission?roleid=' + row.id" target="_blank">查看权限</a>
        </template>
      </Table>
    </Modal>
  </div>
</template>

<script>
/* eslint-disable */
import S from 'utils/util'; // Some commonly used tools
import io from 'utils/request'; // Http request
import * as runtime from 'utils/runtime'; // Runtime information
/* eslint-disable */

// 添加/编辑的表单数据
let init_setRole_formData = {
  uid: 0,
  roleids: []
};

export default {
  name: 'list',
  data() {
    return {
      members: [],
      membersTableCols: [
        { title: 'UID', slot: 'uid', width: 100 },
        { title: '姓名', slot: 'name', width: 120 },
        { title: '部门', slot: 'department', width: 100 },
        { title: '邮箱', slot: 'email' },
        { title: '权限角色', slot: 'role', width: 200 },
        { title: '创建时间', slot: 'createtime', width: 150 },
        { title: '操作', slot: 'operate' }
      ],
      membersTableLoading: false,

      roles: [],
      rolesTableCols: [
        { type: 'selection', align: 'center', width: 90 },
        { title: 'ID', slot: 'id' },
        { title: '角色', slot: 'name' },
        { title: '权限', slot: 'permission' }
      ],

      openSetRoleModal: false,
      checkedRoleIds: [],
      setRole_formData: { ...init_setRole_formData }
    };
  },
  created() {
    this.getsMembers();
  },

  methods: {
    getsMembers: function (callback = function () {}) {
      if (this.members.length == 0) this.membersTableLoading = true;
      io.get('pms_plat/permission.member.list')
        .then(data => {
          this.members = this.handerMembers(data.members);
          this.membersTableLoading = false;
          callback();
        })
        .catch(error => {

        });
    },

    getsRoles: function (callback = function () {}) {
      io.get('pms_plat/permission.role.list').then(
        data => {
          this.roles = this.handerRoles(data.roles);
          callback();
        },
        reject => {
        }
      );
    },

    onResetPassword: function (uid) {
      io.post('pms_plat/permission.member.superreset', {
        uid
      }).then(
        data => {
          this.$Modal.success({ title: '密码重置成功', content: '新密码：' + data.new_password });
        },
        reject => {

        }
      );
    },

    onDelMember: function (uid, act) {
      io.post('pms_plat/permission.member.del', {
        uid,
        act
      }).then(
        () => {
          this.$Message.success('操作成功');
          this.getsMembers();
        },
        reject => {

        }
      );
    },

    onOpenSetRoleModal: function (uid) {
      let roleids = [];
      this.members.forEach(member => {
        if (member.uid == uid)
          member.roles.forEach(role => {
            roleids.push(role.roleid);
          });
      });

      this.getsRoles(() => {
        for (let k in Object.keys(this.roles)) {
          if (S.inArray(this.roles[k].id, roleids)) {
            this.roles[k]['_checked'] = true;
          }
        }
        this.setRole_formData.uid = uid;
        this.openSetRoleModal = true;
      });
    },

    onSelectionRoles: function (roles) {
      let roleids = [];
      roles.forEach(role => {
        roleids.push(role.id);
      });
      this.setRole_formData.roleids = roleids;
    },

    onSetRole: function () {
      let formData = { ...this.setRole_formData };
      formData.roleids = JSON.stringify(formData.roleids);
      io.post('pms_plat/permission.member.setrole', formData).then(
        () => {
          this.$Message.success('保存成功');
          this.getsMembers();
        },
        reject => {
          this.addModalLoading = false;

        }
      );
    },

    handerMembers: function (members) {
      return members;
    },

    handerRoles: function (roles) {
      return roles;
    },

    rowClassName: function (row) {
      if (row.is_delete > 0) {
        return 'delete';
      } else {
        return '';
      }
    }
  },

  watch: {}
};
</script>

<style lang="less">
.delete td {
  background-color: #ddd !important;
  color: #aaa;
}
</style>
