<template>
  <div>
    <div style="width: 400px">
      <div class="widget-form-group">
        <div class="widget-form-label">姓名:</div>
        <div class="widget-form-content">
          <Input type="text" v-model="formData.username" disabled />
        </div>
      </div>
      <div class="widget-form-group">
        <div class="widget-form-label">手机号:</div>
        <div class="widget-form-content">
          <Input type="text" v-model="formData.mobile" />
        </div>
      </div>
      <div class="widget-form-group">
        <div class="widget-form-label">邮箱:</div>
        <div class="widget-form-content">
          <Input type="text" v-model="formData.email" />
        </div>
      </div>
      <div class="widget-form-group">
        <div class="widget-form-label">部门:</div>
        <div class="widget-form-content">
          <Select v-model="formData.department">
            <Option :value="0">请选择部门</Option>
            <Option v-for="item in orgnization" :value="item.id" :key="item.id">{{ item.name }}</Option>
          </Select>
        </div>
      </div>
      <div class="widget-form-group">
        <div class="widget-form-label"></div>
        <div class="widget-form-content">
          <Button type="primary" @click="onSave" :loading="statemanager.saveBtnLoading">保存</Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
import S from 'utils/util'; // Some commonly used tools
import io from 'utils/request'; // Http request
import * as runtime from 'utils/runtime'; // Runtime information
/* eslint-disable */

export default {
  name: 'edit',

  data() {
    return {
      orgnization: [],
      formData: {
        uid: '',
        username: '',
        mobile: '',
        email: '',
        department: ''
      },

      statemanager: {
        saveBtnLoading: false
      }
    };
  },

  created() {
    this.formData.uid = this.$route.query.uid;

    io.get('pms_plat/permission.member.orgnization').then(data => {
      this.orgnization = data;
    });

    io.get('pms_plat/permission.member.get', { data: { uid: this.formData.uid } }).then(data => {
      this.formData.username = data.name;
      this.formData.mobile = data.mobile;
      this.formData.email = data.email;
      this.formData.department = data.department;
    });
  },

  methods: {
    onSave: function () {
      let formData = { ...this.formData };

      this.statemanager.saveBtnLoading = true;

      io.post('pms_plat/permission.member.update', formData)
        .then(
          () => {
            this.$Message.success('保存成功');
          },
          error => {

          }
        )
        .finally(() => {
          this.statemanager.saveBtnLoading = false;
        });
    }
  },

  watch: {}
};
</script>

<style scoped></style>
