<template>
  <div>
    <Modal
      :value="value"
      :mask-closable="false"
      :styles="{ top: '45px' }"
      @on-visible-change="visibleChange"
      :width="800"
      footer-hide
    >
      <template slot="header">
        <Button type="primary" @click="addModal = true">新增</Button>
      </template>
      <div style="max-height: 350px; overflow: auto" v-if="value">
        <Table :columns="elementListTableCol" :data="elementList">
          <template slot="operate" slot-scope="{ row }">
            <a class="space4" href="javascript:;" @click="editElement(row)">编辑</a>
            <Poptip confirm title="确认删除?" transfer @on-ok="onDelElement(row.id)">
              <a class="space4" href="javascript:;">删除</a>
            </Poptip>
          </template>
        </Table>
      </div>
    </Modal>

    <!-- 新增元素 -->
    <Modal v-model="addModal" :loading="addModalLoading" :mask-closable="false" :width="480" @on-ok="confirm">
      <p></p>
      <div style="width: 80%">
        <div class="widget-form-group">
          <div class="widget-form-label">元素名称:</div>
          <div class="widget-form-content">
            <Input v-model="formData.name" type="text" />
          </div>
        </div>
        <div class="widget-form-group">
          <div class="widget-form-label">元素类型:</div>
          <div class="widget-form-content flex flex-item-align">
            <RadioGroup v-model="formData.type">
              <Radio label="element"><span>元素</span></Radio>
            </RadioGroup>
          </div>
        </div>
        <div class="widget-form-group" v-if="isEdit">
          <div class="widget-form-label">元素编码:</div>
          <div class="widget-form-content">
            <Input v-model="formData.idcode" type="text" />
          </div>
        </div>
      </div>
    </Modal>
  </div>
</template>

<script>
import S from 'utils/util'; // Some commonly used tools
let init_formData = {
  name: '',
  type: 'element',
  idcode: '', // 元素编码
  id: '' // 元素id
};
export default {
  name: 'accessModal',
  components: {},
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: false
    },
    accessId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      elementListTableCol: [
        { title: '元素名称', key: 'name' },
        { title: '元素类型', key: 'type' },
        { title: '元素编码', key: 'idcode' },
        { title: '操作', slot: 'operate' }
      ],
      elementList: [],
      currentEditMenuid: 0,

      addModal: false,
      addModalLoading: false,
      formData: { ...init_formData }, // 元素数据
      isEdit: false
    };
  },
  computed: {},
  watch: {
    value(val) {
      if (val) {
        this.getElementlist();
      } else {
        this.elementList = [];
      }
    },
    addModal(val) {
      if (!val) {
        this.formData = { ...init_formData };
        this.isEdit = false;
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    // 关闭弹窗
    closeModal() {
      this.$emit('input', false);
    },
    // 弹窗状态发生改变
    visibleChange(val) {
      if (!val) {
        this.closeModal();
        this.$emit('update:accessId', '');
      }
    },

    /**
     * @description: 添加元素相关事件
     *
     */

    //  编辑元素
    editElement(row) {
      this.isEdit = true;
      this.addModal = true;
      this.formData.name = row.name;
      this.formData.type = row.type;
      this.formData.idcode = row.idcode;
      this.formData.id = row.id;
    },

    // 删除元素
    onDelElement(id) {
      let params = { id };
      this.$api.delResource(params).then(
        res => {
          this.$Message.success('删除成功');
          this.getElementlist();
          this.$parent.getsMenus();
        },
      );
    },

    confirm() {
      if (!this.formData.name) {
        this.$Message.error('元素名称不可为空');
        return;
      }
      if (!this.formData.type) {
        this.$Message.error('元素类型不可为空');
        return;
      }
      if (this.isEdit) {
        if (!this.formData.idcode) {
          this.$Message.error('元素编码不可为空');
          return;
        }
        // 编辑元素
        this.editelement();
      } else {
        // 新增元素
        this.addelement();
      }
    },

    // api-获取元素列表
    getElementlist() {
      let params = {
        parent_id: this.accessId
      };
      this.$api.getElementlist(params).then(
        res => {
          if (S.isEmptyObject(res)) {
            this.elementList = [];
          } else {
            this.elementList = res;
          }
        },
      );
    },

    // api-添加元素
    addelement() {
      let params = {
        name: this.formData.name,
        type: this.formData.type,
        parent_id: this.accessId
      };
      this.$api.addelement(params).then(
        res => {
          this.$Message.success('元素添加成功');
          this.getElementlist();
          this.$parent.getsMenus();
        },
        error => {}
      );
    },

    // api-编辑元素
    editelement() {
      let params = {
        id: this.formData.id,
        idcode: this.formData.idcode,
        name: this.formData.name
      };
      this.$api.editelement(params).then(
        res => {
          this.$Message.success('编辑成功');
          this.getElementlist();
          this.$parent.getsMenus();
        },
      );
    }
  },
  filters: {}
};
</script>

<style lang="less" scoped></style>
