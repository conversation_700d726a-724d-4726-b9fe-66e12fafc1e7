import { cloneDeep } from 'lodash';

export default {
  methods: {
    async createDownloadCenterMission(type, script_conds) {
      const clickEvent = event;
      let params = {
        type,
        script_conds,
        is_async: 2
      };
      console.log('=>(detail.vue:241) params', params);

      return await this.$api
        .createDownloadCenterMission(params)
        .then(res => {
          // this.downloadAnimation(clickEvent);
          this.download(res.url)
        })
        .catch(err => {
          console.log('=>(downloadCenter.js:23) err', err);

        });
    },
    download(url, title = '') {
      const downloadLink = document.createElement('a');
      downloadLink.setAttribute('href', url);
      downloadLink.setAttribute('target', '_blank');
      downloadLink.setAttribute('style', 'display:none');
      downloadLink.setAttribute('download', title);
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    }
    // 下载动画
    // downloadAnimation(event) {
    //   const x = event.clientX - 20;
    //   const y = event.clientY - 20;
    //
    //   console.log(x, y);
    //   console.log(document.body.clientWidth, document.body.clientHeight);
    //   this.createBall(x, y);
    // },
    // createBall(left, top) {
    //   const bar = new Image();
    //   bar.src = require('@/assets/image/downloadCenter/xlsx.png');
    //   bar.style.position = 'fixed';
    //   bar.style.left = left + 'px';
    //   bar.style.top = top + 'px';
    //   bar.style.width = '30px';
    //   bar.style.height = '30px';
    //   bar.style.borderRadius = '50%';
    //   bar.style.zIndex = '100';
    //   bar.style.transition = 'left .6s linear, top .6s cubic-bezier(0.5, 1.5, 1, 1)';
    //   document.body.appendChild(bar);
    //   setTimeout(() => {
    //     const downloadCenterRect = document.getElementById('downloadCenter').getBoundingClientRect();
    //     console.log('=>(download.vue:44) downloadCenterRect', downloadCenterRect);
    //
    //     const x = downloadCenterRect.x;
    //     const y = downloadCenterRect.y;
    //     bar.style.top = y + 'px';
    //     bar.style.left = x + 'px';
    //   }, 0);
    //   const that = this;
    //   bar.addEventListener(
    //     'transitionend',
    //     function () {
    //       this.remove();
    //       that.$Notice.info({
    //         desc: '下载任务已提交, 请前往下载中心查看进度',
    //         render: h => {
    //           return h('span', [
    //             '下载任务已提交, 请前往',
    //             h(
    //               'a',
    //               { on: { click: () => that.$store.commit('downloadCenter/CHANGE_DOWNLOAD_VISIBLE', true) } },
    //               '下载中心'
    //             ),
    //             '查看进度',
    //           ]);
    //         },
    //       });
    //     },
    //     { once: true }
    //   );
    // },
  },
};
