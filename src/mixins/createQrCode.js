import QRCode from '@/utils/canavs2img/qrCode';
export default {
  data() {
    return {};
  },
  methods: {
    /**
     * @description: 生成二维码
     * @param { String } url
     * @param { String } ref 当前div绑定ref,默认为QrCode
     * @param { String } obj.QR_qrColor 二维码颜色
     * @param { String } obj.QR_qrBgColor 二维码背景色
     * @param { String } obj.QR_width 二维码宽度
     * @param { String } obj.QR_height 二维码高度度
     * @param { url } base64 是否将canvas转化为base64抛出
     *
     */
    _creatQrCode(url, ref = 'QrCode', obj = {}, base64 = false) {
      let style = {
        QR_qrColor: '#333333', //二维码颜色
        QR_qrBgColor: '#ffffff', //二维码背景色
        QR_width: 400, // 二维码宽度
        QR_height: 400, // 二维码高度度
        ...obj
      };
      this.$refs[ref].innerHTML = '';
      console.log('-> %c this.$refs[ref]  === %o', 'font-size: 15px;color: green;', this.$refs[ref]);
      let QrCodeDiv = new QRCode(this.$refs[ref], {
        text: url,
        width: style.QR_width,
        height: style.QR_height,
        colorDark: style.QR_qrColor, //二维码颜色
        colorLight: style.QR_qrBgColor, //二维码背景色
        correctLevel: QRCode.CorrectLevel.L //容错率，L/M/H
      });
      console.log('-> QrCodeDiv._el.title', QrCodeDiv._el.title);
      // 关闭url地址
      QrCodeDiv._el.title = '';

      if (base64) {
        let canvas = QrCodeDiv._el.querySelector('canvas'); //获取生成二维码中的canvas，并将canvas转换成base64

        var base64Text = canvas.toDataURL('image/png', 1.0);
        return base64Text;
      }
    }
  }
};
