export default {
  data() {
    return {
      downloadApiName: '',
      downloadLoading: false
    };
  },
  methods: {
    // 导出excel
    downloadExcel(downloadQuery = {}) {
      const { downloadApiName } = this;
      if (downloadApiName == '') {
        this.$Message.error('接口名称未接入');
        return;
      }
      this.downloadLoading = true;
      this.$api[downloadApiName](downloadQuery)
        .then(
          res => {
            this.download(res.url);
          },
        )
        .finally(() => (this.downloadLoading = false));
    },
    // 通过a标签下载
    download(url) {
      const downloadLink = document.createElement('a');
      downloadLink.setAttribute('href', url);
      downloadLink.setAttribute('target', '_blank');
      downloadLink.setAttribute('style', 'display:none');
      downloadLink.setAttribute('download', '');
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    }
  }
};
