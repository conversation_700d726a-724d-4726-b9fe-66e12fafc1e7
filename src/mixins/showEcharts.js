export default {
  computed: {
    isShowLineEcharts() {
      return (options, type, Axis = 'yAxis') => {
        if (type === 'line') {
          // 是否显示面积图
          let keys = Object.keys(options);
          try {
            if (keys.length && options.xAxis.data && options.xAxis.data.length) {
              return true;
            } else {
              return false;
            }
          } catch {}
        } else if (type === 'pie') {
          // 是否显示饼图
          if (options.series && options.series.length) {
            return true;
          } else {
            return false;
          }
        } else if (type === 'bar') {
          if (options[Axis]) {
            // 是否显示柱状图
            return options[Axis].data.length > 0 ? true : false;
          } else {
            return false;
          }
        } else {
          // 如果没有指定类型,则默认返回true
          return true;
        }
      };
    }
  }
};
