import S from '@/utils/util';

export default {
  data() {
    return {};
  },
  mounted() {},
  methods: {
    initPieChart(id, data, plab = true) {
      let showed = false;
      if (data.seriesData.data.length <= 0) {
        showed = true;
      }
      let pieChart = this.$echarts.init(document.getElementById(id));
      pieChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} ({d}%)'
        },
        title: {
          show: showed, // 是否显示title
          text: '暂无数据',
          left: 'center',
          top: 'center',
          textStyle: {
            color: '#000',
            fontSize: 12,
            fontWeight: 400
          }
        },
        color: ['#5470C6', '#EE6666', '#73C0DE', '#91CC75', '#FAC858', '#FFC0CB', '#DA70D6'],
        legend: {
          orient: 'horizontal',
          bottom: '10%',
          data: data.legendData || []
        },
        series: [
          {
            name: data.seriesData.name,
            type: 'pie',
            radius: data.seriesData.radius,
            center: ['50%', '40%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            // width:'270px',
            // height:'270px',
            emphasis: {
              label: {
                show: plab,
                fontSize: '30',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: true
            },
            data: data.seriesData.data
          }
        ]
      });
    },

    initbarXChart(id, data) {
      let showed = false;
      if (data.data.length <= 0) {
        showed = true;
      }
      let initChart = this.$echarts.init(document.getElementById(id));
      initChart.setOption({
        color: ['#CAF982', '#EC808D', '#81D3F8'],
        // tooltip:{
        //   trigger: 'axis',
        //   axisPointer:{
        //     type:'shadow'
        //   }
        // },
        title: {
          show: showed, // 是否显示title
          text: '暂无数据',
          left: 'center',
          top: 'center',
          textStyle: {
            color: '#000',
            fontSize: 12,
            fontWeight: 400
          }
        },
        grid: {
          left: '',
          right: 80,
          bottom: '3%',
          containLabel: true //grid 区域是否包含刻度
        },
        yAxis: [
          {
            type: 'category',
            data: data.catedata,
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            }
          }
        ],
        xAxis: [
          {
            type: 'value',
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisLabel: {
              show: false
            }
          }
        ],
        series: [
          {
            data: data.data,
            type: 'bar',
            label: data.label,
            barMaxWidth: 32,
            barCategoryGap: '30px'
          }
        ]
      });
    }
  }
};
