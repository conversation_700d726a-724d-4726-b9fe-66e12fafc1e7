export default {
  data() {
    return {
      // succ_num: 0, // 上传成功的数据条数
      // fail_num: 0, // 上传失败的数据条数
      // reportList: [], // 上传成功后,查看报告的数据
      // isImportSuccess: false, // 是否导入成功
      excelUploadLoading: false, // 数据上传的loading,批量上传时,只有在最后一次上传置为false
      // excelUploadApiName: '', // excel数据上传的接口api
      excelUpdateIndex: 0, // 当前进行批量的次数
      chunkItemsLength: 0, // 总共要轮询的次数
      percentTotalLength: 0, // 要上传的所有数据
    };
  },
  computed: {
    uploadPercent() {
      let currentLenth = Number(this.succ_num) + Number(this.fail_num);
      if (this.percentTotalLength == 0) {
        return 0;
      } else {
        return $operator.divide(Number(currentLenth) * 100, Number(this.percentTotalLength), 0);
      }
    },
  },
  methods: {
    /**
     * @description:
     *  1: 上传处理后的数据
     *  2: 第一次上传的时候,要将报告相关数据清空
     *  3: 最后一次批量上传完成，调用列表接口刷新数据
     */
    _batchUpload(list, totalList) {
      if (!this.excelUploadApiName) {
        throw Error('请先填写_excelUploadApiName的api');
      }
      this.$api[this.excelUploadApiName]({ batch_params: list }).then(
        res => {
          this.succ_num += Number(res.succ_num);
          this.fail_num += Number(res.fail_num);
          this.reportList = this.reportList.concat(res.fail_data);

          this.excelUpdateIndex++;
          this._btachUploadPolling(totalList);

          if (this.chunkItemsLength == this.excelUpdateIndex) {
            // 导入结束
            this.isImportSuccess = true;
            // 当最后一次请求结束,按钮loading置为false
            this.excelUploadLoading = false;
            // 重新拉取页面数据
            this.getsList();
          }
        },
      );
    },

    /**
     * @description:
     *  1: 将处理好的excel数据,进行切割,利用递归，批量上传,
     *  2: excelUpdateIndex:初始值为0,经历过一次轮询，次数+1
     * @param { number } 批量请求的最大数据量,默认50
     * @param { newAction } true: 表示一个新的文件导入
     */
    _btachUploadPolling(handleExcelList, newAction = false, number = 100) {
      if (newAction) {
        this.percentTotalLength = handleExcelList.length;
        this.reportList = [];
      }
      // 轮询开始,loading开始
      this.excelUploadLoading = true;

      // 切割数据
      let chunkItems = this.$lodash.chunk(handleExcelList, number);
      this.chunkItemsLength = chunkItems.length;

      if (this.excelUpdateIndex < +this.chunkItemsLength) {
        this._batchUpload(chunkItems[this.excelUpdateIndex], handleExcelList);
      }
    },
    /**
     * @description:
     *  1: 初始化上传相关数据
     *  2: 目前默认使用场景是弹窗关闭时
     */
    _initUploadData() {
      this.succ_num = 0;
      this.fail_num = 0;
      this.excelUpdateIndex = 0;
      this.chunkItemsLength = 0;
      this.percentTotalLength = 0;
      this.excelUploadLoading = false;
    },
  },
};
