const empty = {
  name: 'empty',
  bind: function (el, binding) {
    // console.log("-> %c value  === %o ", "font-size: 15px", value)
    el.innerHTML = binding.value || '-';
  },
  //被绑定元素插入父节点时调用（父节点存在即可调用，不必存在于 document 中）。
  inserted: function () {
    //插入节点
  },
  /**
   * 被绑定元素所在模板完成一次更新周期时调用。
   * @param {*} el 当前元素
   * @param {*} 获取当前绑定的值
   */
  componentUpdated: function (el, binding) {
    el.innerHTML = binding.value || '-';
  },
  //只调用一次， 指令与元素解绑时调用。
  unbind: function (el, { value }) {}
};

export default empty;
