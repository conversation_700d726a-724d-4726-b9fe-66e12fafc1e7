const copy = {
  name: 'copy',
  /**
   *初始化绑定，只调用一次，指令第一次绑定到元素时调用，可以定义一个在绑定时执行一次的初始化动作
   * @param {*} el 当前的元素
   * @param {*} 例如：v-copy="测试文字" { value }是binding.value的缩写，可以直接获取value，也就是“测试文字”
   */
  bind: function (el, { value }) {
    el.newvalue = value;
    el.handler = function () {
      if (!el.newvalue) {
        return;
      }
      let textarea = document.createElement('textarea');
      textarea.style.position = 'absolute';
      textarea.style.left = '-1000px';
      textarea.style.readOnly = 'readonly';
      textarea.value = el.newvalue;
      document.body.appendChild(textarea);
      textarea.select();

      let result = document.execCommand('copy');
      if (result) {
        console.log('复制成功');
      }
      document.body.removeChild(textarea);
    };
    el.addEventListener('click', el.handler);
  },
  //被绑定元素插入父节点时调用（父节点存在即可调用，不必存在于 document 中）。
  inserted: function () {
    //插入节点
  },
  /**
   * 被绑定元素所在模板完成一次更新周期时调用。
   * @param {*} el 当前元素
   * @param {*} 获取当前绑定的值
   */
  componentUpdated: function (el, { value }) {
    el.newvalue = value;
  },
  //只调用一次， 指令与元素解绑时调用。
  unbind: function (el, { value }) {
    el.removeEventListener('click', el.handler);
  }
};

export default copy;
