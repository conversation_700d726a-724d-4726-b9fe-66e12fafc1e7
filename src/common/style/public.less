.f-title {
  font-size: 16px;
  font-weight: 700;
  padding-bottom: 10px;
}

.f-title-16 {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 20px;
}

.f-title-20 {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 20px;
}

.no-wrap {
  display: flex;
  flex-wrap: nowrap;
}

.hidden-scroll::-webkit-scrollbar {
  display: none;
}

.hidden-scroll {
  scrollbar-width: none; //兼容火狐
  -ms-overflow-style: none; //兼容IE10
}
.rxj-pop-select {
  display: none;
}
.section-header {
  display: flex;
  align-items: center;
  padding: 5px 0;
  line-height: 18px;

  .section-mark {
    height: 14px;
    border-left-style: solid;
    border-left-width: 4px;
    border-radius: 2px;
    border-left-color: rgb(35, 98, 251);
  }

  .section-title {
    flex-shrink: 0;
    margin-left: 8px;
    font-size: 16px;
    font-weight: 600;
  }
}

.common-modal-form {
  .mt8 {
    margin-top: 8px;
  }
  .create-section {
    display: flex;
    display: -webkit-flex;
    flex-wrap: wrap;
    -webkit-flex-wrap: wrap;
  }
  .common-form-item {
    padding: 12px 12px 0;
    margin-bottom: 0;
    width: 50%;
    .ivu-form-item-error-tip {
      position: relative;
      top: auto;
      left: auto;
    }
  }
  .ivu-form-item-label {
    font-size: 14px;
    line-height: 1.5;
    color: #6b778c;
    word-break: break-all;
    word-wrap: break-word;
    padding-bottom: 4px !important;
  }
}

// 处理数据重置后focus样式未清除的情况
.ivu-select-item-focus {
  background-color: transparent;
}

.ivu-select-item-selected {
  background-color: #f3f3f3;
}
.error_msg {
  color: #ed4014;
}
.el-cascader .el-input.is-focus .el-input__inner {
  border-color: #447cdd;
  box-shadow: 0 0 0 2px rgba(20, 89, 209, 0.2);
}
.el-cascader .el-input .el-input__inner:focus,
.el-cascader .el-input.is-focus .el-input__inner {
  border-color: #447cdd;
}
.el-cascader-node.in-active-path,
.el-cascader-node.is-active,
.el-cascader-node.is-selectable.in-checked-path {
  color: #447cdd;
}

// el-select下拉框样式
.el-select-dropdown__item {
  padding: 0 10px;
  font-size: 13px;
}

// el-cascader下拉框样式
.el-cascader-node {
  font-size: 13px;
  padding-left: 6px;
}
.el-select-dropdown__list .selected {
  color: #447cdd;
}
.error-text{
  color: #f5222d;
}
.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  .ivu-modal {
    top: -40px;
  }
}
.error_msg {
  color: #ed4014;
}
