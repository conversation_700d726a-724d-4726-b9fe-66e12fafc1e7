import Vue from 'vue';
import App from './App.vue';
import router from './router';
import store from './store';
import ViewUI from 'view-design';
import config from '@/config';
import './plugins/element.js';
import './plugins/svg-icon.js';
import './plugins/iview';
import 'font-awesome/css/font-awesome.min.css';
import './index.less';
import './flex.less';
import '@/assets/font/font.css';
import '@/common/style/public.less';
// import imageUrl from '@/utils/imageUrl' // 图片样式处理工具
import util from '@/utils/util'; // 工具
import VuePageStack from '@/components/vue-page-stack'; // 页面导航管理器
import draggable from 'vuedraggable';
import * as echarts from 'echarts';
import * as filters from '@/utils/filters';
import { Bus } from '@/utils/bus';
import API from './api';
import _ from 'lodash';

var moment = require('moment');
import request from '@/utils/request';
import prints from '@/utils/print';
import Viewer from 'v-viewer';
import 'viewerjs/dist/viewer.css';
import KPage from '@/components/k-page';
import KWidget from '@/components/k-widget';
import KLink from '@/components/k-link';
import eChartFn from '@/components/k-configure-echarts/index.js';
import ChartPanel from '@/components/k-configure-echarts/index.vue';
import Dvd from '@/components/dvd';
import BackButton from '@/components/BackButton/BackButton';
import MarkStatus from './components/MarkStatus';
import StatusText from '@/components/StatusText';
import './directives';

import AuditRefuse from './components/AuditButton/AuditRefuseBtn.vue';
import AuditPass from './components/AuditButton/AuditPassBtn.vue';
import FullTabs from '@/plugins/jump-tabs.js';
Vue.use(FullTabs);
Vue.component('AuditRefuse', AuditRefuse);
Vue.component('AuditPass', AuditPass);
Vue.prototype.$moment = moment;
Vue.prototype._ = _;
Vue.prototype.$lodash = _;
Vue.use(prints);
Vue.use(Viewer, {
  defaultOptions: {
    zIndex: 200000,
  },
});
Viewer.setDefaults({
  Options: {
    inline: true,
    button: true,
    navbar: true,
    title: true,
    toolbar: true,
    tooltip: true,
    movable: true,
    zoomable: true,
    rotatable: true,
    scalable: true,
    transition: true,
    fullscreen: true,
    keyboard: true,
    url: 'data-source',
  },
});
!util.isProdEnv && require('./mock');

for (const key in filters) {
  // console.log('-> %c key  ===    %o', 'font-size: 15px;color: #fa8c16 ;', key);
  Vue.filter(key, filters[key]);
}
Vue.prototype.$http = request;
Vue.prototype.$eChartFn = eChartFn;
//事件bus
Vue.prototype.$bus = new Bus();
Vue.prototype.$api = new API(request);
Vue.use(ViewUI, {
  transfer: true,
  capture: false,
});
Vue.prototype.$Message.config({
  duration: 4,
});
Vue.prototype.$Notice.config({
  top: 70,
  duration: 3,
});
Vue.use(VuePageStack, { router });
Vue.component('Dvd', Dvd);

Vue.component('KPage', KPage);
Vue.component('KWidget', KWidget);
Vue.component('KLink', KLink);
Vue.component('MarkStatus', MarkStatus);
Vue.component('StatusText', StatusText);
Vue.component(ChartPanel.name, ChartPanel);
Vue.component('draggable', draggable);
Vue.component('BackButton', BackButton);
Vue.prototype.$echarts = echarts;
if (config.codeVersrequestn == undefined)
  util.log('环境变量VUE_APP_CODE_VERSrequestN未定义', 'VUE_APP_CODE_VERSrequestN');
/**
 * 生产环境关掉提示
 */
Vue.config.productrequestnTip = false;

if (!util.isProdEnv) util.log(process.env.VUE_APP_NODE_ENV, 'NODE_ENV');
new Vue({
  router,
  store,
  render: h => h(App),
  mounted() {
    store.dispatch('app/setClientHeight', document.documentElement.clientHeight).then();
  },
}).$mount('#app');

window.addEventListener('resize', () => {
  return (() => store.dispatch('app/setClientHeight', document.documentElement.clientHeight))();
});
