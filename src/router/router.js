import Main from '@/components/main';

// see ./README.md
const devRouterFiles = require.context('./', true, /\.dev\.js$/);
const devRoutes = devRouterFiles.keys().reduce((routes, path) => {
  routes.push(...devRouterFiles(path).default);
  return routes;
}, []);

/**
 * 开发调试页面
 * see ./README.md
 */
export const developRoutes = devRoutes;

/**
 * 固定路由 [无权限页面]
 */
export const constantRoutes = [
  {
    path: '/login',
    name: 'login',
    meta: {
      title: '登录',
      notNeedLogin: true // 不需要登录
    },
    component: () => import('@/view/login/login')
  }
];

/**
 * 404路由
 */
export const errorRoutes = [
  {
    path: '*',
    component: Main,
    redirect: '/'
  }
];
