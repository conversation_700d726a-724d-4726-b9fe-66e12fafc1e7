import Vue from 'vue';
import VueRouter from 'vue-router';
import store from '@/store';
import { constantRoutes } from './router';
import S from '@/utils/util';
import * as runtime from '@/utils/runtime';
// import {LoadingBar} from 'view-design'
Vue.use(VueRouter);

const router = new VueRouter({
  mode: 'history',
  base: '/',
  routes: constantRoutes
});

router.beforeEach(async (to, from, next) => {
  Vue.prototype.$Modal.remove();
  // LoadingBar.start();
  if (runtime.isLogin()) {
    if (to.path === '/login') {
      next('/');
    }
    const has = store.state.router.routes && store.state.router.routes.length > 0;
    if (has) {
      next();
    } else {
      let debug = false,
        userRoutes = [];
      if (!debug) {
        userRoutes = await store.dispatch('router/getUserRoutes');
      } else {
        //初始化项目时没有路由的情况下使用下面的逻辑，来访问已有的页面，如:菜单管理页面...
        userRoutes.push({
          path: to.path,
          component: () => import(`../view${to.path}`)
        });
        store.commit('router/SET_ROUTES', userRoutes);
      }
      console.log('%c=>(index.js:43) userRoutes', 'font-size: 18px;color: #FF7043 ;', userRoutes)

      userRoutes.forEach(route => {
        router.addRoute(route);
      });
      next({ ...to, replace: true });
    }
  } else {
    runtime.logout();
    if (to.matched.some(_ => _.meta.notNeedLogin)) {
      next();
    } else {
      next('/login?from=' + encodeURIComponent(to.fullPath));
    }
  }
});

router.afterEach(to => {
  S.setTitle(to);
  window.scrollTo(0, 0);
  store.commit('app/SHOW_HELP_WRAPPER', false);
  // LoadingBar.finish();
  // 20210506 by lsm 尝试解决开启Modal浏览器返回前一个页面时，body中overflow hidden样式未清除问题
  document.body.removeAttribute('style');
  // Vue.prototype.$Modal.remove()
});

export default router;
