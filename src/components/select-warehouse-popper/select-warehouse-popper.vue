<template>
  <el-popover
    placement="bottom"
    width="700"
    transfer
    :visible-arrow="false"
    :poper-options="popOptions"
    popper-class="pop-common"
    @show="onShow"
    v-model="showPop"
    :disabled="isDetail"
    trigger="click"
  >
    <slot slot="reference" style="width: 100%"></slot>
    <div class="popover-content-box">
      <div class="header-box">
        <h4 class="title">选择仓库</h4>
        <div class="search-box">
          <span>搜索：</span>
          <Input v-model="queryFormData.name" placeholder="请输入仓库名称" @keyup.enter.native="onSearch" class="mr10">
            <Button slot="append" icon="ios-search" @click="onSearch"></Button>
          </Input>
          <Input v-model="queryFormData.code" placeholder="请输入仓库编码" @keyup.enter.native="onSearch">
            <Button slot="append" icon="ios-search" @click="onSearch"></Button>
          </Input>
        </div>
      </div>
      <div class="table-wrapper">
        <Table :columns="tableCols" :data="list" ref="section-table" height="300" :loading="tableLoading">
          <!-- 勾选 -->
          <template slot-scope="{ row, index }" slot="checkBox">
            <Checkbox :value="list[index].checked" @on-change="changeSelectOrder(row, index)"></Checkbox>
          </template>

          <!-- 联系人 -->
          <template slot-scope="{ row }" slot="contact">
            {{ row.contact || '-' }}
          </template>

          <!-- 联系电话 -->
          <template slot-scope="{ row }" slot="mobile">
            {{ row.mobile || '-' }}
          </template>
        </Table>
        <div class="block_20"></div>
        <KPage
          :total="total"
          :page-size="+queryFormData.pageSize"
          :current="+queryFormData.page"
          @on-change="handleCurrentChange"
          @on-page-size-change="handleSizeChange"
          style="text-align: center"
          :show-sizer="false"
        />
      </div>
      <div class="bottom-btn-wrapper">
        <Button @click="onCancel">取消</Button>
        <Dvd />
        <Dvd />
        <Dvd />
        <Button type="primary" @click="onConfirm">确定</Button>
      </div>
    </div>
  </el-popover>
</template>

<script>
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '', // 名称
  code: '',
  status: '',
};
export default {
  name: 'select-warehouse-popper',
  mixins: [],

  components: {},

  props: {
    title: {
      type: String,
      default: '选择仓库',
    },
    apiName: {
      type: String,
      default: 'getErpWarehouseList',
    },
    code: {
      type: String,
      default: '',
    },
    isDetail: {
      type: Boolean,
      default: () => false,
    },
    status: {
      type: String,
      default: 'ENABLE',
    },
    inputType: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      // popOptions: { boundariesElement: 'body', gpuAcceleration: false },
      popOptions: {},
      tableLoading: false,
      list: [],
      tableCols: [
        { slot: 'checkBox', align: 'center', fixed: 'left' },
        { title: '仓库名称', key: 'name', align: 'center' },
        { title: '仓库编码', key: 'code', align: 'center' },
        { title: '联系人', slot: 'contact', align: 'center' },
        { title: '联系电话', slot: 'mobile', align: 'center' },
      ],
      queryFormData: {
        ...init_query_form_data,
      },
      total: 0,
      selected_items: {},
      showPop: false,
    };
  },

  computed: {},

  watch: {
    showPop: {
      immediate: true,
      handler(val) {
        if (val) {
          this.queryFormData = { ...init_query_form_data, inbound_type: this.inputType };
          this.list &&
            this.list.some((item, index) => {
              if (item.code == this.code) {
                this.changeSelectOrder('', index);
                return true;
              }
            });
        } else {
          this.selected_items = {};
        }
      },
    },
  },

  created() {},

  mounted() {},

  methods: {
    changeSelectOrder(row, index) {
      let isChecked = this.list[index].checked;
      this.list.map(item => (item.checked = false));
      this.list[index].checked = !isChecked;
      if (isChecked) {
        this.selected_items = {};
      } else {
        this.selected_items = this.list[index];
      }
    },
    onCancel() {
      this.showPop = false;
    },
    onConfirm() {
      this.$emit('selectSup', this.selected_items);
      this.showPop = false;
    },
    onSearch() {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = 20;
      this.getList();
    },
    getList() {
      this.queryFormData.status = this.status;
      this.tableLoading = true;
      this.$api[this.apiName](this.queryFormData).then(res => {
        this.tableLoading = false;
        this.list = this.handler(res.list);
        this.total = res.total;
      });
    },
    handler(list) {
      if (!list) return [];
      list.forEach((item, index) => {
        item.checked = false;
        if (item.code == this.code || this.selected_items.code == item.code) {
          item.checked = true;
          this.selected_items = item;
        }
      });
      return list;
    },
    handleSizeChange(val) {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.queryFormData.page = val;
      this.getList();
    },
    onShow() {
      this.getList();
      this.showPop = true;
    },
  },

  destroyed() {},
};
</script>

<style scoped lang="less">
:deep(.el-popover__reference-wrapper) {
  width: 100%;
}

.popover-content-box {
  .header-box {
    .title {
      font-weight: 600;
      line-height: 50px;
      padding: 0 20px;
      font-size: 16px;
      border-bottom: 1px solid rgb(223, 225, 230);
    }

    .search-box {
      display: flex;
      align-items: center;
      padding: 10px 20px;

      .el-input {
        width: 200px;
      }

      .el-button {
        margin-left: 10px;
      }
    }
  }

  .bottom-btn-wrapper {
    height: 50px;
    display: flex;
    justify-content: flex-end;
    padding: 10px 20px;
    border-top: 1px solid rgb(223, 225, 230);
    margin-top: 20px;
  }
}
</style>
<style lang="less">
.pop-common {
  padding: 0 !important;
}
</style>
