<template>
  <Modal
    :value="confirmVisible"
    :title="title"
    width="430px"
    :closable="false"
    @on-visible-change="changeVisible"
    class-name="confirm-modal"
    transfer
  >
    <Form ref="form" :model="formData" :rules="ruleInline">
      <FormItem prop="type">
        <RadioGroup v-model="formData.type" @input="changeRadioType">
          <Radio v-for="(item, index) in radioList" :key="index" :label="item.label">{{ item.desc }}</Radio>
        </RadioGroup>
      </FormItem>
    </Form>
    <div slot="footer">
      <Button @click="cancel">取消</Button>
      <Button type="primary" @click="confirmPass">确定</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'confirmModal',
  mixins: [],

  components: {},

  props: {
    title: {
      type: String,
      default: '请确认导出的报表类型',
    },
    content: {
      type: String,
      default: '请确认导出的报表类型',
    },
    confirmVisible: {
      type: Boolean,
      default: false,
    },
    contentText: {
      type: String,
      default: '确定要通过审核吗？',
    },
    radioList: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      formData: {
        type: '',
      },
      ruleInline: {
        type: [{ required: true, message: '请确认导出的报表类型', trigger: 'blur' }],
      },
    };
  },

  computed: {},

  watch: {
    confirmVisible(val) {
      val &&
        this.$nextTick(() => {
          this.$refs['form'].resetFields();
        });
    },
  },

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    cancel() {
      this.changeVisible(false);
    },
    confirmPass() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          console.log('=>(index.vue:91) this.formData', this.formData);
          this.$emit('ok', this.formData);
        } else {
          this.$Message.error('Fail!');
        }
      });
    },
    changeVisible(val) {
      !val && this.$emit('update:confirmVisible', val);
    },
    changeRadioType(val) {
      let selected = this.radioList.find(item => item.label === val);
      if (selected) {
        this.formData.exportType = selected.exportType;
        // 处理非formData.type进行参数上传的情况
        if (selected.extraKey) {
          this.formData[selected.extraKey] = val;
        }
      }
    },
  },
};
</script>

<style scoped lang="less">
.confirm-modal {
  .content {
    padding: 0px;
  }
}
// ::v-deep .ivu-modal-header {
//   display: none;
// }
// ::v-deep .ivu-modal-footer {
//   border-top: none;
// }
// .title {
//   display: flex;
//   align-items: center;
//   margin-bottom: 10px;
//   padding-left: 10px;
//   padding-top: 10px;
//   .h-title {
//     font-size: 15px;
//     font-weight: 500;
//     margin-left: 16px;
//     line-height: 26px;
//   }
// }
// .content-text {
//   padding-left: 52px;
// }
</style>
