<template>
  <Modal
    :value="visible"
    :title="title"
    width="430px"
    @on-visible-change="changeVisible"
    class-name="tips-modal vertical-center-modal"
    transfer
  >
    <div class="content">
      <div class="content-text" v-html="contentText">
        <!--        <p>{{  }}</p>-->
      </div>
      <slot name="extra-content"></slot>
    </div>
    <div slot="footer">
      <Button v-if="showCancel" @click="cancel">{{ cancelText }}</Button>
      <Button type="primary" @click="confirmPass">{{ confirmText }}</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'TipsModal',
  mixins: [],

  components: {},
  model: {
    prop: 'visible',
    event: 'update:visible',
  },
  props: {
    title: {
      type: String,
      default: '温馨提示',
    },
    confirmText: {
      type: String,
      default: '确定',
    },
    cancelText: {
      type: String,
      default: '取消',
    },
    showCancel: {
      type: Boolean,
      default: false,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    contentText: {
      type: String,
      default: '确定要通过审核吗？',
    },
    isBackTipModal: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {};
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    cancel() {
      this.changeVisible(false);
      this.$emit('onCancel');
    },
    confirmPass() {
      this.$emit('onOk');
    },
    changeVisible(val) {
      if (!val) {
        this.$emit('update:visible', val);
        if (this.isBackTipModal) {
          this.$router.back();
        }
      }
    },
  },
};
</script>

<style scoped lang="less">
.tips-modal {
  .content {
    padding: 0px;
  }
}

::v-deep .ivu-modal-footer {
  border-top: none;
}

.title {
  display: flex;
  align-items: center;
  padding-left: 10px;

  .h-title {
    font-size: 15px;
    font-weight: 500;
    margin-left: 6px;
    line-height: 26px;
  }
}

.content-text {
  padding: 16px;
  line-height: 21px;
  font-size: 13px;
}
</style>
