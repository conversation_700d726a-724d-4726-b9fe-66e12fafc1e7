<template>
  <Modal
    :value="confirmVisible"
    :title="title"
    width="430px"
    :closable="false"
    @on-visible-change="changeVisible"
    class-name="confirm-modal"
    transfer
  >
    <div slot="header"></div>
    <div class="content">
      <div class="title">
        <Icon type="ios-help-circle" size="26" color="#ff9900"></Icon>
        <span class="h-title">{{ content }}</span>
      </div>
      <div class="content-text">
        <p>{{ contentText }}</p>
      </div>
    </div>
    <div slot="footer">
      <Button @click="cancel">取消</Button>
      <Button type="primary" @click="confirmPass">确定</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'confirmModal',
  mixins: [],

  components: {},

  props: {
    title: {
      type: String,
      default: '提示'
    },
    content: {
      type: String,
      default: '通过审核'
    },
    confirmVisible: {
      type: Boolean,
      default: false
    },
    contentText: {
      type: String,
      default: '确定要通过审核吗？'
    }
  },

  data() {
    return {};
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    cancel() {
      this.changeVisible(false);
    },
    confirmPass() {
      this.$emit('ok');
      this.cancel();
    },
    changeVisible(val) {
      !val && this.$emit('update:confirmVisible', val);
    }
  }
};
</script>

<style scoped lang="less">
.confirm-modal {
  .content {
    padding: 0px;
  }
}
::v-deep .ivu-modal-header {
  display: none;
}
::v-deep .ivu-modal-footer {
  border-top: none;
}
.title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding-left: 10px;
  padding-top: 10px;
  .h-title {
    font-size: 15px;
    font-weight: 500;
    margin-left: 16px;
    line-height: 26px;
  }
}
.content-text {
  padding-left: 52px;
}
</style>
