<template>
  <div class="wrapper">
    <div class="buttonBox">
      <el-upload
        action
        accept=".xlsx, .xls"
        :auto-upload="false"
        :show-file-list="false"
        :on-change="handle"
        class="flex flex-item-align"
      >
        <Button v-if="btnType === 'button'" type="primary" class="space6" :loading="excelUploadLoading">{{
          btnText
        }}</Button>
        <a v-if="btnType === 'link'">{{ btnText }}</a>
        <p>
          <a v-if="btnType === 'text' && !name">{{ btnText }}</a>
          <span v-if="name" style="color: #155bd4">{{ this.name }}</span>
        </p>
      </el-upload>
    </div>
  </div>
</template>

<script>
import xlsx from 'xlsx';
export default {
  name: 'excel',
  components: {},
  mixins: [],
  props: {
    excelUploadLoading: {
      type: Boolean,
      default: false
    },
    btnText: {
      type: String,
      default: '上传,'
    },
    btnType: {
      type: String,
      default: 'button'
    },
    isStock: {
      type: Bo<PERSON>an,
      default: false
    }
  },
  data() {
    return {
      name: ''
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 将流数据转化为json数据
    async handle(file) {
      //读取FILE中的数据（变为JSON格式）
      let excelJsonList = await this.readFile(file.raw);
      let workbook = xlsx.read(excelJsonList, { type: 'binary' }),
        worksheet = workbook.Sheets[workbook.SheetNames[0]];
      this.html = xlsx.utils.sheet_to_html(worksheet);
      excelJsonList = xlsx.utils.sheet_to_json(worksheet);
      // 获取导入excel数据
      this.excelUpload(excelJsonList, file.name);
    },
    // 读取数据
    readFile(file) {
      return new Promise(resolve => {
        let reader = new FileReader();
        reader.readAsBinaryString(file);
        reader.onload = ev => {
          resolve(ev.target.result);
        };
      });
    },
    // 将excel读取的日期数字改为时间格式
    formatDate(numb, format) {
      const old = numb - 1;
      const t = Math.round((old - Math.floor(old)) * 24 * 60 * 60);
      const time = new Date(1900, 0, old, 0, 0, t);
      const year = time.getFullYear();
      const month = time.getMonth() + 1;
      const date = time.getDate();
      return year + format + (month < 10 ? '0' + month : month) + format + (date < 10 ? '0' + date : date);
    },
    // 单独处理excel的日期
    handleDate(time) {
      if (time == '' || time == undefined) return '';
      if (time.toString().includes('-') || time.toString().includes('/')) {
        return time;
      } else {
        return this.formatDate(time, '/');
      }
    },
    // 将提取好的数据转发出去
    excelUpload(excelJsonList, fileName) {
      let hasHandleExcelList = this.handleExcelJsonList(excelJsonList);
      if (!hasHandleExcelList.length) {
        this.$Message.error('导入表格数据无效');
        return;
      }
      this.name = fileName;
      this.$emit('excelUpload', hasHandleExcelList);
    },
    handleExcelJsonList(excelJsonList) {
      // 如果待处理的数据为空则直接返回空数组
      if (!excelJsonList || !excelJsonList.length) {
        return [];
      }
      let resultArr = [];
      const company_id = '省公司ID';
      const company_name = '省公司名称';
      const clinic_id = '诊所ID';
      const clinic_name = '诊所名称';
      const spu_code = '商品ID';
      const name = '商品名称';
      excelJsonList.forEach(item => {
        resultArr.push({
          company_id: item[company_id],
          company_name: item[company_name],
          clinic_id: item[clinic_id],
          clinic_name: item[clinic_name],
          spu_code: item[spu_code],
          name: item[name]
        });
      });
      return resultArr || [];
    }
  },
  filters: {}
};
</script>
<style lang="less" scoped>
p {
  margin-bottom: 0px;
}
</style>
