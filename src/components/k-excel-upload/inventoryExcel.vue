<!-- 处理库存盘点上传数据 -->
<template>
  <div class="wrapper">
    <div class="buttonBox">
      <el-upload
        action
        accept=".xlsx, .xls"
        :auto-upload="false"
        :show-file-list="false"
        :on-change="handle"
        class="flex flex-item-align"
      >
        <Button v-if="btnType === 'button'" type="primary" class="space6" :loading="excelUploadLoading">{{
          btnText
        }}</Button>
        <a v-if="btnType === 'link'">{{ btnText }}</a>
        <p>
          <a v-if="btnType === 'text' && !name">{{ btnText }}</a>
          <span v-if="name" style="color: #155bd4">{{ this.name }}</span>
        </p>
      </el-upload>
    </div>
  </div>
</template>

<script>
import xlsx from 'xlsx';
export default {
  name: 'excel',
  components: {},
  mixins: [],
  props: {
    excelUploadLoading: {
      type: Boolean,
      default: false,
    },
    btnText: {
      type: String,
      default: '上传,',
    },
    btnType: {
      type: String,
      default: 'button',
    },
    code: {
      type: String,
      default: '',
    },
    isStock: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      name: '',
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 将流数据转化为json数据
    async handle(file) {
      //读取FILE中的数据（变为JSON格式）
      let excelJsonList = await this.readFile(file.raw);
      let workbook = xlsx.read(excelJsonList, { type: 'binary' }),
        worksheet = workbook.Sheets[workbook.SheetNames[0]];
      this.html = xlsx.utils.sheet_to_html(worksheet);
      excelJsonList = xlsx.utils.sheet_to_json(worksheet);
      // 获取导入excel数据
      this.excelUpload(excelJsonList, file.name);
    },
    // 读取数据
    readFile(file) {
      return new Promise(resolve => {
        let reader = new FileReader();
        reader.readAsBinaryString(file);
        reader.onload = ev => {
          resolve(ev.target.result);
        };
      });
    },
    // 将excel读取的日期数字改为时间格式
    formatDate(numb, format) {
      const old = numb - 1;
      const t = Math.round((old - Math.floor(old)) * 24 * 60 * 60);
      const time = new Date(1900, 0, old, 0, 0, t);
      const year = time.getFullYear();
      const month = time.getMonth() + 1;
      const date = time.getDate();
      return year + format + (month < 10 ? '0' + month : month) + format + (date < 10 ? '0' + date : date);
    },
    // 单独处理excel的日期
    handleDate(time) {
      if (time == '' || time == undefined) return '';
      if (time.toString().includes('-') || time.toString().includes('/')) {
        return time;
      } else {
        return this.formatDate(time, '/');
      }
    },
    // 将提取好的数据转发出去
    async excelUpload(excelJsonList, fileName) {
      let hasHandleExcelList = await this.handleExcelJsonList(excelJsonList);
      console.log(hasHandleExcelList);
      if (!hasHandleExcelList.length) {
        this.$Message.error('导入表格数据无效');
        return;
      }
      this.name = fileName;
      this.$emit('excelUpload', hasHandleExcelList);
    },
    async handleExcelJsonList(excelJsonList) {
      // 如果待处理的数据为空则直接返回空数组
      if (!excelJsonList || !excelJsonList.length) {
        return [];
      }
      console.log(excelJsonList);
      let resultArr = [];
      let checkArr = [];

      //上传前数据检测
      const objMap = {
        num: '序号',
        id: '产品编号',
        product_name: '产品名称',
        spec: '规格',
        unit: '单位',
        // name:'产地',
        supplier_name: '供应商',
        batch_code: '批号',
        est_stock_num: '账面库存',
        real_stock_num: '实际库存',
        // diff_num:'盈亏数量',
        // name:'平均成本单价',
        // name:'盈亏金额'
      };

      const checkMap = {
        product_code: '产品编码',
        product_name: '产品名称',
        spec: '规格',
        prov_name: '产地',
        supplier_name: '供应商',
        batch_code: '批号',
        est_stock_num: '账面库存',
        real_stock_num: '实际库存',
      };

      excelJsonList.forEach(item => {
        let obj = {};
        for (let key in checkMap) {
          obj[key] = item[checkMap[key]] || '';
        }

        // 上传文件填了产品编码则push
        if (obj.real_stock_num) {
          checkArr.push(obj);
        }
      });
      if (!checkArr?.length) {
        return [];
      }

      let result = await this.$api.stockImportDetec({ batch_params: checkArr, warehouse_code: this.code });
      resultArr = result.succ_data;

      console.log(checkArr);
      return resultArr || [];
    },
  },
  filters: {},
};
</script>
<style lang="less" scoped>
p {
  margin-bottom: 0px;
}
</style>
