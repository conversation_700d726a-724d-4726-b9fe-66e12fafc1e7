<!--<template>-->
<!--  <div>-->
<!--    <div class="editor-container">-->
<!--      <div class="toolbar-box"></div>-->
<!--      <div class="title-box">-->
<!--        <input placeholder="请在这里添加标题....." :border="false" />-->
<!--      </div>-->
<!--      <Editor id="tinymce" v-model="tinymceHtml" :init="init" ></Editor>-->
<!--    </div>-->
<!--    <k-goods-select-multiple v-model="goodsSelectVisible" @on-selected="selectGoods" :show-style="false"></k-goods-select-multiple>-->
<!--    <Preview :preview-visible.sync="previewVisible" :content="tinymceHtml"></Preview>-->
<!--  </div>-->
<!--</template>-->

<!--<script>-->
<!--// import tinymce from 'tinymce/tinymce'-->
<!--// import Editor from '@tinymce/tinymce-vue'-->
<!--// // 引入富文本编辑器主题的js和css-->
<!--// import '/public/tinymce/themes/silver/theme.min.js'-->
<!--// import '/public/tinymce/skins/ui/oxide/skin.min.css'-->
<!--// import 'tinymce/icons/default' //引入编辑器图标icon-->
<!--// // 扩展插件-->
<!--// import 'tinymce/plugins/lists' //列表-->
<!--// import 'tinymce/plugins/advlist' //引入编辑器图标icon-->
<!--// import 'tinymce/plugins/image'//图片-->
<!--// import 'tinymce/plugins/imagetools'//图片工具-->
<!--// import 'tinymce/plugins/paste'// 自动链接-->
<!--// // import 'tinymce/plugins/autolink'// 自动链接-->
<!--// // import 'tinymce/plugins/autosave' //自动保存-->
<!--// import 'tinymce/plugins/help' //自动保存-->
<!--// import 'tinymce/plugins/nonbreaking' //插入不间断空格。-->
<!--// import 'tinymce/plugins/noneditable' //防止某些元素被编辑。-->
<!--// import 'tinymce/plugins/quickbars'  //编辑器右侧快速工具条-->
<!--// import 'tinymce/plugins/link'-->
<!--// import 'tinymce/plugins/code'-->
<!--// // import 'tinymce/plugins/preview'-->
<!--// import 'tinymce/plugins/wordcount'-->
<!--// import 'tinymce/plugins/autoresize' //自动调整编辑器高度-->
<!--// import 'tinymce/plugins/hr'-->
<!--// import 'tinymce/plugins/fullscreen'-->
<!--// import '/public/tinymce/plugins/lineheight/plugin.min.js'-->
<!--// // import 'tinymce/plugins/searchreplace'-->
<!--// import 'tinymce/plugins/visualblocks'-->
<!--// // import 'tinymce/plugins/directionality'-->
<!--// // import 'tinymce/plugins/visualblocks'-->
<!--// // import 'tinymce/plugins/visualchars'-->
<!--// // import 'tinymce/plugins/template'-->
<!--// // import 'tinymce/plugins/charmap'-->
<!--// // import 'tinymce/plugins/nonbreaking'-->
<!--// // import 'tinymce/plugins/insertdatetime'-->
<!--// import KGoodsSelectMultiple from '_c/k-goods-select-multiple'-->
<!--// import request from 'utils/request'-->
<!--// import axios from 'axios'-->
<!--// import config from '@/config'-->
<!--// import-->
<!--import Preview from './Preview'-->

<!--const domain = process.env.VUE_APP_CMD === 'build' ? config.CdnDomain : ''-->
<!--export default {-->
<!--  name: 'index',-->
<!--  mixins: [],-->

<!--  components: { Editor, KGoodsSelectMultiple, Preview },-->

<!--  props: {-->
<!--    value: {-->
<!--      type: String,-->
<!--      default: ''-->
<!--    },-->
<!--    maxSize: {-->
<!--      type: Number,-->
<!--      default: 3-->
<!--    },-->
<!--    disabled: {-->
<!--      type: Boolean,-->
<!--      default: false-->
<!--    },-->
<!--    previewFlag: {-->
<!--      type: Boolean,-->
<!--      default: false-->
<!--    }-->
<!--  },-->

<!--  data() {-->
<!--    return {-->
<!--      tinymceHtml: '',-->
<!--      tinymceId: 'tinymce',-->
<!--      init: {},-->
<!--      goodsSelectVisible: false,-->
<!--      editor: null,-->
<!--      previewVisible: false,-->
<!--    }-->
<!--  },-->

<!--  computed: {},-->

<!--  watch: {-->
<!--    value: {-->
<!--      handler: function ( val ) {-->
<!--        this.tinymceHtml = val-->

<!--      },-->
<!--      immediate: true-->
<!--    },-->
<!--    tinymceHtml: {-->
<!--      handler: function ( val ) {-->
<!--        this.$emit( 'input', val )-->

<!--      },-->
<!--      immediate: true-->
<!--    }-->
<!--  },-->

<!--  created() {-->
<!--    this.initEditor()-->
<!--  },-->

<!--  mounted() {-->

<!--  },-->

<!--  methods: {-->
<!--    initEditor() {-->
<!--      this.init = {-->
<!--        selector: '#tinymce',-->
<!--        inline: true,-->
<!--        /*toolbar 设置*/-->
<!--        toolbar_persist: true,-->
<!--        fixed_toolbar_container: '.toolbar-box',-->
<!--        font_formats: '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;',-->
<!--        format_empty_lines: true,-->
<!--        placeholder: '请在这里输入正文内容.....',-->
<!--        language_url: domain + '/tinymce/langs/zh_CN.js', //汉化路径是自定义的，一般放在public或static里面-->
<!--        language: 'zh_CN', //汉化\-->
<!--        plugins: ['lists', 'quickbars', 'visualblocks', 'image', 'lineheight', 'fullscreen', 'imagetools', 'nonbreaking', 'noneditable', 'code', 'link'],//插件-->
<!--        toolbar: 'fontselect | fontsizeselect | formatselect imagetools | alignleft aligncenter alignright alignjustify outdent indent lineheight | bold italic underline strikethrough | forecolor backcolor removeformat | image myCustomToolbarButton | fullscreen | bullist numlist  | link code | ', //工具栏-->
<!--        // toolbar_mode: false,-->
<!--        quickbars_selection_toolbar: 'bold italic | link fontsizeselect forecolor backcolor',-->
<!--        //工具栏-->
<!--        quickbars_insert_toolbar: false,-->
<!--        menubar: false, //隐藏菜单栏-->
<!--        min_height: 400, //编辑器最小高度-->
<!--        height: 400,-->
<!--        // toolbar_location: '/',-->
<!--        fontsize_formats: '12px 14px 16px 18px 20px 22px 24px 28px 32px 36px 48px 56px 72px', //字体大小-->
<!--        block_formats: 'Paragraph=p;Header 1=h1;Header 2=h2;Header 3=h3;Header 4=h4;Header 5=h5;Header 6=h6',-->
<!--        branding: false, //隐藏右下角技术支持-->
<!--        // auto_focus: true, //自动聚焦-->
<!--        toolbar_mode: 'wrap', //工具栏折叠-->
<!--        image_advtab: true, //图片上传-->
<!--        images_upload_url: false,-->
<!--        autoresize_bottom_margin: 50,-->
<!--        autoresize_on_init: false,-->
<!--        object_resizing: 'img',-->
<!--        // br_in_pre: false,-->
<!--        // forced_root_block: '',-->
<!--        content_css: domain + '/tinymce/style/content.css', //编辑器内容样式-->
<!--        image_dimensions: true,-->
<!--        images_file_types: 'jpeg,jpg,png,gif,bmp,webp',//指定可拖拽的类型-->
<!--        content_style: 'img{max-width:100%;}'+'p{line-height: 1.3;}'+'div{line-height: 1.3;}'+'section{line-height: 1.3;}'+'span{line-height: 1.3;}'+'b{line-height: 1.3;}', //编辑器内部样式-->
<!--        images_upload_handler: ( blobInfo, success, failure ) => {-->
<!--          this.handleImgUpload( blobInfo, success, failure )-->
<!--        },-->
<!--        setup: ( editor ) => {-->
<!--          console.log( '-> %c editor  === %o', 'font-size: 15px;color: green;', editor )-->
<!--          editor.ui.registry.addButton( 'myCustomToolbarButton', {-->
<!--            text: '添加商品',-->
<!--            tooltip: '添加商品',-->
<!--            icon: 'plus',-->
<!--            onAction: () => {-->
<!--              // const content = `<section href="data-id=200" style="text-decoration: none;color:#000;display: block;">  <a>十二通</a> <img width="100%"  src="https://pic2.zhimg.com/v2-93ffc1821cb744d0f4839a6d58fee700_r.jpg?source=172ae18b" alt=""><a>2333</a></section>`-->
<!--              // editor.insertContent( content )-->
<!--              this.addProd( editor )-->
<!--            }-->
<!--          } )-->
<!--          editor.on( 'init', function ( e ) {-->
<!--            editor.focus()-->
<!--          } )-->
<!--          // editor.on('paste',function(e) {-->
<!--          //  const text = e.clipboardData.getData('text/plain')-->
<!--          //  const html = e.clipboardData.getData('text/html')-->
<!--          //   console.log("-> %c html  === %o", "font-size: 15px;color: green;", html)-->
<!--          //   console.log("-> %c text  === %o", "font-size: 15px;color: green;", text)-->
<!--          //       if(text&&html.indexOf('<meta')===-1) {-->
<!--          //         console.log(e.clipboardData.getData('text/explain'))-->
<!--          //         e.preventDefault();-->
<!--          //         setTimeout(() => {-->
<!--          //           editor.insertContent(text)-->
<!--          //         }, 0);-->
<!--          //       }-->
<!--          // })-->
<!--          if(this.disabled){-->
<!--            editor.setMode('readonly')-->
<!--          }-->
<!--        },-->
<!--        init_instance_callback: editor => {-->
<!--          editor.focus() // 初始化聚焦，让内联模式的编辑器工具显示-->
<!--        },-->
<!--        statusbar: false, //隐藏状态栏-->
<!--        extended_valid_elements: 'a[href|class|style],img[width|height|alt|src,style|class],section[data-goods-id,style|class],p[style|class]',-->
<!--        formats: {},-->
<!--        valid_style: {-->
<!--          '*': 'font-size,font-family,color,text-decoration,text-align,width,height,display,max-width'-->
<!--        },-->
<!--        valid_children: '+a[div|a|p|img|section],+section[div|a|p|img|section],+div[div|a|p|img|section],+p[div|a|p|img|section],+img[div|a|p|img|section]',-->
<!--        remove_trailing_brs: true,-->
<!--        // format_empty_lines: true,-->
<!--        paste_data_images: true,-->
<!--        force_br_newlines: true,-->
<!--        // paste_merge_formats: false,-->
<!--        // paste_preprocess: function(plugin, args) {-->
<!--        //   console.log("-> %c args  === %o", "font-size: 15px;color: green;", args)-->
<!--        //   console.log(args.content);-->
<!--        //   args.content += ' preprocess';-->
<!--        // },-->
<!--        // paste_postprocess: function(plugin, args) {-->
<!--        //   console.log(args.node);-->
<!--        //   args.node.setAttribute('id', '42');-->
<!--        // }-->
<!--      }-->
<!--    },-->
<!--    previewArticle() {-->
<!--      console.log( 'preview' )-->
<!--      this.previewVisible = true-->
<!--    },-->
<!--    addProd( editor ) {-->
<!--      console.log( '-> %c editor  === %o', 'font-size: 15px;color: green;', editor )-->
<!--      this.editor = editor-->
<!--      this.goodsSelectVisible = true-->
<!--    },-->
<!--    selectGoods( goods, style ) {-->
<!--      console.log( '-> %c goods  === %o', 'font-size: 15px;color: green;', goods )-->
<!--      let template = ``-->
<!--      for ( let i = 0; i < goods.length; i++ ) {-->
<!--        const good = goods[i]-->
<!--        if ( style === 1 ) {-->
<!--          template += `<div class="mceNonEditable" data-goods-id="${ good.id }" style="width: 100%;box-sizing:border-box;display:flex;color:#000;background:#FAFAFA;border-radius:8px;padding: 12px;font-size: 1rem;line-height: 20px;">-->
<!--          <div  class="link-a" data-goods-id="${ good.id }" style="display:block!important;align-items: center;"><img class="goods-img"  data-goods-id="${ good.id }" style="vertical-align: bottom;margin:0!important;border-radius:6px;height: 90px;width: 90px;object-fit: cover;" src="${ good.main_img }" alt="${ good.id }"></div>-->
<!--          <div class="link-a" style="width:100%;text-decoration:none;margin-left: 16px;flex:1;height: 100%;display: block!important;" data-goods-id="${ good.id }">-->
<!--           <span data-goods-id="${ good.id }" style="font-weight: 600;margin-top: 12px;text-align:left!important;text-decoration: none;word-break: break-all;text-overflow: ellipsis;overflow: hidden;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">${ good.name }</span>-->
<!--           <span data-goods-id="${ good.id }" style="letter-spacing:0;margin-top: 16px;text-align:left!important;text-decoration: none;color:#E5634B;display: block">￥${ good.price }</span>-->
<!--          </div>-->
<!--          </div><p style="line-height: 1.2;min-height: 14px;"></p>`-->
<!--        } else {-->
<!--          template += `<div class="mceNonEditable" data-goods-id="${ good.id }" style="display:inline-block;text-decoration: none;height:100%;box-sizing:border-box;width: 47%;color:#000;background:#FAFAFA;border-radius:2px;padding: 12px 6px 6px;font-size: 1rem;margin-right: 2%; line-height: 20px;margin-bottom: 16px;border-radius: 2px;vertical-align: bottom;">-->
<!--          <p class="link-a"  data-goods-id="${ good.id }" ><img class="goods-img" style="display:block;height: 152px;width: 100%;object-fit: cover;border-radius:2px;" data-goods-id="${ good.id }"  src="${ good.main_img }" alt="${ good.id }"></p>-->
<!--          <p class="link-a" style="margin-top: 8px;padding: 0 13px 9px 9px;display: block; " data-goods-id="${ good.id }">-->
<!--           <span style="font-weight: 600;text-align:left!important;width: 100%;display:inline-block;text-decoration: none;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;" data-goods-id="${ good.id }" >${ good.name }</span><span>&ZeroWidthSpace;&ZeroWidthSpace;</span>-->
<!--           <span style="text-decoration: none;margin-top: 10px;text-align:left!important;letter-spacing:0;" data-goods-id="${ good.id }" >￥${ good.price }</span>-->
<!--          </p>-->
<!--          </div>`-->
<!--        }-->
<!--      }-->
<!--      // console.log("-> %c div  === %o", "font-size: 15px;color: green;", div)-->
<!--      // // template = `${template}`-->
<!--      // if(style===2){-->
<!--      //   template = `<div style="display:flex;flex-wrap:wrap;">${template}</div>`-->
<!--      // }-->
<!--      if ( style === 1 ) {-->
<!--        template = `<p></p><div  style="box-sizing: border-box;flex-direction: column;max-width: none!important;">${ template }</div><p></p>`-->
<!--      } else {-->
<!--        template = `<p></p>${ template }<p></p>`-->
<!--      }-->
<!--      this.editor.insertContent( template )-->
<!--      this.goodsSelectVisible = false-->
<!--      // this.editor.focus()-->
<!--    },-->
<!--    async handleImgUpload( blobInfo, success, failure ) {-->
<!--      console.log("-> %c blobInfo, success, failure  === %o", "font-size: 15px;color: green;", blobInfo, success, failure)-->
<!--      const file = blobInfo.blob()-->
<!--      console.log( '-> %c file  === %o', 'font-size: 15px;color: green;', file )-->
<!--      // const imgBase64 = `data:${ blobInfo.blob().type };base64,${ blobInfo.base64() }`-->
<!--      // success( imgBase64 )-->
<!--      if ( file.size / 1024 / 1024 >= this.maxSize ) {-->
<!--        failure( '上传文件大小不能超过3M' )-->
<!--        return-->
<!--      }-->
<!--      let ext = file.name.lastIndexOf( '.' ) > 0-->
<!--          ? file.name.substring( file.name.lastIndexOf( '.' ) + 1, file.name.length )-->
<!--          : ''-->
<!--      console.log( '-> %c ext  === %o', 'font-size: 15px;color: green;', ext )-->
<!--      await request.get( '/pms_plat/qiniu.getuptoken', { data: { ext } } ).then( data => {-->
<!--        console.log( '-> %c data  === %o', 'font-size: 15px;color: green;', data )-->
<!--        // this.token = data.token-->
<!--        // this.domain = data.domain-->
<!--        const formData = new FormData()-->
<!--        formData.append( 'file', file )-->
<!--        formData.append( 'token', data.token )-->
<!--        axios.post( 'https://upload.qiniup.com', formData ).then( res => {-->
<!--          console.log( '-> %c res  === %o', 'font-size: 15px;color: green;', res )-->
<!--          if ( res.status === 200 ) {-->
<!--            const imgSrc = data.domain + '/' + res.data.key-->
<!--            success( imgSrc )-->
<!--          } else {-->
<!--            failure( '上传失败' )-->
<!--          }-->
<!--        } ).catch( err => {-->
<!--          failure( '上传失败' )-->
<!--        } )-->
<!--      }, reject => {-->
<!--        console.log( 2131232 )-->
<!--        this.$Message.error( {-->
<!--          content: reject.errmsg || String( reject ),-->
<!--          duration: 3,-->
<!--        } )-->
<!--        throw new Error( reject.errmsg || reject )-->
<!--      } )-->
<!--      // success(`${imgBase64}`)-->
<!--      // uploadImgage(data).then(res => {-->
<!--      //   // 传入success回调里的数据就是富文本编辑器里插入图片的src的值-->
<!--      //   success(`${this.baseUrl}/${res.data[0]}`)-->
<!--      // }).catch(() => { failure('error') })-->
<!--    },-->
<!--    destroyTiny() {-->
<!--      // tinymce&&tinymce.editors[this.tinymceId].destroy()-->
<!--      //tinyMCE.editors[tinyID].remove();-->
<!--    },-->

<!--  },-->

<!--  beforeDestroy() {-->
<!--    // 销毁实例-->
<!--    if ( this.editor ) {-->
<!--      this.editor.destroy();-->
<!--      this.editor = null;-->
<!--    }-->
<!--  }-->

<!--}-->
<!--</script>-->

<!--<style scoped lang="less">-->
<!--.editor-container{-->
<!--  .toolbar-box{-->
<!--    box-shadow: 0px 2px 4px 0px rgba(0,0,0,0.14);-->
<!--    ::v-deep .tox-toolbar{-->
<!--     background: #F7F7F7;-->
<!--   }-->
<!--  }-->
<!--  .title-box {-->
<!--    input {-->
<!--      font-size: 24px;-->
<!--      padding: 8px 8px;-->
<!--      width: 100%;-->
<!--      box-sizing: border-box;-->
<!--      border: none;-->
<!--      box-shadow: none;-->
<!--      background-color: #fefefe;-->
<!--      color: #777;-->
<!--      border-bottom: 1px dotted #eee;-->
<!--    }-->
<!--  }-->
<!--}-->

<!--::v-deep .tox-tinymce {-->
<!--  //z-index: 0 !important;-->
<!--  z-index: 5 !important;-->
<!--  width: 100%;-->
<!--}-->

<!--::v-deep .tox-fullscreen {-->
<!--  z-index: 1500000 !important;-->
<!--}-->
<!--::v-deep button[title="字号"]{-->
<!--  width: 70px;-->
<!--}-->
<!--::v-deep button[title="基块"]{-->
<!--  width: 70px;-->
<!--}-->
<!--::v-deep button[title="字体"]{-->
<!--  width: 120px;-->
<!--}-->
<!--::v-deep .mce-content-body{-->
<!--  min-height: 350px;-->
<!--  .tox-editor-header{-->
<!--    border: none;-->
<!--  }-->
<!--}-->

<!--</style>-->
