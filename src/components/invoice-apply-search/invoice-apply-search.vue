<template>
  <Select
    ref="apply-search"
    transfer
    :value="value"
    :clearable="isClearable"
    :loading="searchLoading"
    :remote-method="search"
    filterable
    @on-clear="clearSub"
    @on-query-change="queryChange"
    class="filterable-select"
    :placeholder="placeholder"
    @on-select="selectSup"
  >
    <!--      <Option value="" v-show="showAll">全部</Option>-->
    <Option v-for="(option, index) in apply_list" :key="index" :value="option.id" :label="option.desc">
      <span>{{ option.desc }}</span>
      <span class="disabled-tag" v-if="option.is_disabled === '1'">已禁用</span>
    </Option>
  </Select>
</template>

<script>
import S from '@/utils/util';
export default {
  name: 'apply-search',
  components: {},
  mixins: [],
  props: {
    isClearable: {
      type: Boolean,
      default: true,
    },
    value: {
      type: String,
      default: '',
    },
    showAll: {
      type: <PERSON>olean,
      default: true,
    },
    type: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: '输入或选择申请方',
    },
  },
  data() {
    return {
      searchLoading: false,
      apply_list: [],
    };
  },
  computed: {},
  watch: {},
  created() {
    if (!this.$route.query.applicant_id) {
      this.searchMethod();
    } else {
      let list = JSON.parse(localStorage.getItem('apply_list')) || [];
      this.apply_list = list;
      this.$emit('input', list[0] && list[0].id);
    }
  },
  mounted() {},
  methods: {
    searchMethod: S.debounce(function (query) {
      this.searchLoading = true;
      let params = {
        name: query || '',
      };
      this.$api.getApplicantList(params).then(res => {
        this.searchLoading = false;
        this.apply_list = S.descToArrHandle(res.applicant, 'name');
        localStorage.setItem('apply_list', JSON.stringify(this.apply_list));
      });
    }, 200),
    search() {},
    selectSup(val) {
      this.$emit('input', val.value);
    },
    queryChange(val) {
      this.searchMethod(val);
    },
    clear() {
      this.$refs['apply-search'].clearSingleSelect();
    },
    clearSub() {
      this.searchMethod();
      this.$emit('input', '');
    },
  },
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
.disabled-tag {
  color: #ccc;
  margin-left: 10px;
}
</style>
