<select-search>
	<Select
			transfer
			:value="value"
			:clearable="isClearable"
			:loading="searchLoading"
			:remote-method="searchMethod"
			filterable
			class="filterable-select"
			:placeholder="placeholder"
			@on-select="selectSup">
		<Option v-for="(option, index) in list" :key="option.id" :value="option.id">{{ option.name }}</Option>
	</Select>
</select-search>

<script>
export default {
  name: 'select-search',
  components: {},
  mixins: [],
  props: {
    isClearable: {
      type: Boolean,
      default: true
    },
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    apiName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      searchLoading: false,
      list: []
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    searchMethod(query) {
      if (query) {
        this.$api[this.apiName]({ name: query }).then(res => {
          console.log(res);
          this.list = res.list;
        });
      }
    },
    selectSup(val) {
      console.log(val);
      this.$emit('input', val.value);
    }
  }
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
</style>
