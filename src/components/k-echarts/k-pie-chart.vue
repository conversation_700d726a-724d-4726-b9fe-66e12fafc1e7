<template>
  <div :id="id" :ref="id" style="height: 300px; width: 100%"></div>
</template>
<script>
export default {
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  props: {
    id: {
      type: String,
      default: () => 'pie'
    },
    options: {
      type: Object,
      default: () => {
        return {
          seriesData: [],
          legendData: [],
          name: '',
          tooltip: {},
          color: []
        };
      }
    }
  },
  data() {
    return {};
  },
  watch: {
    options: {
      deep: true,
      handler(val) {
        if (val && val.seriesData && val.name) {
          this.init(val);
        }
      },
      immediate: true
    }
  },
  created() {},
  methods: {
    init(options) {
      this.$nextTick(() => {
        this.drawLine(options);
        const myChart = this.$echarts.init(document.getElementById(this.id));
        window.addEventListener('resize', function () {
          myChart.resize();
        });
      });
    },
    // 将辨证参考的数据格式转换为饼图所需要的格式
    drawLine(data) {
      console.log('-> data', data);
      const myChart = this.$echarts.init(document.getElementById(this.id));
      // var data = this.genData(3);
      let option = {
        title: {
          text: data.name || '',
          subtext: '',
          left: 'center',
          top: 'bottom',
          padding: [30, 0],
          textStyle: {
            fontSize: 12,
            color: '#000'
          }
        },
        tooltip: {
          trigger: 'item',
          // formatter: "{b}:{d}%",
          formatter: a => {
            return `${a.data.name}：${a.data.rate ? a.data.rate : a.data.value}`;
          },
          confine: true
          // extraCssText: 'z-index:9999'
        },
        // legend: {
        // 	type: "scroll",
        // 	width: "100%",
        // 	orient: "vertical",
        // 	// right: "20%", // !设置图例的位置
        // 	left: "50%",
        // 	bottom: 40,
        // 	data: data.legendData,
        // },
        series: [
          {
            name: '',
            type: 'pie',
            center: ['50%', '50%'], // ?!饼图的位置
            stillShowZeroSum: true,
            showEmptyCircle: true,
            itemStyle: {
              borderRadius: 4
            },
            data: data.seriesData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            },
            radius: [15, '60%'],
            label: {
              show: false,
              padding: [0, -5],
              overflow: 'none',
              // width:0,
              // height:0,
              // lineHeight: 0,
              // borderRadius:2.5,
              // padding: [2.5,-2.5,2.5,-2.5],
              // backgruondColor: 'auto',
              // formatter: '{d}%\n{b}',
              formatter: a => {}
              // rich: {
              // 	a: {
              // 		padding:[0,-50,-20,-50]
              // 	},
              // 	b: {
              // 		height: 10,
              // 		width: 10,
              // 		lineHeight: 10,
              // 		marginBottom:10,
              // 		padding: [0,-20,0,20],
              // 		borderRadius: 10,
              // 	},
              // 	c: {
              // 		padding:[-10,-50,0,-50]
              // 	},
              // },
            },
            labelLine: {
              length: 0,
              length2: 0
            },
            color: data.color
          }
        ]
      };
      myChart.setOption(option);
    }
  }
};
</script>

<style lang="less" scoped></style>
