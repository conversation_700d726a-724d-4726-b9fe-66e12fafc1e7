<template>
  <div :id="id" :ref="id" style="height: 300px; width: 100%"></div>
</template>
<script>
export default {
  name: 'KFunnel<PERSON><PERSON>',
  props: {
    id: {
      type: String,
      default: () => 'funnel'
    },
    options: {
      type: Object,
      default: () => {
        return {
          seriesData: {
            cv: 0, //访客量
            order_cv: 0, //下单人数
            paid_cv: 0 //支付人数
          }
        };
      }
    }
  },
  data() {
    return {};
  },
  watch: {
    options: {
      deep: true,
      handler(val) {
        this.init();
      }
    }
  },
  created() {},
  mounted() {
    // this.init()
  },
  methods: {
    init() {
      this.$nextTick(() => {
        this.drawLine();
        // var myChart = this.$echarts.init(document.getElementById(this.id));
        // window.addEventListener("resize", function() {
        // 	myChart.resize();
        // });
      });
    },
    drawLine() {
      var myChart = this.$echarts.init(document.getElementById(this.id));
      window.addEventListener('resize', function () {
        myChart.resize();
      });
      var data = this.options;
      myChart.setOption({
        title: {
          text: '',
          left: 'center',
          top: 'bottom'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c}'
        },
        series: [
          {
            name: '',
            type: 'funnel',
            top: '10%',
            bottom: 60,
            left: 'center',
            width: '50%',
            min: 0,
            max: 100,
            minSize: '5%',
            maxSize: '100%',
            sort: 'descending',
            gap: 0,
            label: {
              show: true,
              position: 'inside'
            },
            labelLine: {
              length: 10,
              lineStyle: {
                width: 1,
                type: 'solid'
              }
            },
            itemStyle: {
              borderColor: '#fff',
              borderWidth: 1
            },
            emphasis: {
              label: {
                fontSize: 20
              }
            },
            markLine: {
              lineStyle: {
                width: 100
              }
            },
            data: [
              {
                value: data.seriesData.order_num,
                name: '全部订单:',
                itemStyle: {
                  color: '#E88E6D',
                  fontSize: 20
                },
                label: {
                  position: 'right',
                  color: 'black',
                  padding: [30, 0, 30, 4],
                  formatter: function (data) {
                    console.log('data', data);
                    return `${data.name}${data.value}`;
                  }
                }
              },
              {
                value: data.seriesData.paid_order_num,
                name: '支付订单:',
                itemStyle: {
                  color: '#E88E6D',
                  opacity: 0.7
                },
                label: {
                  position: 'right',
                  color: 'black',
                  padding: [30, 0, 30, 4],
                  formatter: function (data) {
                    return `${data.name}${data.value}`;
                  }
                }
              },
              {
                value: data.seriesData.refund_order_num,
                name: '退款订单:',
                itemStyle: {
                  color: '#E88E6D',
                  opacity: 0.4
                },
                label: {
                  position: 'right',
                  color: 'black',
                  padding: [30, 0, 30, 4],
                  formatter: function (data) {
                    return `${data.name}${data.value}`;
                  }
                }
              }
            ]
          }
        ]
      });
    }
  }
};
</script>

<style lang="less" scoped></style>
