<template>
  <div :id="id" :ref="id" style="height: 300px; width: 100%"></div>
</template>
<script>
import S from 'utils/util';
export default {
  name: 'KNormalBar',
  props: {
    id: {
      type: String,
      default: () => 'normalBar'
    },
    options: {
      type: Object,
      default: () => {
        return {
          seriesData: {
            order_num: 0, // 总订单
            his_pres_num: 0, // his订单
            shop_order_num: 0 // 商城订单
          }
        };
      }
    },
    isCustomer: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
  watch: {
    options: {
      deep: true,
      handler(val) {
        if (val) {
          this.init(val);
        }
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    init(options) {
      this.$nextTick(() => {
        this.drawLine(options);
      });
    },
    drawLine(options) {
      var myChart = this.$echarts.init(document.getElementById(this.id));
      window.addEventListener('resize', function () {
        myChart.resize();
      });
      // let xData =options.xData.map(item=> {
      //   console.log("-> item", item)
      //  return  (item/10000).toFixed(2)
      // })
      // console.log("-> xData", xData)
      myChart.setOption({
        grid: {
          top: '10%',
          left: '160px',
          right: '10%',
          bottom: '6%'
        },
        tooltip: {
          show: true,
          formatter: a => {
            console.log('-> a', a);
            if (this.isCustomer) {
              return `${a.name}：${a.value}人`;
            } else {
              let val = S.number_format(a.value, 2);
              return `${a.name}：${val}元`;
            }
          }
        },
        yAxis: {
          type: 'category',
          position: 'left',
          data: options.yData,
          axisTick: {
            show: false // 去除x轴的刻度线
          },
          axisLabel: {
            interval: 0,
            inside: false,
            color: '#666', // 自定义x轴label的颜色
            fontSize: 12,
            verticalAlign: 'middle',
            // padding: [0,0,0,-200],
            width: 156,
            margin: '160',
            overflow: 'truncate',
            ellipsis: '...',
            align: 'left'
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(204, 204, 204, .5)'
            }
          }
        },
        xAxis: {
          type: 'value',
          name: options.xLabel,
          position: 'bottom',
          nameLocation: 'start',
          data: options.yData,
          nameTextStyle: {
            color: '#999999', // 设置y轴单位的颜色
            fontSize: 12
          },
          axisTick: {
            show: false // 去除y轴的刻度线
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(204, 204, 204, .5)'
            }
          },
          splitLine: {
            show: true, // 显示y轴的网格线
            lineStyle: {
              color: '#CCCCCC',
              opacity: 0.3
            }
          }
          // axisLabel: {
          //   formatter: ''
          // }
        },
        series: [
          {
            data: options.xData,
            type: 'bar',
            // color: '#9CA4FF', // 更改柱体0的颜色
            barWidth: '14px', // 设置柱子的宽度
            label: {
              show: true,
              position: 'right',
              formatter: a => {
                console.log('-> a', a.value);
                if (this.isCustomer) {
                  return `${a.value}人`;
                } else {
                  let val = S.number_format(a.value, 2);
                  return `¥ ${val}`;
                }
              },
              fontSize: 12,
              fontWeight: 'bolder'
            },
            itemStyle: {
              color: '#9CA4FF',
              label: {
                show: true,
                formatter: '{c}',
                position: 'right',
                textStyle: {
                  color: '#1157E5',
                  fontSize: 14
                }
              }
            }
          }
        ]
      });
    }
  }
};
</script>

<style lang="less" scoped></style>
