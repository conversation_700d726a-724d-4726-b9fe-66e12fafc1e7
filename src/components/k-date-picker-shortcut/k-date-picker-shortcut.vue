<template>
  <div>
    <DatePicker
      v-model="timeRange"
      :options="options"
      :transfer="true"
      transfer-class-name="date-range-disabled"
      clearable
      format="yyyy-MM-dd"
      :placeholder="placeholder"
      type="daterange"
      v-on="$listeners"
    ></DatePicker>

  </div>
</template>

<script>
export default {
  name: "PickerShortcut",
  components: {},
  mixins: [],
  props: {
    value: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '时间范围选择'
    }
  },
  data() {
    return {
      listOptions: [
        {
          text: '本周',
          startDate: this.$moment().weekday(0).format('YYYY-MM-DD'),
          endDate: this.$moment().weekday(6).format('YYYY-MM-DD'),
          awalysShow: true,
        },
        {
          text: '上周',
          startDate: this.$moment().weekday(-7).format('YYYY-MM-DD'),
          endDate: this.$moment().weekday(-1).format('YYYY-MM-DD'),
          awalysShow: true,
        },
        {
          text: '本月',
          startDate: this.$moment().startOf('month').format('YYYY-MM-DD'),
          endDate: this.$moment().endOf('month').format('YYYY-MM-DD'),
          awalysShow: true,
        },
        {
          text: '上月',
          startDate: this.$moment().subtract(1, 'months').startOf('month').format("YYYY-MM-DD"),
          endDate: this.$moment().subtract(1, 'months').endOf('month').format("YYYY-MM-DD"),
          awalysShow: true,
        },
        {
          text: '第一季度',
          startDate: this.$moment(this.$moment().year()+'-01').quarter(1).format("YYYY-MM-DD"),
          endDate:  this.$moment(this.$moment(this.$moment().year()+'-01').quarter(1)).endOf('quarter').format("YYYY-MM-DD"),
          awalysShow: true,
        },
        {
          text: '第二季度',
          startDate: this.$moment(this.$moment().year()+'-01').quarter(2).format("YYYY-MM-DD"),
          endDate:  this.$moment(this.$moment(this.$moment().year()+'-01').quarter(2)).endOf('quarter').format("YYYY-MM-DD"),
          awalysShow: false,
        },
        {
          text: '第三季度',
          startDate: this.$moment(this.$moment().year()+'-01').quarter(3).format("YYYY-MM-DD"),
          endDate:  this.$moment(this.$moment(this.$moment().year()+'-01').quarter(3)).endOf('quarter').format("YYYY-MM-DD"),
          awalysShow: false,
        },
        {
          text: '第四季度',
          startDate: this.$moment(this.$moment().year()+'-01').quarter(4).format("YYYY-MM-DD"),
          endDate:  this.$moment(this.$moment(this.$moment().year()+'-01').quarter(4)).endOf('quarter').format("YYYY-MM-DD"),
          awalysShow: false,
        },
        {
          text: '上半年',
          startDate: this.$moment(this.$moment().year()+'-01').startOf('month').format("YYYY-MM-DD"),
          endDate: this.$moment(this.$moment().year()+'-06').endOf('month').format("YYYY-MM-DD"),
          awalysShow: true,
        },
        {
          text: '下半年',
          startDate: this.$moment(this.$moment().year()+'-06').startOf('month').format("YYYY-MM-DD"),
          endDate: this.$moment(this.$moment().year()+'-12').endOf('month').format("YYYY-MM-DD"),
          awalysShow: false,
        },
        {
          text: '全年',
          startDate: this.$moment(this.$moment().year()+'-01').startOf('year').format("YYYY-MM-DD"),
          endDate: this.$moment(this.$moment().year()+'-01').endOf('year').format("YYYY-MM-DD"),
          awalysShow: true,
        },
      ],
      options: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        },
        shortcuts: [],
      },
      timeRange: [],
    };
  },
  computed: {},
  watch: {
    value: {
      immediate: true,
      handler (val) {
        this.timeRange = val
      }
    }
  },
  created() {
  },
  mounted() {
    this.initShortcuts()
  },
  methods: {
    // 初始化快捷键
    initShortcuts () {
      let currentDay = this.$moment(new Date()).format("YYYY-MM-DD")
      this.listOptions.forEach(( item, index ) => {
        // isBefore为true,表示当前的时间区间还没开始
        let isBefore= this.$moment(currentDay).isBefore(item.startDate)
        if ( isBefore && !item.awalysShow ) {
          // 禁用快捷键
          this.DisableDateShortcut(index)
        }
        this.options.shortcuts.push({
          text: item.text,
          value: () => [item.startDate, item.endDate]
        })
      })
    },
    /**
     * @description: 禁用范围时间选择器指定的快捷方式
     * @param { String | Number | Array } source 禁用快捷方式
     * @param { String } className 添加的指定样式class
     * */
    DisableDateShortcut (source = undefined, className = 'date-disabled') {
      this.$nextTick( () => {
        let disabledFn = (index, className) => {
          let el = document.getElementsByClassName('ivu-picker-panel-sidebar')
          let classNameList = el[0].childNodes[0].classList
          if ( el[0].childNodes[index] ) {
            el[0].childNodes[index].classList = classNameList.toString() + ` ${className}`
          }
        }
        let type = Object.prototype.toString.call(source)
        if ( type == '[object Array]' ) {
          source.forEach( source_item => {
            disabledFn(source_item, className)
          } )
        }else if ( type == '[object Number]' || type == '[object String]' ) {
          disabledFn(source, className)
        }else {
          throw Error('DisableDateShortcut：传入的第一个参数接受数组，字符串，数字，当前入参异常')
        }
      } )
    },
  },
  filters: {}
};
</script>

<style lang="less">
// 时间范围选择器快捷方式禁用
.date-range-disabled {
  .date-disabled {
    pointer-events: none;
    color: #ccc;
  }
}
</style>