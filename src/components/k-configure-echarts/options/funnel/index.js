// 漏斗图
import { merge } from 'lodash';
const handlerMinSize = function (...rest) {
  if (rest.length !== 3 || rest[0].value == 0) return 100;
  let minSize = (Number(rest[1].value) / 2 / Number(rest[0].value)) * 100 + '%';
  return minSize;
};
const funnerlOptions = data => {
  const defaultConfig = {
    title: {
      text: '',
      left: 'center',
      top: 'bottom'
    },
    tooltip: {
      extraCssText: `z-index:2`,
      formatter: function (data) {
        return `${data.name}${data.value}`;
      }
    },
    series: [
      {
        name: '',
        type: 'funnel',
        left: '10%',
        top: '10%',
        bottom: '10%',
        width: '60%',
        min: 0,
        // minSize: handlerMinSize(...data.series[0].data),
        // minSize: 100,
        maxSize: '80%',
        sort: 'descending',
        gap: 0,
        label: {
          show: true,
          position: 'inside'
        },
        labelLine: {
          length: 0, // 延伸线长度
          lineStyle: {
            width: 1,
            type: 'solid'
          }
        },
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        emphasis: {
          label: {
            // fontSize: 20
          }
        },
        markLine: {
          lineStyle: {
            width: 100
          }
        },
        data: [
          {
            value: 799,
            name: '访客量:',
            itemStyle: {
              color: '#E88E6D'
            },
            label: {
              position: 'right',
              color: '#666666',
              padding: [30, 0, 30, 30],
              formatter: function (data) {
                return `{a|${data.name}} {b|${data.value}}`;
              },
              rich: {
                a: {
                  color: '#666666',
                  fontSize: 10
                },
                b: {
                  fontWeight: 'bold',
                  color: '#000000',
                  fontSize: 12
                }
              }
            }
          },
          {
            value: 1000,
            name: '下单人数:',
            itemStyle: {
              color: 'rgba(232, 142, 109, .7)'
            },
            label: {
              position: 'right',
              color: '#666666',
              padding: [30, 0, 30, 30],
              formatter: function (data) {
                return `{a|${data.name}} {b|${data.value}}`;
              },
              rich: {
                a: {
                  color: '#666666',
                  fontSize: 10
                },
                b: {
                  fontWeight: 'bold',
                  color: '#000000',
                  fontSize: 12
                }
              }
            }
          },
          {
            value: 324,
            name: '支付人数:',
            itemStyle: {
              color: 'rgba(232, 142, 109, .4)'
            },
            label: {
              position: 'right',
              color: '#666666',
              padding: [30, 0, 30, 30],
              formatter: function (data) {
                return `{a|${data.name}} {b|${data.value}}`;
              },
              rich: {
                a: {
                  color: '#666666',
                  fontSize: 10
                },
                b: {
                  fontWeight: 'bold',
                  color: '#000000',
                  fontSize: 12
                }
              }
            }
          }
        ]
      }
    ]
  };

  const opt = merge(defaultConfig, data);
  return opt;
};

export default {
  funnerlOptions
};
