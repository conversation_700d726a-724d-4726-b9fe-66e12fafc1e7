import { merge } from 'lodash';
import { number_format } from '@/utils/filters';

let colorList = ['#7F9EE4', '#7FC5ED', '#A5DCDE', '#C7EEA9', '#DEA5C5', '#d9d9d9'];

// 文案居于左侧的横向柱状图
const horizontalBar = data => {
  const defaultConfig = {
    grid: {
      top: '10%',
      left: '160px',
      right: '10%',
      bottom: '6%'
    },
    tooltip: {
      show: true,
      formatter: a => {
        let val = number_format(a.value, 2);
        return `${a.name}：${val}元`;
      }
    },
    yAxis: {
      type: 'category',
      position: 'left',
      data: [],
      axisTick: {
        show: false // 去除x轴的刻度线
      },
      axisLabel: {
        interval: 0,
        inside: false,
        color: '#666', // 自定义x轴label的颜色
        fontSize: 12,
        verticalAlign: 'middle',
        // padding: [0,0,0,-200],
        width: 156,
        // margin: '160',
        overflow: 'truncate',
        ellipsis: '...',
        align: 'right'
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(204, 204, 204, .5)'
        }
      }
    },
    xAxis: {
      type: 'value',
      name: '',
      position: 'bottom',
      nameLocation: 'start',
      data: [],
      nameTextStyle: {
        color: '#999999', // 设置y轴单位的颜色
        fontSize: 12
      },
      axisTick: {
        show: false // 去除y轴的刻度线
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(204, 204, 204, .5)'
        }
      },
      splitLine: {
        show: true, // 显示y轴的网格线
        lineStyle: {
          color: '#CCCCCC',
          opacity: 0.3
        }
      }
      // axisLabel: {
      //   formatter: ''
      // }
    },
    series: [
      {
        data: [],
        type: 'bar',
        // color: '#9CA4FF', // 更改柱体0的颜色
        barWidth: '14px', // 设置柱子的宽度
        label: {
          show: true,
          position: 'right',
          formatter: a => {
            let val = number_format(a.value, 2);
            return `¥ ${val}`;
          },
          fontSize: 12,
          fontWeight: 'bolder'
        },
        itemStyle: {
          color: '#9CA4FF',
          label: {
            show: true,
            formatter: '{c}',
            position: 'right',
            textStyle: {
              color: '#1157E5',
              fontSize: 14
            }
          }
        }
      }
    ]
  };
  const opt = merge(defaultConfig, data);
  return opt;
};

// 竖向柱状图
const verticalBar = data => {
  const defaultConfig = {
    grid: {
      top: '10%',
      left: '2%',
      right: '2%',
      bottom: '16%'
    },
    tooltip: {
      show: true
    },
    xAxis: {
      type: 'category',
      data: [],
      axisTick: {
        show: false // 去除x轴的刻度线
      },
      axisLabel: {
        color: '#999' // 自定义x轴label的颜色
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(204, 204, 204, .5)'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: '人数',
      nameTextStyle: {
        color: '#999999', // 设置y轴单位的颜色
        fontSize: 14,
        padding: [0, 0, -30, 30] // 设置单位的位置
      },
      axisTick: {
        show: false // 去除y轴的刻度线
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(204, 204, 204, .5)'
        }
      },
      splitLine: {
        show: false // 去除y轴的网格线
      },
      axisLabel: {
        formatter: ''
      }
    },
    series: [
      {
        data: [],
        type: 'bar',
        color: 'rgba(17, 87, 229, .3)', // 更改柱体的颜色
        label: {
          show: true,
          formatter: '{c}',
          position: 'top',
          textStyle: {
            color: '#1157E5',
            fontSize: 14
          }
        },
        barWidth: '30%' // 设置柱子的宽度
      }
    ]
  };
  const opt = merge(defaultConfig, data);
  return opt;
};

// 文案居于柱状图上方
const customHorizontaBar = data => {
  const defaultConfig = {
    grid: {
      top: '10%',
      left: '4%',
      right: '10%',
      bottom: '8%'
    },
    tooltip: {
      show: true
    },
    yAxis: {
      type: 'category',
      offset: 0,
      position: 'left',
      // offset: -100,
      axisTick: {
        show: false // 去除x轴的刻度线
      },
      axisLabel: {
        interval: 0,
        color: '#666666', // 自定义x轴label的颜色
        fontSize: 12,
        verticalAlign: 'bottom',
        align: 'left',
        padding: [0, 0, 12, 10]
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(204, 204, 204, .5)'
        }
      }
    },
    xAxis: {
      type: 'value',
      name: '',
      nameTextStyle: {
        color: '#999999', // 设置y轴单位的颜色
        fontSize: 14,
        padding: [0, 0, -30, 60] // 设置单位的位置
      },
      axisTick: {
        show: false // 去除y轴的刻度线
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(204, 204, 204, .5)'
        }
      },
      splitLine: {
        show: true, // 显示y轴的网格线
        lineStyle: {
          color: '#CCCCCC',
          opacity: 0.3
        }
      },
      axisLabel: {
        formatter: ''
      }
    },
    series: [
      {
        type: 'bar',
        color: 'rgba(17, 87, 229, .3)', // 更改柱体的颜色
        barWidth: '20%', // 设置柱子的宽度
        // itemStyle: {
        label: {
          show: true,
          formatter: '{c}%',
          position: 'right',
          textStyle: {
            color: '#000',
            fontSize: 14
          }
          // }
        }
      }
    ]
  };
  const opt = merge(defaultConfig, data);
  return opt;
};

export default {
  horizontalBar,
  verticalBar,
  customHorizontaBar
};
