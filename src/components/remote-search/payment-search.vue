<!-- 带前置条件的付款方远程搜索 -->
<template>
  <div class="flex" style="margin-top: 1px">
    <Select
      :value="payment_type"
      clearable
      :placeholder="paymentPlaceholder"
      style="width: 90px"
      @on-clear="paymentTypeClear"
      @on-change="paymentTypeChange"
    >
      <Option v-for="item in paymentTypeDesc" :label="item.desc" :key="item.id" :value="item.id">
        <div class="custom-option">
          <span>{{ item.desc }}</span>
        </div>
      </Option>
    </Select>
    <Select
      ref="payment"
      transfer
      :value="value"
      :clearable="isClearable"
      :loading="searchLoading"
      :remote-method="search"
      filterable
      @on-clear="clearSub"
      @on-query-change="queryChange"
      class="filterable-select"
      :placeholder="placeholder"
    >
      <Option v-for="(option, index) in payment_list" :key="index" :value="option.ent_code" :label="option.ent_name">
        <div class="custom-option"  @click="selectSup(option)">
          <span>{{option.ent_name }}</span>
          <span class="disabled-tag" v-if="option.is_disabled === '1'">已禁用</span>
        </div>
      </Option>
    </Select>
  </div>
</template>

<script>
import util from '@/utils/util';

export default {
  props: {
    paymentPlaceholder: {
      type: String,
      default: '全部'
    },
    placeholder: {
      type: String,
      default: '请输入付款方',
    },
    isClearable: {
      type: Boolean,
      default: true,
    },
    // 付款方
    value: {
      type: String,
      default: '',
    },
    // 付款方类型
    payment_type: {
      type: String,
      default: '',
    },
    // 付款方类型枚举
    paymentTypeDesc: {
      type: Array,
      default: () => []
    },
  },
  name: 'payment-search',
  components: {},
  mixins: [],
  data() {
    return {
      searchLoading: false,
      payment_list: [],
    };
  },
  computed: {},
  watch: {
    value(val) {
      !val && this.searchMethod('');
    },
  },
  created() {
    if (!this.$route.query.ent_code) {
      this.searchMethod();
    } else {
      let list = JSON.parse(localStorage.getItem('payment_list')) || [];
      this.payment_list = list;
      this.$emit('input', list[0] && list[0].ent_code);
    }
  },
  mounted() {},
  methods: {
    searchMethod: util.debounce(function (query) {
      this.searchLoading = true;
      let params = {
        fill_cp: 1,
        fill_com: 1,
        name: query || '',
        ent_type: this.payment_type,
      };
      this.$api.searchPurchasesubject(params).then(res => {
        this.searchLoading = false;
        this.payment_list = res.list;
        localStorage.setItem('payment_list', JSON.stringify(this.payment_list));
      });
    }, 200),
    search() {},
    selectSup(val) {
      this.$emit('getEntTypeAux', val.ent_type)
      this.$emit('input', val.ent_code);
    },
    queryChange(val) {
      this.searchMethod(val);
    },
    // 外部清除
    // clear() {
    //   this.$refs['payment'].clearSingleSelect();
    // },
    clearSub() {
      this.searchMethod();
      this.$emit('getEntTypeAux', '')
      this.$emit('input', '');
    },
    changeEntType() {
      this.$emit('getEntTypeAux', '')
      this.$emit('input', '');
      this.searchMethod();
    },

    // 同步付款方类型
    paymentTypeClear (val) {
      this.setPaymentType()
    },
    paymentTypeChange (val) {
      this.setPaymentType(val)
    },
    setPaymentType (val = '') {
      this.$emit('getPaymentType', val)
      this.$emit('input', '');
      this.$emit('getEntTypeAux', '')
      this.searchMethod();
    },
  },
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
.disabled-tag {
  color: #ccc;
  margin-left: 10px;
}

.custom-option {
  padding: 7px 16px;
}
.ivu-select-item {
  padding: 0;
}
</style>
