<template>
  <Select
    ref="applyCompany"
    transfer
    :value="value"
    :clearable="isClearable"
    :loading="searchLoading"
    :remote-method="search"
    filterable
    :transfer-class-name="className"
    @on-query-change="queryChange"
    class="filterable-select"
    :placeholder="placeholder"
    @on-clear="clearSub"
    @on-select="selectSup"
  >
    <Option v-for="(option, index) in apply_company_list" :key="option.id" :value="option.id">{{ option.name }}</Option>
  </Select>
</template>

<script>
import util from '@/utils/util';
export default {
  name: 'apply-company-search',
  components: {},
  mixins: [],
  props: {
    placeholder: {
      type: String,
      default: '请输入申请主体的省公司'
    },
    isClearable: {
      type: Boolean,
      default: true
    },
    value: {
      type: String,
      default: ''
    },
    className: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      searchLoading: false,
      apply_company_list: [],
    };
  },
  computed: {},
  watch: {
    value(val) {
      !val && this.searchMethod('');
    }
  },
  created() {},
  mounted() {
    if (!this.$route.query.company_id) {
      this.searchMethod();
    } else {
      let list = JSON.parse(localStorage.getItem('apply_company_list')) || [];
      this.apply_company_list = list;
    }
  },
  methods: {
    searchMethod: util.debounce(function (query) {
      this.searchLoading = true;
      this.$api.getCompanyListName({ name: query }).then(res => {
        this.searchLoading = false;
        this.apply_company_list = res.list;
        localStorage.setItem('apply_company_list', JSON.stringify(this.apply_company_list));
      });
    }, 200),
    search() {},
    clear() {
      this.$refs.collection.clearSingleSelect();
    },
    selectSup(val) {
      this.$emit('input', val.value);
    },
    queryChange(val) {
      this.searchMethod(val);
    },
    clearSub() {
      this.searchMethod();
      this.$emit('input', '');
    }
  }
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
</style>
