<!-- 带前置搜索条件的诊所选择控件，不涉及远程，抽离控件，便于方便管理 -->
<!--外部调用demo,通过directChange事件同步外部的自诊所类型数据-->
<!--<clinic-type-search-->
<!--  v-model="queryFormData.clinic_type"-->
<!--  :clinic_direct_type="queryFormData.clinic_direct_type"-->
<!--  :directTypeDesc="directTypeDesc"-->
<!--  :typeDesc="typeDesc"-->
<!--  @directChange="val => (queryFormData.clinic_direct_type = val)"-->
<!--&gt;</clinic-type-search>-->
<template>
  <div>
    <Select
      :value="clinic_direct_type"
      clearable
      :placeholder="directPlaceholder"
      style="width: 90px"
      @on-clear="directClear"
      @on-change="directChange"
    >
      <Option v-for="item in directTypeDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
    </Select>
    <Select
      :value="value"
      clearable
      :placeholder="clinicPlaceholder"
      style="width: 100px"
      @on-change="typeChange"
      :disabled="!clinic_direct_type"
    >
      <Option v-for="item in typeDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
    </Select>
  </div>
</template>

<script>
export default {
  name: 'clinic-type-search',
  props: {
    directPlaceholder: {
      type: String,
      default: '诊所类型'
    },
    clinicPlaceholder: {
      type: String,
      default: '诊所子类型'
    },
    clinic_direct_type: {
      type: String,
      default: '',
    },
    value: {
      type: String,
      default: '',
    },
    // 诊所类型枚举
    directTypeDesc: {
      type: Array,
      default: () => [],
    },
    // 诊所自类型枚举
    typeDesc: {
      type: Array,
      default: () => [],
    },
  },
  components: {},
  mixins: [],
  data() {
    return {};
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    directChange(val) {
      this.$emit('directChange', '');
      this.$emit('directChange', val);
    },
    directClear() {
      this.$emit('input', '');
    },
    typeChange(val) {
      this.$emit('input', val);
    },
  },
};
</script>

<style lang="less" scoped></style>
