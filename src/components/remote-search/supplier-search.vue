<template>
  <Select
    ref="supplier"
    closeable
    transfer
    :value="value"
    :clearable="isClearable"
    :loading="searchLoading"
    :remote-method="search"
    filterable
    :transfer-class-name="className"
    :disabled="disabled"
    @on-clear="clearSub"
    @on-query-change="queryChange"
    class="filterable-select"
    :placeholder="placeholder"
    :style="{ width: getWidth, height }"
    @on-select="selectSup"
  >
    <Option v-for="(option, index) in supplierList" :key="option.id" :value="option.id">{{ option.name }}</Option>
  </Select>
</template>

<script>
import util from '@/utils/util';
export default {
  name: 'partner-search',
  components: {},
  mixins: [],
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    showAll: {
      type: Boolean,
      default: true,
    },
    isClearable: {
      type: Boolean,
      default: true,
    },
    value: {
      type: String,
      default: '',
    },
    width: {
      type: Number | String,
      default: 200,
    },
    height: {
      type: String,
      default: '32px',
    },
    placeholder: {
      type: String,
      default: '请输入搜索供应商',
    },
    className: {
      type: String,
      default: '',
    },
    // 当传入的supplierName存在值的时候，说明要执行回显
    supplierName: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      searchLoading: false,
      supplierList: [],
      query: '',
    };
  },
  computed: {
    getWidth() {
      if (typeof this.width === 'number') {
        return this.width + 'px';
      } else {
        return this.width;
      }
    },
  },
  watch: {
    value(val) {
      if (!val) {
        this.searchMethod('')
      }
    }
  },
  created() {
    if (!this.supplierName) {
      if (!this.$route.query.supplier_id) {
        this.searchMethod('');
      } else {
        // 用户详情回显
        let list = JSON.parse(localStorage.getItem('erp_supplierList')) || [];
        this.supplierList = list;
        this.$emit('input', list[0] && list[0].id);
      }
    }
  },
  mounted() {},
  methods: {
    searchMethod: util.debounce(function (query) {
      this.searchLoading = true;
      this.$api.getSupplierList({ q: query}).then(res => {
        this.searchLoading = false;

        this.supplierList = res.list;
        localStorage.setItem('erp_supplierList', JSON.stringify(this.supplierList));
      });
    }, 200),
    search() {},
    selectSup(val) {
      this.$emit('input', val.value);
      this.$emit('change', val.value);
    },
    queryChange(val) {
      if (this.supplierName) {
        this.searchMethod(this.supplierName);
        this.$emit('clearsupplierName');
      } else {
        this.searchMethod(val);
      }
    },
    clear() {
      this.$refs.supplier.clearSingleSelect();
    },
    clearSub() {
      this.searchMethod();
      this.$emit('input', '');
      this.$emit('change', '');
    },
  },
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
</style>
