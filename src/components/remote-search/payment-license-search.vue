<template>
  <Select
    ref="collection"
    transfer
    :value="value"
    :clearable="isClearable"
    :loading="searchLoading"
    :remote-method="search"
    filterable
    :transfer-class-name="className"
    @on-query-change="queryChange"
    class="filterable-select"
    :placeholder="placeholder"
    @on-clear="clearSub"
    @on-select="selectSup"
  >
    <Option
      v-for="(option, index) in license_list"
      :key="index"
      :label="option.organization_name"
      :value="option.ent_code"
    >
      <span>{{ option.organization_name }}</span>
      <span class="disabled-tag" v-if="option.is_disabled === '1'">已禁用</span>
    </Option>
  </Select>
</template>

<script>
import util from '@/utils/util';
export default {
  name: 'payment-license-search',
  components: {},
  mixins: [],
  props: {
    placeholder: {
      type: String,
      default: '请输入营业执照名称（付款方）',
    },
    isClearable: {
      type: Boolean,
      default: true,
    },
    value: {
      type: String,
      default: '',
    },
    className: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      searchLoading: false,
      license_list: [],
    };
  },
  computed: {},
  watch: {
    value(val) {
      !val && this.searchMethod('');
    },
  },
  created() {},
  mounted() {
    if (!this.$route.query.organ_payer_id) {
      this.searchMethod();
    } else {
      let list = JSON.parse(localStorage.getItem('license_list')) || [];
      this.license_list = list;
    }
  },
  methods: {
    searchMethod: util.debounce(function (query) {
      this.searchLoading = true;
      let params = {
        fill_cp: 1,
        fill_com: 1,
        filter_organ_name: 1,
        organization_name: query || '',
      };
      this.$api.searchPurchasesubject(params).then(res => {
        this.searchLoading = false;
        this.license_list = res.list;
        localStorage.setItem('license_list', JSON.stringify(this.license_list));
      });
    }, 200),
    search() {},
    clear() {
      this.$refs.collection.clearSingleSelect();
    },
    selectSup(val) {
      let type = '';
      this.license_list.some(item => {
        console.log('=>(-search.vue:88) ', item.ent_code === val.value, item);
        if (item.ent_code === val.value) {
          type = item.ent_type;
          return true;
        }
      });
      this.$emit('input', val.value);
      this.$emit('getOrganEntType', type);
    },
    queryChange(val) {
      this.searchMethod(val);
    },
    clearSub() {
      this.searchMethod();
      this.$emit('input', '');
      this.$emit('getOrganEntType', '');
    },
  },
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
.disabled-tag {
  color: #ccc;
  margin-left: 10px;
}
</style>
