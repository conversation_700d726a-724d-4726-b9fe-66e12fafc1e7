<template>
  <Select
    ref="collection"
    transfer
    :value="value"
    :clearable="isClearable"
    :loading="searchLoading"
    :remote-method="search"
    filterable
    :transfer-class-name="className"
    @on-query-change="queryChange"
    class="filterable-select"
    :placeholder="placeholder"
    @on-clear="clearSub"
    @on-select="selectSup"
  >
    <Option v-for="(option, index) in collection_list" :key="option.id" :value="option.id">{{ option.name }}</Option>
  </Select>
</template>

<script>
import util from '@/utils/util';
export default {
  name: 'collection-search',
  components: {},
  mixins: [],
  props: {
    placeholder: {
      type: String,
      default: '请输入收款方'
    },
    isClearable: {
      type: Boolean,
      default: true
    },
    value: {
      type: String,
      default: ''
    },
    className: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      searchLoading: false,
      collection_list: [],
    };
  },
  computed: {},
  watch: {
    value(val) {
      !val && this.searchMethod('');
    }
  },
  created() {},
  mounted() {
    if (!this.$route.query.payee_id) {
      this.searchMethod();
    } else {
      let list = JSON.parse(localStorage.getItem('collection_list')) || [];
      this.collection_list = list;
      // this.$emit('input', list[0] && list[0].id)
    }
  },
  methods: {
    searchMethod: util.debounce(function (query) {
      this.searchLoading = true;
      this.$api.getCompanyPayeeList({ name: query }).then(res => {
        this.searchLoading = false;
        this.collection_list = res.list;
        localStorage.setItem('collection_list', JSON.stringify(this.collection_list));
      });
    }, 200),
    search() {},
    clear() {
      this.$refs.collection.clearSingleSelect();
    },
    selectSup(val) {
      this.$emit('input', val.value);
    },
    queryChange(val) {
      this.searchMethod(val);
    },
    clearSub() {
      this.searchMethod();
      this.$emit('input', '');
    }
  }
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
</style>
