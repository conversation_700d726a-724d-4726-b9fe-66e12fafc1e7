<template>
  <div class="picture-wrapper">
    <div class="picture-list" v-viewer="{ url: 'data-source', zIndex: 9999 }">
      <draggable v-model="getImgUrls" @change="onDragChange" filter=".no-drag">
        <div class="picture-list__pic" v-for="(url, key) in getImgUrls" :key="key">
          <!--          :style="{ backgroundImage: 'url(' + url + '-B.200)' }"-->
          <img
            v-if="isImg(url)"
            :src="url + '-B.w200'"
            :data-source="url"
            style="position: absolute; width: 100%; height: 100%; left: 0"
          />
          <div v-else style="position: absolute; width: 100%; height: 100%; left: 0">
            <Tooltip :content="getTooltip(url)">
              <a
                style="
                  font-size: 50px;
                  display: inline-flex;
                  align-items: center;
                  width: 100%;
                  padding-top: 6px;
                  padding-left: 6px;
                "
                @click="previewFile(url)"
              >
                <svg-icon :iconClass="getSvgIcon(url)"></svg-icon>
              </a>
            </Tooltip>
          </div>
          <div class="picture-list__remove" @click="onRemove(key)" v-if="!isQueryDetail"></div>
        </div>
        <div class="flex-inline flex-item-align flex-warp picture-display no-drag">
          <div class="picture-upload" v-show="showUploadBtn && !isQueryDetail">
            <Upload :multiple="multiple" @on-success="onUploadSuccess" v-bind="$attrs">
              <div class="picture-upload-icon">
                <div class="upload-limit">{{ imgUrls.length }} / {{ limit }}</div>
              </div>
            </Upload>
          </div>
        </div>
      </draggable>

    </div>
    <!-- 添加图片样式会换行,暂时手动调整 -->

    <div class="upload-tips">
      <slot style="align-self: self-end"></slot>
    </div>
  </div>
</template>

<script>
import Vue from 'vue';
import Upload from '@/components/upload/qiniu-upload';
import draggable from 'vuedraggable';
import 'viewerjs/dist/viewer.css';
import './index.less';
import Viewer from 'v-viewer';
// import imageUrl from 'utils/imageUrl';
import axios from 'axios';

Viewer.setDefaults({
  zIndexInline: 9999,
});
Vue.use(Viewer);

export default {
  name: 'k-picture',
  components: {
    Upload,
    draggable,
  },
  props: {
    limit: {
      type: Number,
      default: 1,
    },
    value: {
      type: [String, Array],
      default() {
        return [];
      },
    },
    isQueryDetail: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      showUploadBtn: true,
      imgUrls: [],
      isArray: true,
    };
  },
  created() {
    // console.log(this.isQueryDetail)
  },
  methods: {
    previewFile(url) {
      const suffix = url.split('.').pop();
      const a = document.createElement('a');
      a.href = url;
      a.target = '_blank';
      a.style.display = 'none';
      a.download = suffix === 'pdf' ? '' : `${Date.now()}.${suffix}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    },
    onUploadSuccess: function (imgUrl) {
      console.log('%c=>(picture.vue:100) imgUrl', 'font-size: 18px;color: #FF7043 ;', imgUrl);
      if (this.imgUrls.length < this.limit) {
        this.imgUrls.push(imgUrl);
        this.$emit('on-success', imgUrl);
        this.notifyEmit();
      }
    },
    onRemove: function (key) {
      // if (this.pdfList.length) {
      //   const index = this.imgUrls.indexOf(url);
      //   console.log('-> %c index  picture.vue -- 109 ===    %o', 'font-size: 15px;color: #00B771 ;', index);
      //   this.imgUrls.splice(index, 1);
      // } else {
      this.imgUrls.splice(key, 1);
      this.$emit('on-remove', key);
      // }

      this.notifyEmit();
    },
    onDragChange: function () {
      this.notifyEmit();
    },
    notifyEmit: function () {
      this.$emit('input', this.isArray ? this.imgUrls : this.imgUrls[0] || '');
    },
  },
  computed: {
    isImg() {
      return url => {
        const suffix = url.split('.').pop();
        return ['jpg', 'jpeg', 'png', 'gif'].includes(suffix);
      };
    },
    getSvgIcon() {
      return url => {
        return url.split('.').pop() || 'file';
      };
    },
    getTooltip() {
      return url => {
        const suffix = url.split('.').pop();
        return suffix === 'pdf' ? '点击在新窗口查看' : '点击可直接下载';
      };
    },
    multiple() {
      return this.limit <= 1 ? false : true;
    },
    // pdfList() {
    //   const imgSuffix = ['jpg', 'jpeg', 'png', 'gif'];
    //   return this.imgUrls.filter(item => {
    //     const suffix = item.split('.').pop();
    //     return !imgSuffix.includes(suffix);
    //     // return item.indexOf('.pdf') > -1 || item.indexOf('.doc') > -1
    //   });
    // },
    getImgUrls: {
      get() {
        // const imgSuffix = ['jpg', 'jpeg', 'png', 'gif'];
        // return this.imgUrls.filter(item => {
        //   // console.log(item, 'image item')
        //   const suffix = item.split('.').pop();
        //   // console.log(suffix, 'image suffix')
        //   return imgSuffix.includes(suffix);
        // });
        return this.imgUrls;
      },
      set(val) {
        this.imgUrls = val;
      },
    },
    // getImgUrls() {
    //   const imgSuffix = ['jpg', 'jpeg', 'png', 'gif']
    //   return this.imgUrls.filter(item=>{
    //     const suffix = item.split('.').pop()
    //     return imgSuffix.includes(suffix)
    //   })
    // },
  },
  watch: {
    value: {
      immediate: true,
      handler: function () {
        if (typeof this.value == 'string') {
          let regUrl = (this.value && this.value.replace(/(\.jpg|\.png|\.gif|\.jpeg)-B\..*$/, '$1')) || '';
          this.isArray = false;
          this.imgUrls = this.value != '' ? [regUrl] : [];
        } else {
          if (this.value instanceof Array) {
            this.imgUrls = [];
            this.value.forEach(url => {
              this.imgUrls.push((url && url.replace(/(\.jpg|\.png|\.gif|\.jpeg)-B\..*$/, '$1')) || '');
            });
          } else {
            this.imgUrls = [];
          }
        }
      },
    },

    imgUrls: {
      immediate: true,
      handler: function () {
        // imageUrl
        // console.log(this.imgUrls)
        // for (let key in this.imgUrls) {
        //   this.imgUrls[key] = imageUrl.imageCdnDomain(this.imgUrls[key])
        // //   // console.log("-> %c this.imgUrls[key]  === %o", "font-size: 15px;color: green;", this.imgUrls[key])
        // }
        this.showUploadBtn = this.imgUrls.length >= this.limit ? false : true;
      },
    },
  },
};
</script>

<style lang="less" scoped>
.pdf-box {
  display: inline-flex;
  flex-direction: column;
  margin-bottom: 12px;
}

.doc-list {
  position: relative;
}

.doc-list__remove {
  position: absolute;
  width: 20px;
  height: 20px;
  top: -5px;
  right: -10px;
  cursor: pointer;
  background: url(https://static.rsjxx.com/backstage/2019/0509/103116_32332.png) no-repeat;
  background-size: 20px 20px;
  background-position: 50%;
}
</style>
