<template>
  <div class="index-wrapper">
    <span class="audit-text">
      <i class="status-dot" :style="getStatusTextColor(status)"></i>
      <slot></slot>
    </span>
  </div>
</template>

<script>
export default {
  name: 'statusText',
  mixins: [],

  components: {},

  props: {
    status: {
      type: String,
      default: '',
    },
  },

  data() {
    return {};
  },

  computed: {
    getStatusTextColor() {
      /**
       * @method getStatusTextColor
       * @param {status} type  Number - description
       * @description: 0 -- 待审核  1 -- 审核通过  2 -- 审核驳回
       * @author: yangyi
       * @date: 9/2/22
       */
      return status => {
        switch (status) {
          case '10':
          case 'TBP':
          case 'WAIT':
          case 'WAIT_AUDIT':
          case 'W_START':
          case 'W_AUDIT':
            return {
              background: '#ffad33',
            };
          case '90':
          case 'ON':
          case 'UP':
          case 'enable':
          case 'UNDERWAY':
            return {
              background: '#19be6b',
            };
          case '70':
          case 'OFF':
          case 'DOWN':
          case 'disable':
          case 'SOLD_OUT':
          case 'WAIT_AUDIT_REJECT':
          case 'REJ':
            return {
              background: '#ed4014',
            };
          case 'END':
          case 'CLOSED':
            return {
              background: '#999999',
            };
        }
      };
    },
  },

  watch: {},

  created() {},

  mounted() {},

  methods: {},

  destroyed() {},
};
</script>

<style scoped lang="less">
.audit-text {
  .status-dot {
    display: inline-block;
    border-radius: 50%;
    width: 10px;
    height: 10px;
    margin-right: 4px;
  }
}
</style>
