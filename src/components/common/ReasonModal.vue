<template>
  <div class="reason-modal-wrapper">
    <Modal :value="modalVisible" :mask-closable="false" title="审核驳回">
      <div>
        <p class="mb10">请输入驳回原因</p>
        <Input v-model="refuseText" class="refuse-input" placeholder="请输入驳回原因" type="textarea" />
      </div>

      <div slot="footer">
        <Button @click="refuseCancel">取消</Button>
        <Button type="primary" @click="refuseOk">确定</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
export default {
  name: 'ReasonModal',
  mixins: [],
  model: {
    prop: 'reason',
    event: 'input'
  },
  components: {},

  props: {
    modalVisible: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      refuseText: ''
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  methods: {
    refuseOk() {
      this.$emit('input', this.refuseText);
    },
    refuseCancel() {}
  },

  destroyed() {}
};
</script>

<style scoped lang="less"></style>
