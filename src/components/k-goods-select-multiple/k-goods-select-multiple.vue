<template>
  <Modal v-model="modalValue" title="选择商品" :mask-closable="false" :width="750" class-name="vertical-center-modal">
    <div style="position: relative">
      <Button to="/goods/warehouse/list" target="_blank">商品管理</Button>
      <Dvd /><Dvd />
      <a @click="onRefresh">刷新</a>
      <div style="position: absolute; right: 0px; top: 0px">
        <Select
          v-model="queryFormData.goods_type"
          clearable
          placeholder="请选择商品类型"
          @on-change="onSearch"
          style="width: 200px"
        >
          <Option v-for="(item, id) in typeDesc" :key="id" :value="id">{{ item.desc }}</Option>
        </Select>
        <Dvd />
        <Dvd />
        <Dvd />
        <Input
          v-model="queryFormData.name"
          placeholder="搜索商品标题或者商品ID"
          clearable
          @keyup.enter.native="onSearch"
          @on-clear="onSearch"
          style="width: 180px"
        >
          <Icon type="ios-search" slot="suffix" />
        </Input>
      </div>
    </div>
    <div class="block_10"></div>
    <div style="position: relative; overflow: auto">
      <Table
        ref="selection"
        height="350"
        @on-select="onSelect"
        @on-select-cancel="onSelectCancel"
        @on-select-all="onSelectAll"
        @on-select-all-cancel="onSelectAllCancel"
        :columns="tableCols"
        :data="list"
        :loading="tableLoading"
      >
        <template slot-scope="{ row }" slot="id">
          {{ row.id }}
        </template>
        <!--        <template slot-scope="{row}" slot="info">-->
        <!--          <div class="media-left media-middle">-->
        <!--            <img :src="row.main_img|imageStyle('B.100')" style="width:35px;margin-right:5px;" class="img-rounded" :title="'id:'+row.id"/>-->
        <!--          </div>-->
        <!--          <div class="media-body">-->
        <!--            <div class="clip"><KLink :to="{path: '/goods/warehouse/list', query: {id: row.id}}" target="_blank">{{row.name}}</KLink></div>-->
        <!--            <div>价格：￥{{row.price}}</div>-->
        <!--            <div>类型：{{row.goods_type_text}}</div>-->
        <!--          </div>-->
        <!--        </template>-->
        <template slot-scope="{ row }" slot="price"> ￥{{ row.price }} </template>
      </Table>

      <div class="block_20"></div>
      <div class="flex-c flex-item-align" style="margin-bottom: 30px">
        <Select v-model="goods_style" clearable placeholder="请选择商品样式" style="width: 150px" v-if="showStyle">
          <Option v-for="(item, id) in styleList" :key="item.id" :value="item.id">{{ item.label }}</Option>
        </Select>
        <KPage
          v-if="total > 0"
          :total="total"
          :page-size="queryFormData.pageSize"
          :page-size-opts="[5]"
          :current="queryFormData.page"
          :show-sizer="false"
          @on-change="onPageChange"
          @on-page-size-change="onPageSizeChange"
          size="small"
          style="text-align: right; margin-left: 10px; margin-top: 10px"
        />
      </div>
    </div>
    <div slot="footer">
      <div v-if="Object.keys(selected_items).length > 0" style="display: inline-block" class="lr15 text-muted">
        已选: 商品(<span class="text-error">{{ Object.keys(selected_items).length }}</span
        >)
      </div>
      <Button @click="modalValue = false">取消</Button>
      <Button type="primary" @click="onConfirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
/* eslint-disable */
import S from 'utils/util'; // Some commonly used tools
import io from 'utils/request'; // Http request
import * as runtime from 'utils/runtime'; // Runtime information
/* eslint-disable */

let init_query_from_data = {
  page: 1,
  pageSize: 5,
  name: '',
  goods_type: '',
  status_list: [],
  xn_scope: '0,1,9',
  status: '200'
};

export default {
  name: 'k-goods-select-multiple',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    disabledItemIds: {
      type: Array,
      default() {
        return [];
      }
    },
    showStyle: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      queryFormData: { ...init_query_from_data },
      modalValue: false,
      styleList: [
        { label: '一行一个', id: 1 },
        { label: '一行两个', id: 2 }
      ],
      tableCols: [
        { type: 'selection', width: 60 },
        { title: '商品ID', slot: 'id', width: 70 },
        { title: '商品名称', key: 'name' },
        { title: '商品类型', key: 'goods_type_text' },
        { title: '商品售价', slot: 'price' }
      ],
      tableLoading: false,

      list: [],
      total: 0,
      typeDesc: {},
      selected_items: {},
      goods_style: 1 //商品样式
    };
  },
  created() {
    this.$api.getGoodsLibOptions().then(res => {
      this.typeDesc = res.typeDesc;
    });
  },
  methods: {
    onSearch: function () {
      this.queryFormData.page = 1;
      this.get();
    },

    onPageChange: function (page, pageSize) {
      console.log('-> page, pageSize', page, pageSize);
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize || 5;
      this.get();
    },
    onPageSizeChange(size) {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = size;
      this.get();
    },
    onRefresh: function () {
      this.get();
    },

    onSelect: function (selection, row) {
      this.$set(this.selected_items, row.id, row);
    },

    onSelectAll: function (selection) {
      selection.forEach(item => {
        this.$set(this.selected_items, item.id, item);
      });
    },

    onSelectCancel: function (selection, row) {
      this.$delete(this.selected_items, row.id);
    },

    onSelectAllCancel: function (selection) {
      for (let k in this.list) {
        this.$delete(this.selected_items, this.list[k].id);
      }
    },

    onConfirm: function () {
      // if(!this.goods_style){
      //   this.$Message.error('请选择商品样式')
      //   return
      // }
      if (!Object.keys(this.selected_items).length) {
        this.$Message.error('请选择商品');
        return;
      }
      let items = [];
      for (let key in this.selected_items) {
        items.push(this.selected_items[key]);
      }
      console.log(this.goods_style);
      this.$emit('on-selected', items, this.goods_style);
    },

    get: function () {
      this.tableLoading = true;

      this.$api
        .getGoodsLibList(this.queryFormData)
        .then(data => {
          this.list = this.handler(data.list);
          this.statusDesc = data.statusDesc;
          this.total = Number(data.total);

          this.tableLoading = false;
        })
        .catch(error => {

        });
    },

    handler: function (list) {
      for (let k in list) {
        for (let j in this.selected_items) {
          if (list[k].id == this.selected_items[j].id) {
            list[k]['_checked'] = true; // 选中已选项
          }
        }

        if (S.inArray(Number(list[k].id), this.disabledItemIds)) {
          list[k]['_disabled'] = true; // 选中已选项
        }
      }
      return list;
    },

    clearQuery: function () {
      this.queryFormData = { ...init_query_from_data };
      this.queryFormData.page = 1;
      this.list = [];
      this.total = 0;
      this.selected_items = {};
    }
  },

  watch: {
    value: function (val) {
      this.modalValue = val;
      if (val == true) {
        this.clearQuery();
        this.get();
      } else {
        // this.goods_style = ''
      }
    },

    modalValue: function (val) {
      this.$emit('input', val);
    },

    selected_items: function (val) {
      // S.log(this.selected_items)
    }
  }
};
</script>

<style lang="less">
.vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;
  .ivu-modal {
    top: 0;
  }
  .ivu-modal-body {
    padding-bottom: 0;
  }
}
.clip {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 90%;
  display: inline-block;
}
</style>
