<template>
  <el-popover
    placement="bottom"
    width="900"
    transfer
    :visible-arrow="false"
    :poper-options="popOptions"
    popper-class="pop-common"
    @show="onShow"
    :disabled="isDetail"
    v-model="showPop"
    trigger="click"
  >
    <slot slot="reference" style="width: 100%"></slot>
    <div class="popover-content-box">
      <div class="header-box">
        <h4 class="title">{{ title }}</h4>
        <div class="search-box">
          <span>搜索：</span>
          <Input
            v-model="queryFormData.code"
            placeholder="请输入销售单编号"
            style="width: 200px"
            clearable
            @keyup.enter.native="onSearch"
            class="mr10"
          >
            <!-- <Button slot="append" icon="ios-search" @click="getList"></Button> -->
          </Input>
          <Input
            v-model="queryFormData.keyword"
            placeholder="请输入客户姓名/手机号/备注"
            style="width: 200px"
            clearable
            @keyup.enter.native="onSearch"
            class="mr10"
          >
            <!-- <Button slot="append" icon="ios-search" @click="getList"></Button> -->
          </Input>
          <Select
            v-model="queryFormData.warehouse_code"
            style="width: 200px"
            placeholder="请选择仓库"
            class="mr10"
            clearable
            transfer
            transfer-class-name="warehouseSelect"
          >
            <Option v-for="item in warehouseList" :value="item.code" :key="item.code">{{ item.name }}</Option>
          </Select>

          <Button type="primary" @click="onSearch">搜索</Button>
          <!--          <Input v-model="queryFormData.code" placeholder="销售单编号" @keyup.enter.native='getList'>-->
          <!--            <Button slot="append" icon="ios-search" @click="getList"></Button>-->
          <!--          </Input>-->
        </div>
      </div>
      <div class="table-wrapper">
        <Table :columns="tableCols" :data="list" ref="section-table" height="300" :loading="tableLoading">
          <!-- 勾选 -->
          <template slot-scope="{ row, index }" slot="checkBox">
            <Checkbox :value="list[index].checked" @on-change="changeSelectOrder(row, index)"></Checkbox>
          </template>

          <template slot-scope="{ row }" slot="cg_name">
            {{ row.cg_name || '-' }}
          </template>

          <template slot-scope="{ row }" slot="payment_fee"> ￥{{ row.payment_fee }} </template>

          <!-- 拍单时间 -->
          <template slot-scope="{ row }" slot="deal_time">
            {{ row.deal_time | date_format('YYYY-MM-DD') }}
          </template>

          <!-- 支付时间 -->
          <template slot-scope="{ row }" slot="pay_time">
            {{ row.pay_time | date_format('YYYY-MM-DD') }}
          </template>
          <!-- 备注 -->
          <template slot-scope="{ row }" slot="remark">
            {{ row.remark || '-' }}
          </template>

          <!-- 创建人 -->
          <template slot-scope="{ row }" slot="operator">
            {{ row.operator || '-' }}
          </template>

          <!-- 创建时间 -->
          <template slot-scope="{ row }" slot="create_time">
            {{ row.create_time | date_format('YYYY-MM-DD') }}
          </template>

          <!-- 更新时间 -->
          <template slot-scope="{ row }" slot="update_time">
            {{ row.update_time | date_format('YYYY-MM-DD') }}
          </template>
        </Table>
        <div class="block_20"></div>
        <KPage
          :total="total"
          :page-size="+queryFormData.pageSize"
          :current="+queryFormData.page"
          @on-change="handleCurrentChange"
          @on-page-size-change="handleSizeChange"
          style="text-align: center"
          :show-sizer="false"
        />
      </div>
      <div class="bottom-btn-wrapper">
        <Button @click="onCancel">取消</Button>
        <Dvd />
        <Dvd />
        <Dvd />
        <Button type="primary" @click="onConfirm">确定</Button>
      </div>
    </div>
  </el-popover>
</template>

<script>
// import S from '@/utils/util'
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  code: '', // 采购单编号
  need_prod_info: 1, // 是否需要采购的产品信息
  r: '',
  keyword: '' // 客户信息
};
export default {
  name: 'select-order-popper',
  mixins: [],

  components: {},

  props: {
    title: {
      type: String,
      default: '选择销售订单'
    },
    apiName: {
      type: String,
      default: 'getErpOrderList'
    },
    code: {
      type: String,
      default: ''
    },
    // 审核状态；10 待审核，90 审核完成，70 拒绝
    audit_status: {
      type: String,
      default: '90'
    },
    isDetail: {
      type: Boolean,
      default: false
    },
    stock_status: {
      type: String,
      default: () => 'WAIT,PART'
    }
  },

  data() {
    return {
      popOptions: { boundariesElement: 'body', gpuAcceleration: false },
      tableLoading: false,
      list: [],
      tableCols: [
        { slot: 'checkBox', align: 'center', fixed: 'left' },
        { title: '销售单编号', key: 'code', align: 'center' },
        { title: '客户姓名', slot: 'cg_name', align: 'center' },
        { title: '销售单金额', slot: 'payment_fee', align: 'center' },
        { title: '拍单时间', slot: 'deal_time', align: 'center' },
        { title: '支付时间', slot: 'pay_time', align: 'center' },
        { title: '备注', slot: 'remark', align: 'center' },
        { title: '审核状态', key: 'audit_status_text', align: 'center' },
        { title: '库存状态', key: 'stock_status_text', align: 'center' },
        { title: '创建人', slot: 'operator', align: 'center' },
        { title: '创建时间', slot: 'create_time', align: 'center' },
        { title: '更新时间', slot: 'update_time', align: 'center' }
      ],
      queryFormData: {
        ...init_query_form_data
      },
      total: 0,
      selected_items: {},
      showPop: false,
      warehouseList: []
    };
  },

  computed: {},

  watch: {
    showPop: {
      immediate: true,
      handler(val) {
        if (val) {
          this.queryFormData = { ...init_query_form_data };
          this.getErpWarehouseList();
          this.list &&
            this.list.some((item, index) => {
              if (item.code == this.code) {
                this.changeSelectOrder('', index);
                return true;
              }
            });
        } else {
          this.selected_items = {};
        }
      }
    }
  },

  created() {},

  mounted() {},

  methods: {
    changeSelectOrder(row, index) {
      let isChecked = this.list[index].checked;
      this.list.map(item => (item.checked = false));
      this.list[index].checked = !isChecked;
      if (isChecked) {
        this.selected_items = {};
      } else {
        this.selected_items = this.list[index];
      }
    },
    onCancel() {
      this.showPop = false;
    },
    onConfirm() {
      console.log(21312);
      this.$emit('selectSup', this.selected_items);
      this.showPop = false;
    },
    onSearch() {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = 20;
      this.getList();
    },
    getList() {
      this.tableLoading = true;
      let params = {
        ...this.queryFormData,
        audit_status: this.audit_status,
        stock_status: this.stock_status
      };
      if (this.apiName === 'getErpOrderList') {
        params.invalid_status = '2';
      }
      this.$api[this.apiName](params).then(res => {
        this.tableLoading = false;
        this.list = this.handler(res.list);
        this.total = res.total;
      });
    },
    handler(list) {
      if (!list) return [];
      list.forEach((item, index) => {
        item.checked = false;
        if (item.code == this.code || this.selected_items.code == item.code) {
          item.checked = true;
          this.selected_items = item;
        }
      });
      return list;
    },
    handleSizeChange(val) {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.queryFormData.page = val;
      this.getList();
    },
    onShow() {
      this.getList();
      this.showPop = true;
    },
    getErpWarehouseList() {
      this.$api.getErpWarehouseList().then(res => {
        this.warehouseList = res.list;
      });
    }
  },

  destroyed() {}
};
</script>

<style scoped lang="less">
:deep(.el-popover__reference-wrapper) {
  width: 100%;
}

.popover-content-box {
  .header-box {
    .title {
      font-weight: 600;
      line-height: 50px;
      padding: 0 20px;
      font-size: 16px;
      border-bottom: 1px solid rgb(223, 225, 230);
    }

    .search-box {
      display: flex;
      align-items: center;
      padding: 10px 20px;

      .el-input {
        width: 200px;
      }

      .el-button {
        margin-left: 10px;
      }
    }
  }

  .bottom-btn-wrapper {
    height: 50px;
    display: flex;
    justify-content: flex-end;
    padding: 10px 20px;
    border-top: 1px solid rgb(223, 225, 230);
    margin-top: 20px;
  }
}
</style>
<style lang="less">
.pop-common {
  padding: 0 !important;
}

body .warehouseSelect {
  z-index: 2000000 !important;
}
</style>
