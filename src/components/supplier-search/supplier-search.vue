<template>
  <!-- <Select
			transfer
			:value="value"
			:clearable="isClearable"
			:loading="searchLoading"
			:remote-method="searchMethod"
			filterable
			@on-clear="clearSub"
			class="filterable-select"
			placeholder="请输入、搜索供应商"
			@on-select="selectSup">
		<Option v-for="(option, index) in supplier_list" :key="option.id" :value="option.id">{{ option.name }}</Option>
	</Select> -->
  <Select
    :value="value"
    style="width: 200px"
    @on-select="selectSup"
    @on-clear="clearSub"
    :auto-complete="false"
    :clearable="isClearable"
    placeholder="请选择供应商"
  >
    <Option v-for="(option, index) in supplier_list" :key="option.id" :value="option.id">{{ option.name }}</Option>
  </Select>
</template>

<script>
import S from 'utils/util';
import { mapState, mapMutations } from 'vuex';
export default {
  name: 'supplier-search',
  components: {},
  mixins: [],
  props: {
    isClearable: {
      type: Boolean,
      default: true,
    },
    value: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      searchLoading: false,
      supplierList: [],
      query: '',
    };
  },
  computed: {
    ...mapState('goods', ['supplier_list']),
  },
  watch: {},
  created() {
    this.searchMethod();
  },
  mounted() {},
  methods: {
    ...mapMutations('goods', ['SET_LIST']),
    async searchMethod(query) {
      this.searchLoading = true;
      await this.$api.getSupplierList({ q: query, pageSize: 50 }).then(res => {
        console.log(res);
        this.searchLoading = false;
        this.SET_LIST({ type: 'supplier_list', value: res.list });
      });
    },
    selectSup(val) {
      console.log('val', val);

      this.$emit('input', val.value);
    },
    clearSub() {
      this.$emit('input', '');
    },
  },
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
</style>
