<template>
  <Select
    ref="company"
    :clearable="isClearable"
    :loading="searchLoading"
    :remote-method="searchMethod"
    :value="value"
    class="filterable-select"
    filterable
    placeholder="请输入、搜索省公司"
    transfer
    :auto-complete="false"
    @on-clear="searchMethod('')"
    @on-change="selectSup"
  >
    <Option v-for="(option, index) in company_list" :key="option.id" :value="option.id">{{ option.name }}</Option>
  </Select>
</template>

<script>
import { mapMutations, mapState } from 'vuex';

export default {
  name: 'supplier-search',
  components: {},
  mixins: [],
  props: {
    isClearable: {
      type: Boolean,
      default: true
    },
    value: {
      type: String,
      default: ''
    },
    status: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      select_company: '',
      searchLoading: false,
      supplierList: []
    };
  },
  computed: {
    ...mapState('goods', ['company_list'])
  },
  watch: {},
  created() {
    this.searchMethod();
  },
  mounted() {},
  methods: {
    ...mapMutations('goods', ['SET_LIST']),
    searchMethod(query) {
      this.searchLoading = true;
      this.$api.getComList({ name: query, status: this.status }).then(res => {
        console.log(res);
        this.SET_LIST({ type: 'company_list', value: res.list });
        this.searchLoading = false;
      });
    },
    selectSup(val) {
      this.$emit('input', val);
      this.$emit('changeProv', val);
    }
  }
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
</style>
