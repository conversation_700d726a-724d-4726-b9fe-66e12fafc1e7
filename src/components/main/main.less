@sidebar-width: 118px; // 左侧菜单的宽度

.app-sidebar {
  width: @sidebar-width;
  transition: all 0.2s;
  position: fixed;
  left: 0;
  top: 0;
  height: 100%;
  z-index: 200;
}
body.without-second-sidebar .app-sidebar {
  width: 92px;
}

.app-container {
  width: auto;
  margin: 0 0 0 @sidebar-width;
  padding-bottom: 16px;
  //transition: margin-right .5s;
  background-color: #f2f2f2;
  .app-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    font-size: 14px;
    top: 0;
    line-height: 56px;
    min-width: 1150px;
    box-sizing: border-box;
    background: #fff;
    padding: 0 0 0 5px;
    transition: padding-right 0.5s;
    z-index: 10;
    box-shadow: 0 0 3px #9e9e9e9c;
  }
}
body.without-second-sidebar .app-container {
  margin-left: 92px;
}

.app-footer {
  position: absolute;
  bottom: 0;
  left: @sidebar-width;
  right: 0px;
  padding: 4px 0;
  text-align: center;
  span {
    font-size: 12px;
    color: #ccc;
  }
}
body.without-second-sidebar .app-footer {
  left: 92px;
}

.app-wrapper.show-help {
  .app-container,
  .app-footer {
    margin-right: 450px;
  }
}
.app-help-container {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 450px;
  background: #fff;
  z-index: 299;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  font-size: 12px;
  border-left: 1px solid #e5e5e5;
}
.app-help-container .help-container-head {
  line-height: 56px;
  height: 56px;
  padding: 0 20px;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  box-shadow: 0 0 3px #9e9e9e9c;
}
.app-help-container .help-container-body {
  padding: 10px 10px 60px;
  overflow-y: scroll;
  height: 100%;
}
.back-btn-box {
  font-size: 12px;
  color: #666666;
  .ivu-btn-small {
    height: 22px;
  }
  .ivu-btn-small:hover {
    .back-icon {
      color: #447cdd !important;
    }
  }
  .back-icon {
    color: #666666 !important;
    width: 10px;
    height: auto;
    margin-right: 4px;
  }
}
