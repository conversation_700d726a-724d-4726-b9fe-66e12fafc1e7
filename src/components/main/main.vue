<template>
  <div class="app-wrapper" :class="{ 'show-help': $store.state.app.showHelpWrapper }">
    <div class="app-sidebar">
      <side-menu></side-menu>
    </div>
    <div class="app-container">
      <div class="app-header" v-if="showThirdSidebar">
        <div class="app-header_left">
          <Breadcrumb class="ks-breadcrumb">
            <template v-for="(item, key) in breadcrumbs">
              <BreadcrumbItem v-if="item.path" :to="item.path" class="bbSize" :key="key">
                <i v-if="item.icon" class="fa" :class="item.icon"></i> {{ item.name }}
              </BreadcrumbItem>
              <BreadcrumbItem v-else class="bbSize" :key="key"> {{ item.name }}</BreadcrumbItem>
            </template>
          </Breadcrumb>
          <div class="back-btn-box" v-show="showBack">
            <Button size="small" @click="routerBack">
              <svg-icon iconClass="back-btn" class="back-icon"></svg-icon>
              <span class="back-text">返回</span>
            </Button>
          </div>
        </div>
        <div class="app-header_right">
<!--          <div class="app-header_trigger" @click="showDownLoad">-->
<!--            <Icon type="ios-download-outline" size="22" id="downloadCenter" />-->
<!--          </div>-->
          <div class="app-header_trigger" @click="notifyDropdown = true">
            <Dropdown trigger="custom" :visible="notifyDropdown" @on-clickoutside="notifyDropdown = false">
              <Icon type="ios-notifications-outline" size="22" />
              <DropdownMenu slot="list" style="width: 300px; height: 400px">
                <p style="text-align: center">暂无通知</p>
              </DropdownMenu>
            </Dropdown>
          </div>
          <div class="app-header_trigger">
            <div style="display: flex; align-items: center">
              <Avatar icon="ios-person" size="small" class="space6" style="background-color: #87d068" />
              <div class="flex flex-c" style="height: 56px; justify-content: center">
                  <span class="space6" style="line-height: 1.5; text-align: left; font-weight: 400">{{
                      userInfo.name
                    }}</span>
                <span class="space6" style="line-height: 1; font-size: 12px; color: #999999">{{
                    userInfo.mobile
                  }}</span>
              </div>
<!--              <Icon type="ios-arrow-down" />-->
            </div>
<!--            <Dropdown trigger="click" @on-click="onDropDownClick">-->
<!--              <div style="display: flex; align-items: center">-->
<!--                <Avatar icon="ios-person" size="small" class="space6" style="background-color: #87d068" />-->
<!--                <div class="flex flex-c" style="height: 56px; justify-content: center">-->
<!--                  <span class="space6" style="line-height: 1.5; text-align: left; font-weight: 400">{{-->
<!--                    userInfo.name-->
<!--                  }}</span>-->
<!--                  <span class="space6" style="line-height: 1; font-size: 12px; color: #999999">{{-->
<!--                    userInfo.mobile-->
<!--                  }}</span>-->
<!--                </div>-->
<!--                <Icon type="ios-arrow-down" />-->
<!--              </div>-->

<!--              &lt;!&ndash;              <p class="space6">{{ userInfo.clinic_name }}</p>&ndash;&gt;-->
<!--              <DropdownMenu slot="list" style="text-align: left">-->
<!--                <DropdownItem name="setting">-->
<!--                  暂无-->
<!--                </DropdownItem>-->
<!--                &lt;!&ndash;                <DropdownItem name="info">&ndash;&gt;-->
<!--                &lt;!&ndash;                  <Icon type="md-person" size="16"/> 基本信息&ndash;&gt;-->
<!--                &lt;!&ndash;                </DropdownItem>&ndash;&gt;-->
<!--                &lt;!&ndash;                <DropdownItem disabled>&ndash;&gt;-->
<!--                &lt;!&ndash;                  <Icon type="md-pricetag" size="16"/> 版本:v{{code_version||'1.0'}}&ndash;&gt;-->
<!--                &lt;!&ndash;                </DropdownItem>&ndash;&gt;-->
<!--&lt;!&ndash;                <DropdownItem name="logout">&ndash;&gt;-->
<!--&lt;!&ndash;                  <Icon type="ios-log-out" size="16" />&ndash;&gt;-->
<!--&lt;!&ndash;                  退出登录&ndash;&gt;-->
<!--&lt;!&ndash;                </DropdownItem>&ndash;&gt;-->
<!--              </DropdownMenu>-->
<!--            </Dropdown>-->
          </div>
        </div>
      </div>
      <div class="app">
        <div class="app-inner" :class="{ 'app-inner-no-margin': !showThirdSidebar }" :style="appInnerStyles">
          <template v-if="!isServeRun">
            <!-- 只在build模式下缓存导航 -->
            <vue-page-stack>
              <router-view />
            </vue-page-stack>
          </template>
          <template v-else>
            <router-view />
          </template>
        </div>
      </div>
    </div>

    <div class="app-footer">
      <span>上海树家医学科技有限公司提供技术支持 V1.0</span> <span style="display: none">{{ code_version }}</span>
    </div>

    <Modal v-model="logoutModal" :width="300" @on-ok="onLogout"> 确定退出登录？ </Modal>
<!--    <DownloadCenter></DownloadCenter>-->
  </div>
</template>

<script>
/* eslint-disable */
import S from 'utils/util'; // Some commonly used tools
import * as runtime from 'utils/runtime'; // Runtime information
/* eslint-disable */
import config from '@/config';
import SideMenu from './components/side-menu';
import DownloadCenter from './components/DownloadCenter.vue';

import './main.less';
import { mapState } from 'vuex';
import router from '../../router';

export default {
  name: 'Main',
  components: {
    SideMenu,
    DownloadCenter,
  },
  data() {
    return {
      breadcrumbs: [],
      appInnerStyles: {
        minHeight: '',
        height: '',
        margin: '10px',
        padding: '16px',
        background: '#FFFFFF',
      },

      userInfo: {},
      isServeRun: false,
      code_version: '',

      logoutModal: false,
      notifyDropdown: false,
      // downloadVisible: false,
    };
  },
  created() {
    this.code_version = config.codeVersion;
    this.isServeRun = S.isServeRun;
    this.userInfo = runtime.getUser();
  },

  methods: {
    appInnerHeight: function () {
      let currentScreenHeight = document.documentElement.clientHeight;
      let dh = 92;
      if (!this.showThirdSidebar) {
        dh = 25;
      }
      this.appInnerStyles.minHeight = currentScreenHeight - dh + 'px';
    },

    onDropDownClick: function (name) {
      if (name == 'logout') {
        this.logoutModal = true;
      }
    },

    onLogout: function () {
      runtime.logout();
      this.$router.push({ path: '/login', query: { from: encodeURIComponent(this.$route.fullPath) } });
    },

    getBreadcrumbs: function (to) {
      let path = to.path;
      let breadcrumbs = [
        {
          path: '/',
          icon: 'fa-home',
          name: '首页',
        },
      ];

      let sup_menu_path = '',
        sup_menu_name = '',
        sup_menu_icon = '',
        activeFirstMenuId = '';

      for (let pid in this.secondMenusAll) {
        let items = this.secondMenusAll[pid];
        items.forEach((item, index) => {
          if (item.path == path) {
            if (item.type != 'SUB_MENU') {
              items.forEach(t => {
                if (t.id == item.p_id) {
                  activeFirstMenuId = t.p_id;
                  sup_menu_name = t.name;
                  sup_menu_path = t.path;
                }
              });
            } else {
              activeFirstMenuId = item.p_id;
            }
          }
        });
      }

      // 一级标题
      let firstMenu = '';
      this.firstMenus.forEach(item => {
        if (item.id == activeFirstMenuId) {
          firstMenu = item.name;
        }
      });

      if (sup_menu_path) {
        breadcrumbs.push({
          path: sup_menu_path,
          name: firstMenu + ' - ' + sup_menu_name,
        });

        breadcrumbs.push({
          path: '',
          name: to.meta.title,
        });
      } else {
        breadcrumbs.push({
          path: '',
          name: firstMenu + ' - ' + to.meta.title,
        });
      }

      return breadcrumbs;
    },

    routerBack() {
      this.$router.back();
    },

    showDownLoad() {
      this.$store.commit('downloadCenter/CHANGE_DOWNLOAD_VISIBLE', true);
    },
  },
  mounted() {
    this.appInnerHeight();
    window.addEventListener('resize', () => {
      return (() => {
        this.appInnerHeight();
      })();
    });
  },
  activated() {},
  computed: {
    ...mapState('menus', {
      firstMenus: state => state.firstMenus,
      secondMenusAll: state => state.secondMenusAll,
      customPageStyle: state => state.customPageStyle,
    }),
    showThirdSidebar: function () {
      return this.$store.state.menus.showSecondMenu;
    },
    showBack() {
      // this.$route
      return this.$route.meta.type === 'PAGE' && window.history.length > 2;
    },
  },

  watch: {
    $route: {
      immediate: true,
      handler: function (to) {
        this.breadcrumbs = this.getBreadcrumbs(to);
        this.$nextTick(() => {
          // const app = document.getElementsByClassName('app-inner')
          if (to.path.includes('statistics') && !to.path.includes('report')) {
            this.appInnerStyles.margin = '0px 10px 10px';
            this.appInnerStyles.background = '#f2f2f2';
            this.appInnerStyles.padding = 0;
          } else {
            this.appInnerStyles = { ...this.appInnerStyles, ...this.customPageStyle };
          }
        });
      },
    },
    showThirdSidebar: function () {
      this.appInnerHeight();
    },
  },
};
</script>

<style lang="less">
.app-header_left {
  display: flex;
  align-items: center;
}

.app-header_right {
  display: inline-block;
  float: right;
  margin-right: 20px;
}

.app-header_trigger {
  display: inline-block;
  width: auto;
  padding: 0 12px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease-in-out;

  &:hover {
    color: #2277ff;
    background-color: #f8f8f9;
  }
}

.ks-breadcrumb {
  padding: 0 20px 0 28px;
  color: #969799;
  line-height: 0;

  &.ivu-breadcrumb a {
    color: #969799;
  }

  &.ivu-breadcrumb > span:last-child {
    color: #323233;
    font-weight: 400;
  }
}

.app-inner {
  //margin: 10px;
  //padding: 16px;
  min-width: 1150px;
  //min-height: 450px;
  background-color: #fff;
}

.app-inner-no-margin {
  margin: 0 0 10px 0;
}

.bbSize {
  font-size: 14px;
}
</style>
