<template>
  <div style="height: 100%">
<!--    <div class="app-first-sidebar">-->
<!--      <div class="app-logo">-->
<!--        <k-link :to="{ path: '/' }" class="logo-wrap">-->
<!--          <div class="current-logo-img">-->
<!--            <img src="@/assets/image/plat_logo.png" alt="" />-->
<!--          </div>-->
<!--        </k-link>-->
<!--      </div>-->
<!--      &lt;!&ndash; 一级菜单 &ndash;&gt;-->
<!--      <ul class="app-first-sidebar-nav" @mouseleave="onPreviewSecondMenus(false)">-->
<!--        <li-->
<!--          v-for="item in firstMenus"-->
<!--          :key="item.pid"-->
<!--          :data-pid="item.id"-->
<!--          :class="{-->
<!--            active: isActive(item) || item.redirect == activeMenuPath,-->
<!--            hover: isOpenPreview == true && item.id == preview.activeFirstMenuId,-->
<!--          }"-->
<!--        >-->
<!--          <k-link-->
<!--            :to="{ path: item.path, query: item.query }"-->
<!--            @mouseover.native="onPreviewSecondMenus(item.path)"-->
<!--            target="_self"-->
<!--            ><i class="fa" :class="item.meta.icon"></i> {{ item.name }}</k-link-->
<!--          >-->
<!--        </li>-->
<!--      </ul>-->
<!--      &lt;!&ndash;      <div class="footer-img logo-img">&ndash;&gt;-->
<!--      &lt;!&ndash;        <img src="@/assets/image/company_logo_bk.png" alt="" />&ndash;&gt;-->
<!--      &lt;!&ndash;      </div>&ndash;&gt;-->
<!--      &lt;!&ndash;      <Poptip trigger="hover" placement="top-start" transfer>&ndash;&gt;-->
<!--      &lt;!&ndash;        <div class="app-user-info">&ndash;&gt;-->
<!--      &lt;!&ndash;          <span class="name">{{ userInfo.name }}</span>&ndash;&gt;-->
<!--      &lt;!&ndash;        </div>&ndash;&gt;-->
<!--      &lt;!&ndash;        <div class="app-team-info" slot="content">&ndash;&gt;-->
<!--      &lt;!&ndash;          <dd>&ndash;&gt;-->
<!--      &lt;!&ndash;            <dl><k-link to="/platform/member/update"> <i class="fa fa-cog"></i> 帐户资料</k-link></dl>&ndash;&gt;-->
<!--      &lt;!&ndash;            <dl><k-link to="/platform/member/updatepassword"> <i class="fa fa-key"></i> 修改密码</k-link></dl>&ndash;&gt;-->
<!--      &lt;!&ndash;            <dl><div @click="logoutModal=true"> <i class="fa fa-sign-out"></i> 退出登录</div></dl>&ndash;&gt;-->
<!--      &lt;!&ndash;          </dd>&ndash;&gt;-->
<!--      &lt;!&ndash;        </div>&ndash;&gt;-->
<!--      &lt;!&ndash;      </Poptip>&ndash;&gt;-->
<!--    </div>-->
    <!-- 二级菜单 -->
    <div class="app-second-sidebar" v-if="secondMenus.length > 0" v-show="!isOpenPreview">
      <div class="second-sidebar-title">{{ activeFirstTitle }}管理</div>
      <ul class="second-sidebar-nav hidden-scroll">
        <li
          v-for="(item, key) in secondMenus"
          :key="key"
          :class="[item.id == activeSecondMenuId ? 'active' : '', item.type === 'GROUP' ? 'is-group-title' : '']"
        >
          <span class="group-text" v-if="item.type === 'GROUP'">{{ item.name }}</span>
          <k-link :to="{ path: item.path, query: item.query }" target="_self" v-else>{{ item.name }}</k-link>
        </li>
      </ul>
    </div>

    <!-- 动态二级菜单 -->
    <div class="app-second-sidebar preview" v-show="isOpenPreview" @mouseleave="isOpenPreview = false">
      <div class="second-sidebar-title">{{ preview.activeFirstTitle }}管理</div>
      <ul class="second-sidebar-nav hidden-scroll">
        <li
          v-for="(item, key) in preview.secondMenus"
          :key="key"
          :class="[item.type === 'GROUP' ? 'is-group-title' : '']"
        >
          <span class="group-text" v-if="item.type === 'GROUP'">{{ item.name }}</span>
          <k-link :to="{ path: item.path, query: item.query }" target="_self" v-else>{{ item.name }}</k-link>
        </li>
      </ul>
    </div>

    <Modal v-model="logoutModal" :width="300" @on-ok="onLogout"> 确定退出登录？ </Modal>
  </div>
</template>

<script>
import './side-menu.less';
import { mapState } from 'vuex';
/* eslint-disable */
import S from 'utils/util';
import * as runtime from 'utils/runtime';
/* eslint-disable */

export default {
  name: 'SideMenu',
  data() {
    return {
      activeMenuPath: this.$route.path,
      activeFirstMenuId: 0,
      activeFirstTitle: '',
      activeSecondMenuId: 0,
      secondMenus: [],

      isOpenPreview: false,
      preview: {
        activeFirstMenuId: 0,
        activeFirstTitle: '',
        activeSecondMenuId: 0,
        secondMenus: [],
      },
      timerid: 0,

      logoutModal: false,
      userInfo: {},
    };
  },
  computed: {
    ...mapState('menus', {
      firstMenus: state => state.firstMenus,
      secondMenusAll: state => state.secondMenusAll,
    }),
  },
  created() {
    const d = this.renderSecondMenus(this.activeMenuPath);
    this.userInfo = runtime.getUser();
  },
  watch: {
    $route(to) {
      this.activeMenuPath = to.path;
      this.renderSecondMenus(this.activeMenuPath);
      this.isOpenPreview = false;
    },
  },
  methods: {
    isActive({ path, redirect, id }, index) {
      return id === this.activeFirstMenuId;
      //
      // let curPath
      // if(path == '/'){
      // 	curPath = redirect.split('/')
      // }else {
      // 	curPath = path.split('/')
      // }
      // let activePathArr = this.activeMenuPath.split('/')
      // return curPath[1] == activePathArr[1]
    },
    renderSecondMenus: function (path) {
      const d = this.handlerSecondMenus(path);
      this.activeFirstMenuId = d.activeFirstMenuId;
      this.activeFirstTitle = d.activeFirstTitle;
      this.activeSecondMenuId = d.activeSecondMenuId;
      this.secondMenus = d.secondMenus;

      if (this.secondMenus.length > 0) {
        this.$store.commit('menus/SET_SHOW_SECOND_MENU', true);
      } else {
        this.$store.commit('menus/SET_SHOW_SECOND_MENU', false);
      }
    },

    onPreviewSecondMenus: function (path) {
      if (this.timerid != null) {
        clearTimeout(this.timerid);
        this.timerid = null;
      }
      if (!path) {
        return;
      }

      this.timerid = setTimeout(() => {
        const d = this.handlerSecondMenus(path);
        this.preview.activeFirstMenuId = d.activeFirstMenuId;
        //在此处return 是为了hover【HIS】的时候能丢掉hover样式，阻止二级菜单渲染
        // if(path==='/his/outpatient/list'){
        // 	return false
        // }
        this.preview.activeFirstTitle = d.activeFirstTitle;
        this.preview.activeSecondMenuId = d.activeSecondMenuId;
        this.preview.secondMenus = d.secondMenus;

        if (this.preview.secondMenus.length <= 0 || this.activeFirstMenuId == this.preview.activeFirstMenuId) {
          this.isOpenPreview = false;
        } else {
          this.isOpenPreview = true;
        }
        this.timerid = null;
      }, 100);
    },

    handlerSecondMenus: function (path) {
      let activeFirstMenuId = 0;
      let activeSecondMenuId = 0;
      let secondMenus = [];
      let activeFirstTitle = '';

      this.firstMenus.forEach(item => {
        if (item.path == path) {
          activeFirstMenuId = item.pid;
          if (path == '/') {
            path = item.redirect;
          }
        }
      });

      let secondRoutes = [];
      for (let pid in this.secondMenusAll) {
        let items = this.secondMenusAll[pid];
        items.forEach(item => {
          if (item.path == path) {
            if (item.type == 'SUB_MENU') {
              activeFirstMenuId = item.p_id;
              activeSecondMenuId = item.id;
            } else {
              activeSecondMenuId = item.p_id;
              items.forEach(t => {
                if (t.id == item.p_id) {
                  activeFirstMenuId = t.p_id;
                }
              });
            }
            secondRoutes = items;
          }
        });
      }

      secondRoutes.forEach(item => {
        if (item.type == 'SUB_MENU' || item.type === 'GROUP') {
          secondMenus.push(item);
        }
      });

      // 一级标题
      this.firstMenus.forEach(item => {
        if (item.id == activeFirstMenuId) {
          activeFirstTitle = item.name;
        }
      });

      return {
        activeFirstMenuId,
        activeFirstTitle,
        activeSecondMenuId,
        secondMenus,
      };
    },

    onLogout: function () {
      runtime.logout();
      this.$router.push({ path: '/login', query: { from: encodeURIComponent(this.$route.fullPath) } });
    },
  },
};
</script>

<style lang="less">
.logo-wrap {
  margin: 0 11px !important;
  height: 70px !important;
  // padding-bottom: 10px;
}
.current-logo-img {
  margin: 24px auto 0;
  img {
    border-radius: 2px;
    width: 70px !important;
    height: 36px !important;
  }
}
.app-first-sidebar-nav {
  margin-top: 70px !important;
}
.footer-img {
  display: flex;
  justify-content: center;
  padding-bottom: 30px;
  img {
    height: 22px;
    width: 60px;
  }
}
.ivu-poptip-inner {
  border-radius: 1px !important;
}
.ivu-poptip-popper .ivu-poptip-body {
  padding: 0;
}
.app-team-info dl {
  margin: 0;
  div,
  a {
    display: block;
    padding: 10px 20px;
    color: #666;
    cursor: pointer;
    &:hover {
      background: #3283fa;
      color: #fff;
    }
  }
}
//.hr-solid-content {
//  color: #a2a9b6;
//  border: 0;
//  font-size: 12px;
//  padding: 1em 0;
//  position: relative;
//}
//.hr-solid-content::before {
//  content: attr(data-content);
//  position: absolute;
//  padding: 0 1ch;
//  line-height: 1px;
//  border: dashed #d0d0d5;
//  border-width: 0 99vw;
//  width: fit-content;
//  /* for 不支持fit-content浏览器 */
//  white-space: nowrap;
//  left: 50%;
//  transform: translateX(-50%);
//  border-image: repeating-linear-gradient(90deg, #d0d0d5, #d0d0d5 1px, transparent 1px, transparent 2px) 0 85% /  0 repeat;
//
//}
//.is-hr{
//  margin-bottom: 0!important;
//}
.second-sidebar-nav {
  .is-group-title {
    width: 117px;
    height: 36px;
    background: rgba(0, 0, 0, 0.03);
    box-shadow: 0px 1px 1px 0px #f1f1f1;
    cursor: unset;
    margin-left: 0;
    .group-text {
      width: 100%;
      display: inline-block;
      font-size: 12px;
      font-weight: 300;
      color: #999999;
      line-height: 17px;
      text-indent: 20px;
    }
  }
}
</style>
