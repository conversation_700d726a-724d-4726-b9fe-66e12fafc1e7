<template>
  <Modal
    :value="downloadVisible"
    title="下载中心"
    width="900px"
    @on-cancel="cancel"
    @on-visible-change="visibleChange"
    footer-hide
    :mask-closable="false"
  >
    <div class="download-box">
      <div style="flex: 1; margin-bottom: 20px; overflow: auto" ref="table-wrapper">
        <Table :columns="tableCols" :data="list">
          <template v-slot:create_time="{ row }">
            {{ row.create_time | date_format }}
          </template>
          <template v-slot:file_size="{ row }">
            <div v-show="row.file_size || row.file_size === 0">{{ row.file_size }}K</div>
            <div v-show="!row.file_size">-</div>
          </template>
          <template v-slot:status_action="{ row }">
            <div v-show="row.status === '1' || row.status === '2'" style="color: #bbb">处理中，请稍等...</div>
            <a v-show="row.status === '3'" type="text" class="download" @click="download(row.url, row.file)">下载</a>
            <a v-show="row.status === '4'" type="text" class="retry" @click="retryDownloadMission(row.id)">重试</a>
          </template>
        </Table>
      </div>

      <KPage
        :total="total"
        :page-size="queryFormData.pageSize"
        :current="queryFormData.page"
        @on-change="handleCurrentChange"
        @on-page-size-change="handleSizeChange"
        style="text-align: right; height: 40px"
      />
    </div>
  </Modal>
</template>

<script>
import { $operator } from '@/utils/operation';
import { mapState } from 'vuex';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  status: '', // 状态 1=待执行、2=执行中、3=已执行、4=失败
  is_download: '', // 是否下载 1=是、2=否
};
export default {
  // 下载中心
  name: 'DownloadCenter',

  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  mixins: [],
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      tableCols: [
        { title: '日期', slot: 'create_time', align: 'center', width: 160 },
        { title: '报表名称', key: 'file', align: 'center', minWidth: 300 },
        { title: '大小', slot: 'file_size', align: 'center' },
        { title: '状态', slot: 'status_action', align: 'center', width: 140 },
      ],
      list: [],
      total: 0,
      pageSize: 20,
      page: 1,
      timer: null,
    };
  },
  computed: {
    ...mapState('downloadCenter', {
      downloadVisible: state => state.visible,
    }),
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    visibleChange(val) {
      if (val) {
        this.getDownloadCenterList();
      } else {
        this.queryFormData = { ...init_query_form_data };
        clearTimeout(this.timer);
        this.timer = null;
      }
    },
    cancel() {
      this.$store.commit('downloadCenter/CHANGE_DOWNLOAD_VISIBLE', false);
    },
    handleCurrentChange(val) {
      this.queryFormData.page = val;
      this.getDownloadCenterList();
    },
    handleSizeChange(val) {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = val;
      this.getDownloadCenterList();
    },
    getDownloadCenterList(scroll = true) {
      clearTimeout(this.timer);
      let params = { ...this.queryFormData };
      this.$api
        .getDownloadCenterList(params)
        .then(res => {
          res.list.forEach(item => {
            if (item.file_size) {
              item.file_size = $operator.divide(item.file_size, 1024, 2);
            }
          });
          this.list = res.list;
          this.total = res.total;
          if (scroll) {
            this.$refs['table-wrapper'].scrollTop = 0;
          }
          let flag = this.list.every(item => item.status === '3' || item.status === '4');
          if (!flag) {
            this.timer = setTimeout(() => {
              this.getDownloadCenterList(false);
            }, 5000);
          }
        })
        .catch(err => {});
    },
    retryDownloadMission(id) {
      let params = { id };
      this.$api
        .retryDownloadMission(params)
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.getDownloadCenterList(false);
        })
        .catch(err => {});
    },
    download(url, title = '') {
      const downloadLink = document.createElement('a');
      downloadLink.setAttribute('href', url);
      downloadLink.setAttribute('target', '_blank');
      downloadLink.setAttribute('style', 'display:none');
      downloadLink.setAttribute('download', title);
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    },
  },
};
</script>
<style lang="less" scoped>
::v-deep .ivu-modal-body {
  padding: 20px 30px;
  height: calc(~'100% - 60px');
  overflow-y: auto;
}
::v-deep .ivu-modal {
  height: calc(~'100% - 100px') !important;
}
::v-deep .ivu-modal-content {
  height: calc(~'100% - 100px');
}

::v-deep .ivu-table-wrapper {
  overflow: unset;
  .ivu-table {
    overflow: unset;
    .ivu-table-header {
      position: sticky;
      top: 0;
      z-index: 100;
    }
  }
}

.download-box {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.download {
  color: #155bd4;
}
.retry {
  color: #ff7743;
}
</style>
