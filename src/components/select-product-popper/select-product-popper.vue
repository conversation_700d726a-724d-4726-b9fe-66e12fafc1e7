<template>
  <el-popover
    placement="bottom"
    width="700"
    transfer
    :visible-arrow="false"
    :poper-options="popOptions"
    popper-class="pop-common"
    @show="onShow"
    v-model="showPop"
    :disabled="isDetail"
    trigger="click"
  >
    <slot slot="reference" style="width: 100%"></slot>
    <div class="popover-content-box">
      <div class="header-box">
        <h4 class="title">选择产品</h4>
        <div class="search-box">
          <span>搜索：</span>
          <Input v-model="queryFormData.q" placeholder="请输入产品名称/编码/条形码" @keyup.enter.native="onSearch">
            <Button slot="append" icon="ios-search" @click="onSearch"></Button>
          </Input>
        </div>
      </div>
      <div class="mt10">
        <Table
          :columns="tableCols"
          :data="list"
          :loading="tableLoading"
          height="300"
          @on-select-all="selectAll"
          @on-select="select"
          @on-select-all-cancel="selectAllCancel"
          @on-select-cancel="selectCancel"
        >
          <template slot-scope="{ row }" slot="sales_price">
            {{ row.sales_price ? `￥${row.sales_price}` : '-' }}
          </template>

          <!-- 产品条码 -->
          <template slot-scope="{ row }" slot="barcode">
            {{ row.barcode || '-' }}
          </template>

          <!-- 规格 -->
          <template slot-scope="{ row }" slot="spec">
            {{ row.spec || '-' }}
          </template>
        </Table>
        <div class="block_20"></div>
        <KPage
          :total="total"
          :page-size="+queryFormData.pageSize"
          :current="+queryFormData.page"
          @on-change="handleCurrentChange"
          @on-page-size-change="handleSizeChange"
          style="text-align: center"
          :show-sizer="false"
        />
      </div>
      <div class="bottom-btn-wrapper">
        <Button @click="onCancel">取消</Button>
        <Dvd />
        <Dvd />
        <Dvd />
        <Button type="primary" @click="onConfirm">确定</Button>
      </div>
    </div>
  </el-popover>
</template>

<script>
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  q: '', // 产品名称/编码/条形码
  status: 'ENABLE',
  r: ''
};
// import S from '@/utils/util'
export default {
  name: 'select-product-popper',
  mixins: [],

  components: {},

  props: {
    title: {
      type: String,
      default: '选择供应商'
    },
    apiName: {
      type: String,
      default: 'getErpProductList'
    },
    code: {
      type: [String, Array],
      default: () => []
    },
    isDetail: {
      type: Boolean,
      default: () => false
    },
    supplier_code: {
      type: String,
      default: () => ''
    },
    warehouse_code: {
      type: String,
      default: ''
    },
    product_list: {
      type: Array,
      default: () => []
    },
    // 状态；上架 ENABLE，下架 DISABLE
    status: {
      type: String,
      default: () => 'ENABLE'
    }
  },

  data() {
    return {
      popOptions: { boundariesElement: 'body', gpuAcceleration: false },
      tableLoading: false,
      list: [],
      tableCols: [
        { type: 'selection', align: 'center' },
        { title: '产品编号', key: 'code', align: 'center' },
        { title: '产品条码', slot: 'barcode', align: 'center' },
        { title: '产品名称', key: 'name', align: 'center' },
        { title: '产品规格', slot: 'spec', align: 'center' },
        { title: '产品单位', key: 'unit', align: 'center' },
        { title: '市场价', slot: 'sales_price', align: 'center' }
      ],
      queryFormData: {
        ...init_query_form_data
      },
      total: 0,

      submitLoading: false,

      selectedList: [], // 选中的数据
      showPop: false
    };
  },

  computed: {},

  watch: {
    showPop: {
      immediate: true,
      handler(val) {
        if (val) {
          this.getList();
          this.selectedList = this._.cloneDeep(this.product_list);
        } else {
          this.selectedList = [];
        }
      }
    }
  },

  created() {},

  mounted() {},

  methods: {
    onCancel() {
      this.showPop = false;
      this.queryFormData = { ...init_query_form_data };
    },
    onShow() {
      this.showPop = true;
    },
    /* 产品表格相关事件 */
    // 表格的选中
    selectAll(val) {
      val &&
        val.forEach((item, index) => {
          let _selectIndex = this.getIndex('selectedList', item);
          if (_selectIndex == -1) {
            this.$set(this.list, index, { ...item, _checked: true });
            this.selectedList.push(item);
          }
        });
    },
    select(val, row) {
      let _listIndex = this.getIndex('list', row);
      this.$set(this.list, _listIndex, { ...row, _checked: true });
      this.selectedList.push(row);
    },
    selectAllCancel(val) {
      this.list &&
        this.list.forEach((item, index) => {
          let _selectIndex = this.getIndex('selectedList', item);
          if (_selectIndex >= 0) {
            this.$set(this.list, index, { ...item, _checked: false });
            this.selectedList.splice(_selectIndex, 1);
          }
        });
    },
    selectCancel(val, row) {
      let _selectIndex = this.getIndex('selectedList', row);
      let _listIndex = this.getIndex('list', row);
      this.$set(this.list, _listIndex, { ...row, _checked: false });
      this.selectedList.splice(_selectIndex, 1);
    },

    getIndex(key, row) {
      let _index = -1;
      this[key].some((item, index) => {
        if (item.id == row.id) {
          _index = index;
        }
      });
      return _index;
    },

    // 根据id回显勾选的商品
    echoSelectedProduct() {
      this.list.forEach((item, index) => {
        this.selectedList.forEach(selected_item => {
          if (item.id == selected_item.id) {
            this.$set(this.list, index, { ...item, _checked: true });
          }
        });
      });
    },
    onConfirm(val) {
      this.$emit('selectedList', this.selectedList);
      this.onCancel();
    },

    handleSizeChange(val) {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.queryFormData.page = val;
      this.getList();
    },

    onSearch() {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = 20;
      this.getList();
    },

    // api-获取商品列表
    getList() {
      this.tableLoading = true;
      let params = {};
      if (this.warehouse_code !== '') {
        params = {
          ...this.queryFormData,
          warehouse_code: this.warehouse_code
        };
      } else {
        params = {
          ...this.queryFormData,
          supplier_code: this.supplier_code,
          status: this.status
        };
      }
      this.$api[this.apiName](params)
        .then(res => {
          this.tableLoading = false;
          this.list = res.list;
          this.total = res.total;
          this.echoSelectedProduct();
        })
        .catch(err => {
          this.tableLoading = false;
        });
    }
  },

  destroyed() {}
};
</script>

<style scoped lang="less">
:deep(.el-popover__reference-wrapper) {
  width: 100%;
}

.popover-content-box {
  .header-box {
    .title {
      font-weight: 600;
      line-height: 50px;
      padding: 0 20px;
      font-size: 16px;
      border-bottom: 1px solid rgb(223, 225, 230);
    }

    .search-box {
      display: flex;
      align-items: center;
      padding: 10px 20px;

      .el-input {
        width: 200px;
      }

      .el-button {
        margin-left: 10px;
      }
    }
  }

  .bottom-btn-wrapper {
    height: 50px;
    display: flex;
    justify-content: flex-end;
    padding: 10px 20px;
    border-top: 1px solid rgb(223, 225, 230);
    margin-top: 20px;
  }
}
</style>
<style lang="less">
.pop-common {
  padding: 0 !important;
}
</style>
