<template>
  <div class="flex" style="margin-top: 1px">
    <Select
      v-if="showType"
      ref="surningType"
      v-model="ent_type"
      style="width: 80px"
      @on-change="changeEntType"
      placeholder="全部"
      clearable
    >
      <Option value="CLI">诊所</Option>
      <Option value="RXJ">养疗馆</Option>
      <Option value="COM">省公司</Option>
      <Option value="OUTSIDE" v-if="showOutside">外部公司</Option>
    </Select>
    <Select
      ref="surning"
      transfer
      :value="value"
      :clearable="isClearable"
      :loading="searchLoading"
      :remote-method="search"
      filterable
      @on-clear="clearSub"
      @on-query-change="queryChange"
      class="filterable-select"
      :placeholder="placeholder"
      @on-select="selectSup"
    >
      <!--      <Option value="" v-show="showAll">全部</Option>-->
      <Option v-for="(option, index) in surning_list" :key="index" :value="option.ent_code" :label="option.ent_name">
        <span>{{ option.ent_name }}</span>
        <span class="disabled-tag" v-if="option.is_disabled === '1'">已禁用</span>
      </Option>
    </Select>
  </div>
</template>

<script>
import util from '@/utils/util';
export default {
  name: 'surning-search',
  components: {},
  mixins: [],
  props: {
    isClearable: {
      type: Boolean,
      default: true,
    },
    value: {
      type: String,
      default: '',
    },
    showAll: {
      type: Boolean,
      default: true,
    },
    type: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: '请输入搜索采购主体',
    },
    company_id: {
      type: String,
      default: '',
    },
    // 展示采购主体类型，目前只开放采购单，后续全部放开可移除该属性
    showType: {
      type: Boolean,
      default: false,
    },
    showOutside: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      searchLoading: false,
      surning_list: [],
      query: '',
      ent_type: '',
    };
  },
  computed: {},
  watch: {
    company_id: {
      handler(val) {
        this.com_id = val;
        console.log('123', val);
        this.searchMethod();
      },
    },
  },
  created() {
    if (!this.$route.query.ent_code && !this.$route.query.clinic_id) {
      // 12.27 兼容诊所搜索
      this.searchMethod();
    } else {
      let list = JSON.parse(localStorage.getItem('surning_list')) || [];
      this.surning_list = list;
      this.$emit('input', list[0] && list[0].ent_code);
      this.$emit('getType', list[0] && list[0].ent_type);
    }
  },
  mounted() {},
  methods: {
    searchMethod: util.debounce(function (query) {
      console.log('query', query);
      this.searchLoading = true;
      let params = {
        name: query || '',
        ent_type: this.ent_type || this.type,
        company_id: this.company_id,
        fill_outside: this.showOutside ? 1 : '',
      };
      this.$api.searchPurchasesubject(params).then(res => {
        this.searchLoading = false;
        this.surning_list = res.list;
        localStorage.setItem('surning_list', JSON.stringify(this.surning_list));
      });
    }, 200),
    search() {},
    selectSup(val) {
      let type = '';
      this.surning_list.some(item => {
        if (item.ent_code == val.value) {
          type = item.ent_type;
        }
      });
      // console.log("-> %c type  === %o ", "font-size: 24px;color:#67C23A ", type)
      this.$emit('input', val.value);
      this.$emit('getType', this.ent_type || type);
    },
    queryChange(val) {
      this.searchMethod(val);
    },
    clear() {
      this.$refs['surning'].clearSingleSelect();
      this.ent_type = '';
    },
    clearSub() {
      this.searchMethod();
      this.$emit('input', '');
      this.$emit('getType', '');
    },
    changeEntType() {
      this.$emit('input', '');
      this.$emit('getType', this.ent_type);
      this.searchMethod();
    },
    clearEntType() {
      this.ent_type = '';
    },
  },
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
.disabled-tag {
  color: #ccc;
  margin-left: 10px;
}
</style>
