<template>
  <div class="KErrorReport-wrapper">
    <Modal
      :value="reportVisible"
      :mask-closable="false"
      width="800"
      @on-visible-change="handleVisibleChange"
      title="错误报告"
    >
      <div class="report-content">
        <Table :columns="reportColumns" :data="reportList" height="400"></Table>
      </div>
      <div slot="footer">
        <Button @click="handleVisibleChange(false)">关闭</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
export default {
  name: 'KErrorReport',
  mixins: [],

  components: {},

  props: {
    reportVisible: {
      type: Boolean,
      default: false
    },
    reportColumns: {
      type: Array,
      default: () => []
    },
    reportList: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {};
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  methods: {
    handleVisibleChange(visible) {
      !visible && this.$emit('update:report-visible', false);
    }
  },

  destroyed() {}
};
</script>

<style scoped lang="less"></style>
