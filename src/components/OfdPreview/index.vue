<template>
  <Modal
    :value="visible"
    :title="title"
    width="630px"
    :closable="false"
    class-name="confirm-modal"
    transfer
    :mask-closable="false"
    @on-visible-change="changeVisible"
  >
    <div id="ofd-container" class="content"></div>
    <div slot="footer">
      <Button @click="cancel">关闭</Button>
    </div>
  </Modal>
</template>

<script>
import { parseOfdDocument, renderOfd } from '@/utils/ofd/ofd';
import axios from 'axios';

export default {
  name: 'OfdPreview',

  components: {},
  mixins: [],
  model: {
    prop: 'visible',
    event: 'update:visible',
  },
  props: {
    title: {
      type: String,
      default: 'OFD文件预览',
    },
    visible: {
      type: Boolean,
      default: false,
    },
    url: {
      type: String,
      default: '',
    },
  },

  data() {
    return {};
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    cancel() {
      this.changeVisible(false);
    },
    changeVisible(val) {
      !val && this.$emit('update:visible', val);
      if (val) {
        this.renderOfd();
      }
    },
    renderOfd() {
      // URL指向你需要转换为File对象的资源

      // 使用fetch API从URL获取资源
      // axios.get(this.url, { responseType: 'blob' }).then(response => {
      //   const file = new File([response.data], 'ofd.ofd', { type: 'application/octet-stream' });
      // this.parseOfd(file);
      parseOfdDocument({
        ofd: this.url, //file 可以为文件链接、上传的文件对象
        success(res) {
          console.log('%c=>(index.vue:74) res', 'font-size: 18px;color: #FF7043 ;', res);
          //输出ofd每页的div
          const divs = renderOfd(500, res[0]);
          const container = document.getElementById('ofd-container');
          container.innerHTML = '';
          for (const div of divs) {
            container.appendChild(div);
          }
        },
        fail(error) {
          console.log(error);
        },
      });
      // });
    },
  },
};
</script>

<style scoped lang="less">
.confirm-modal {
  .content {
    padding: 0px;
  }
}

::v-deep .ivu-modal-footer {
  border-top: none;
}

.content-text {
  padding-left: 52px;
}

.ofd-container {
  height: 400px;
  width: 600px;
}
</style>
