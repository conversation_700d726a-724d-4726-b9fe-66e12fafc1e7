<template>
  <el-select v-model="productCode" placeholder="请输入商品名称"
             filterable
             clearable
             reserve-keyword
             remote
             :loading="loading"
             :remote-method="searchGoods"
             @change="changeGoods"
             size="small">
    <el-option v-for="item in goodsList" :value="item.code" :key="item.code" :label="item.name"></el-option>
  </el-select>
</template>

<script>
export default {
  name: 'GoodsSearch',
  model: {
    prop: 'product_code'
  },
  props: {
    product_code: {
      type: String,
      default: ''
    },
    onSearch: {
      type: Function,
    }
  },
  data() {
    return {
      goodsList: [],
      productCode: '',
      loading: false
    }
  },
  created() {
    const code = this.product_code||''
    this.searchGoods(code,true);
  },
  methods: {
    async searchGoods(query,isCode = false) {
      this.loading = true
      const params = {
        q: query,
        page: 1,
        page_size: 20
      }
      const res = await this.$api.getErpProductList(params)
      this.goodsList = res.list
      if(isCode){
        this.productCode = query
      }
      this.loading = false
    },
    changeGoods(code) {
      console.log('%c=>(index.vue:44) code', 'font-size: 18px;color: #FF7043 ;', code);
      this.$emit('input', code)
      // if(!code){
      //   this.searchGoods('')
      // }
      this.onSearch()
    }
  },
  watch: {
    product_code(newValue, oldValue) {
      this.searchGoods(newValue,true)
    }
  },
}
</script>

<style lang="less" scoped>

</style>