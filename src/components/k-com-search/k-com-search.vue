<template>
  <Select
    ref="company"
    transfer
    :value="value"
    :clearable="isClearable"
    :loading="searchLoading"
    :remote-method="search"
    filterable
    :transfer-class-name="className"
    @on-query-change="queryChange"
    class="filterable-select"
    placeholder="请输入搜索省公司"
    @on-clear="clearSub"
    @on-select="selectSup"
  >
    <Option v-for="(option, index) in com_list" :key="option.id" :value="option.id">{{ option.name }}</Option>
  </Select>
  <!-- <Select :value="value" style="width:200px" @on-select="selectSup" @on-clear="clearSub" :clearable="isClearable" placeholder="请选择供应商">
		<Option v-for="(option, index) in supplier_list"  :key="option.id" :value="option.id">{{ option.name }}</Option>
	</Select> -->
</template>

<script>
import util from '@/utils/util';
export default {
  name: 'k-clinic-search',
  components: {},
  mixins: [],
  props: {
    isClearable: {
      type: Boolean,
      default: true
    },
    value: {
      type: String,
      default: ''
    },
    className: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      searchLoading: false,
      // supplierList: [],
      com_list: [],
      query: '',
      shouldSearch: true
    };
  },
  computed: {},
  watch: {
    value(val) {
      !val && this.searchMethod('');
    }
  },
  created() {},
  mounted() {
    if (!this.$route.query.company_id) {
      this.searchMethod();
    } else {
      console.log('nidksajd');
      this.shouldSearch = false;
      let list = JSON.parse(sessionStorage.getItem('com_list')) || [];
      this.com_list = list;
      console.log('-> %c this.com_list  === %o', 'font-size: 15px;color: green;', this.com_list);
      // this.$emit('input', list[0] && list[0].id)
    }
  },
  methods: {
    searchMethod: util.debounce(function (query) {
      console.log('-> %c query  === %o', 'font-size: 15px;color: green;', query);
      if (!this.shouldSearch) {
        this.shouldSearch = true;
        return;
      }
      this.searchLoading = true;
      this.$api.getComList({ name: query }).then(res => {
        this.searchLoading = false;
        this.com_list = res.list;
        sessionStorage.setItem('com_list', JSON.stringify(this.com_list));
      });
    }, 200),
    search() {},
    clear() {
      this.$refs.company.clearSingleSelect();
    },
    selectSup(val) {
      this.$emit('input', val.value);
      this.$emit('clickItem', val.value);
    },
    queryChange(val) {
      this.searchMethod(val);
    },
    clearSub() {
      this.searchMethod();
      this.$emit('input', '');
      this.$emit('clickItem', '');
    }
  }
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
</style>
