<template>
  <!--<Select-->
  <!--			ref="company"-->
  <!--			transfer-->
  <!--			:value="value"-->
  <!--			:clearable="isClearable"-->
  <!--			:loading="searchLoading"-->
  <!--			:remote-method="search"-->
  <!--			filterable-->
  <!--      :transfer-class-name="className"-->
  <!--      transfer-->
  <!--			@on-clear="clearSub"-->
  <!--			@on-query-change="queryChange"-->
  <!--			class="filterable-select"-->
  <!--			placeholder="请输入搜索省公司"-->
  <!--			@on-select="selectSup">-->
  <!--		<Option v-for="(option, index) in com_list" :key="option.id" :value="option.id">{{ option.name }}</Option>-->
  <!--	</Select>-->
  <el-select
    :value="value"
    size="small"
    filterable
    remote
    reserve-keyword
    class="com-search"
    placeholder="请输入搜索省公司"
    @change="selectSup"
    @clear="clearSub"
    :clearable="isClearable"
    :remote-method="searchMethod"
    :loading="searchLoading"
  >
    <el-option label="全部省公司" value="" v-if="!showAll"></el-option>
    <el-option v-for="item in com_list" :key="item.id" :label="item.name" :value="item.id"> </el-option>
  </el-select>
  <!-- <Select :value="value" style="width:200px" @on-select="selectSup" @on-clear="clearSub" :clearable="isClearable" placeholder="请选择供应商">
		<Option v-for="(option, index) in supplier_list"  :key="option.id" :value="option.id">{{ option.name }}</Option>
	</Select> -->
</template>

<script>
import util from '@/utils/util';
export default {
  name: 'k-clinic-search',
  components: {},
  mixins: [],
  props: {
    isClearable: {
      type: Boolean,
      default: true,
    },
    value: {
      type: String,
      default: '',
    },
    className: {
      type: String,
      default: '',
    },
    showAll: {
      type: Boolean,
      default: false,
    },
    disablePagination: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      searchLoading: false,
      // supplierList: [],
      com_list: [],
      query: '',
    };
  },
  computed: {},
  watch: {},
  created() {
    if (!this.$route.query.company_id) {
      console.log("=>(k-com-search_data.vue:82) '无id", '无id');
      this.searchMethod();
    } else {
      console.log("=>(k-com-search_data.vue:86) '获取本地数据", '获取本地数据');
      let list = JSON.parse(localStorage.getItem('com_list')) || [];
      console.log(
        "=>(k-com-search_data.vue:86) JSON.parse(localStorage.getItem('com_list'))",
        JSON.parse(localStorage.getItem('com_list'))
      );
      this.com_list = list;
      console.log('=>(k-com-search_data.vue:87) this.com_list', this.com_list);
      this.$emit('input', list[0] && list[0].id);
    }
  },
  mounted() {},
  methods: {
    searchMethod: util.debounce(function (query) {
      this.searchLoading = true;
      let params = {
        name: query,
      };
      if (this.disablePagination) {
        params.page_type = '1';
      }
      console.log('-> %c this.disablePagination  === %o', 'font-size: 15px;color: green;', this.disablePagination);

      this.$api.getComList(params).then(res => {
        this.searchLoading = false;
        this.com_list = res.list;
        console.log('=>(k-com-search_data.vue:99) this.com_list1111111111111', this.com_list);
        localStorage.setItem('com_list', JSON.stringify(this.com_list));
      });
    }, 200),
    selectSup(val) {
      // this.searchMethod('')
      console.log('-> val', val);
      this.$emit('input', val);
      this.$emit('clickItem', val);
    },
    queryChange(val) {
      this.searchMethod(val);
    },
    clearSub() {
      this.searchMethod('');
      this.$emit('input', '');
      this.$emit('clickItem', '');
    },
  },
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
.com-search {
  width: 180px;
}
</style>
