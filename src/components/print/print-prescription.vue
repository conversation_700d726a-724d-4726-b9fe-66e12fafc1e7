<template>
  <div ref="printeMe" style="height: 100%">
    <div class="print-wrapper" v-if="prescriptionType.includes('HERBS')">
      <div class="print-content">
        <div class="print-head">
          <div class="print-title">
            <h1>{{ clinic.name }}{{ herbs.prescriptionName }}处方单</h1>
          </div>
          <div class="print-number">
            <span>{{ herbs.prescriptionName }}处方单号：{{ herbs.pres_code }}</span>
            <span>开方日期：{{ herbs.create_time | date_format('YYYY.MM.DD') }}</span>
          </div>
          <div class="user-info">
            <div class="user-info-item user-info-item-1">
              <span>姓名：{{ patientInfo.patient.name }}</span>
              <span>性别：{{ patientInfo.patient.sex === '1' ? '男' : '女' }}</span>
              <span>年龄：{{ patientInfo.age_text }}</span>
              <span>电话：{{ patientInfo.patient.mobile }}</span>
            </div>
            <div class="user-info-item">
              <span style="line-height: 28px"> 病症：{{ userInfo.disease }} </span>
            </div>
            <!--          <div class="user-info-item">-->
            <!--            <span>-->
            <!--              辨证：阴阳、虚实、表里、寒热、表里、寒热-->
            <!--            </span>-->
            <!--          </div>-->
          </div>
        </div>

        <div class="medicine-list">
          <h2>Rp.</h2>
          <div class="medicine-item">
            <span v-for="item in herbs.attrs" :key="item.id"
              >{{ item.medicine_info.generic_name }} {{ item.medicine_info.unit_num
              }}{{ item.medicine_info.unit_name }}</span
            >
          </div>
          <div class="medicine-tips-bold">贴数/用法：共{{ herbs.herbs_info.total }}贴 {{ herbs.usage_text }}</div>
        </div>
      </div>
      <div class="print-footer">
        <div class="total">
          <span> 合计金额：￥{{ herbs.total_fee }} </span>
        </div>
        <div class="physician">
          <span>医生：{{ doctorName }}</span>
          <span>发药人：{{ doctorName }}</span>
        </div>
      </div>
    </div>
    <div class="print-wrapper" style="page-break-before: always" v-for="(item, index) in medicineList" :key="item.id">
      <div class="print-content">
        <div>
          <div class="print-title">
            <h1>{{ clinic.name }}{{ medicine.prescriptionName }}处方单</h1>
          </div>
          <div class="print-number">
            <span>{{ medicine.prescriptionName }}处方单号：{{ medicine.pres_code }}</span>
            <span>开方日期：{{ medicine.create_time | date_format('YYYY.MM.DD') }}</span>
          </div>
          <div class="user-info">
            <div class="user-info-item user-info-item-1">
              <span>姓名：{{ patientInfo.patient.name }}</span>
              <span>性别：{{ patientInfo.patient.sex === '1' ? '男' : '女' }}</span>
              <span>年龄：{{ patientInfo.age_text }}</span>
              <span>电话：{{ patientInfo.patient.mobile }}</span>
            </div>
            <div class="user-info-item">
              <span> 病症：{{ userInfo.disease }} </span>
            </div>
            <!--          <div class="user-info-item">-->
            <!--            <span>-->
            <!--              辨证：阴阳、虚实、表里、寒热、表里、寒热-->
            <!--            </span>-->
            <!--          </div>-->
          </div>
        </div>

        <div class="medicine-list">
          <h2>Rp.</h2>
          <div class="medicine-table">
            <table cellspacing="0" border="0">
              <tr class="table-head">
                <td class="td-text-left td-indent">药品名/规格</td>
                <td>单次剂量</td>
                <td>数量</td>
                <td>金额(元)</td>
              </tr>
              <tr v-for="subItem in item.medItem" :key="subItem.id">
                <td class="td-tr">
                  <div class="td-1">
                    <span style="font-size: 18px">
                      {{ `(${subItem.sequence})` }} {{ subItem.medicine_info.generic_name }}
                    </span>
                    <span class="usage-bold">
                      {{ `用法${subItem.usage_text}` }}
                    </span>
                  </div>
                </td>
                <td>{{ subItem.medicine_info.unit_num }}{{ subItem.medicine_info.unit_name }}</td>
                <td>{{ subItem.quantity_text }}</td>
                <td>{{ subItem.payment_fee }}</td>
              </tr>
            </table>
          </div>
        </div>
      </div>
      <div class="print-footer">
        <div class="total">
          <span> 合计金额：￥{{ medicine.total_fee }} </span>
        </div>
        <div class="physician">
          <span>医生：{{ doctorName }}</span>
          <span>发药人：{{ doctorName }}</span>
        </div>
        <div class="page">
          <span> {{ `第${index + 1}\/${medicineList.length}页 ` }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import S from 'utils/util';

export default {
  name: 'Print',
  data() {
    return {
      userInfo: {},
      doctorName: '',
      clinic: {},
      typeDesc: {},
      prescriptionType: [],
      herbs: {},
      medicine: {},
      medicineList: [],
      patientInfo: {}
    };
  },
  mounted() {},
  methods: {
    printBill(mr_id) {
      let query;
      if (mr_id) {
        query = { mr_id };
      } else {
        query = { mr_id: this.$route.query.id };
      }
      console.log('-> query', query);
      this.$api.getDispensingDetails(query).then(res => {
        this.doctorName = res.doctor_name;
        this.userInfo = res.medical_record.patient;
        let arr = [];
        for (let resKey of res.medical_record.diag_result) {
          arr.push(resKey.name);
        }
        this.userInfo.disease = arr.join(' 、 ');
        this.typeDesc = res.typeDesc;
        this.clinic = res.clinic; //诊所信息
        // this.clinic.create_time = S.moment(this.clinic.create_time*1000).format('YYYY.MM.DD')
        this.patientInfo = res.medical_record;
        res.pres_items.map(item => {
          item.prescriptionName = res.typeDesc[item.type].desc;
          item.total_fee = S.toDecimal(item.total_fee, 2);
          item.attrs = Object.values(item.attrs);
          this.prescriptionType.push(item.type);
          if (item.type === 'MEDICINE') {
            item.attrs.map((item1, index) => {
              console.log(item1);
              item1.sequence = index + 1;
            });
            this.medicineList = [];
            this.chunkArr(item.attrs, 5).map((item, index) => {
              console.log('-> item', item);
              this.medicineList.push({ id: index, medItem: item });
            });
            console.log(this.medicineList);
            this.medicine = item;
          } else if (item.type === 'HERBS') {
            this.herbs = item;
          }
        });
        console.warn(res);
        setTimeout(() => {
          this.$print(this.$refs.printeMe, {}, this.beforePrint, this.afterPrint);
        }, 100);
      });
    },
    chunkArr(arr, size) {
      //判断如果不是数组(就没有length)，或者size没有传值，size小于1，就返回空数组
      if (!arr.length || !size || size < 1) return [];
      let [start, end, result] = [null, null, []];
      console.log(Math.ceil(arr.length / size));
      for (let i = 0; i < Math.ceil(arr.length / size); i++) {
        start = i * size;
        end = start + size;
        result.push(arr.slice(start, end));
      }
      return result;
    },

    beforePrint() {
      console.log('打印前');
    },
    afterPrint() {}
  }
};
</script>
<style scoped lang="less">
@media print {
  body {
    margin: 1cm;
  }
  .is-split {
    page-break-after: always;
  }
  @page {
    margin: 0;
    size: A4 portrait; // A4大小 纵向
  }
}

.print-wrapper {
  width: 100%;
  margin-left: -8px;
  font-family: STSong, SimSun;
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: space-between;
  padding: 100px 100px 66px;

  .print-title {
    text-align: center;

    > h1 {
      font-size: 32px;
      font-weight: bold;
      color: #000000;
      letter-spacing: 3px;
      margin-bottom: 40px;
    }
  }

  .print-number {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding-bottom: 16px;
    padding-top: 20px;

    > span {
      font-size: 18px;
      font-weight: 400;
      color: #000000;
    }
  }

  .user-info {
    border-bottom: 1px solid #777777;
    border-top: 1px solid #777777;
    width: 100%;
    padding-top: 16px;

    .user-info-item {
      margin-bottom: 12px;
      display: flex;
      justify-content: space-between;

      > span {
        line-height: 28px;
        font-size: 18px;
        font-weight: 400;
        color: #000000;
      }
    }
    .user-info-item-1 {
      margin-bottom: 24px;
    }
  }

  .medicine-list {
    margin-top: 28px;

    > h2 {
      font-size: 32px;
      color: #000000;
      line-height: 31px;
      margin-bottom: 28px;
    }

    .medicine-item {
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;

      > span {
        width: 25%;
        margin-bottom: 28px;
        font-size: 18px;
        font-weight: 400;
        color: #000000;
      }
    }

    .medicine-tips-bold {
      font-size: 18px;
      font-weight: 900;
      color: #000000;
      margin-top: 28px;
    }
  }

  .print-footer {
    border-top: 1px solid #777777;

    .total {
      padding: 22px 0;
    }

    span {
      font-size: 18px;
      font-weight: 400;
      color: #000000;
    }

    .physician {
      display: flex;
      justify-content: space-between;
    }
  }
  .page {
    text-align: center;
    color: #000000;
    margin-top: 20px;
  }
}
.medicine-table {
  width: 100%;
  table {
    width: 100%;
    .td-bold {
      font-weight: 900;
    }
    .table-head {
      td {
        font-size: 18px;
        color: #000000;
      }
    }
  }
  tr {
    line-height: 28px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    td {
      text-align: right;
      font-size: 18px;
      color: #000000;
      min-width: 70px;
      .usage-bold {
        font-weight: 900;
        font-size: 18px;
        color: #000000;
        text-indent: 16px;
        width: 100%;
      }
    }
    .td-1 {
      display: flex;
      flex-direction: column;
    }
    .td-text-left {
      text-align: left;
      text-indent: 16px;
      width: 58%;
    }
    .td-tr {
      text-align: left;
      width: 58%;
    }
  }
}
</style>
