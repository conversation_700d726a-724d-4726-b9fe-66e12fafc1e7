<!--  -->
<template>
  <div class="print-wrapper" ref="bills">
    <div v-for="(item, index) in sendGoodsList" :key="index" class="print-loop">
      <div>
        <!-- logo -->
        <div class="print-logo">
          <img src="@/assets/image/rxj_logo.png" alt="榕小家logo" style="height: 54px" v-if="getPurMainType" />
          <img src="@/assets/image/logo.png" alt="榕树家logo" v-else />
        </div>
        <!-- title -->
        <div class="print-title">
          {{ getPurchaseText ? '首次产品' : '二次采购' }}{{ printType == 1 ? '发货' : '进货' }}单
        </div>

        <!-- title-info -->
        <div class="print-title-info">
          <div class="flex-between">
            <p class="font-18">订单编号：{{ code }}</p>
            <p class="font-18">订单日期：{{ time_text }}</p>
          </div>
          <hr />
          <div class="flex-between">
            <p class="font-18">所属省公司：{{ company_name || '-' }}</p>
            <p class="font-18">采购主体：{{ ent_name }}</p>
          </div>
          <div class="flex-between">
            <p class="font-18">收货地址：{{ address || '-' }}</p>
            <p class="font-18">
              联系人/电话：<span>{{ consignee }}</span
              ><span v-if="mobile">/{{ mobile }}</span
              ><span v-else>-</span>
            </p>
          </div>
        </div>
      </div>

      <div class="print-content">
        <!-- 发货清单 -->
        <div class="send-list" v-if="item[0].length">
          <p class="title font-19">发货清单</p>
          <table border="1px" cellspacing="0">
            <thead>
              <tr>
                <!-- <th >商品编号</th> -->
                <th>商品</th>
                <th>终端销售单价</th>
                <th v-if="showPrice === '1'">单价</th>
                <th>发货数量</th>
                <th v-if="showPrice === '1'">发货总价</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(subItem, index) in item[0]" :key="'goods' + index">
                <!-- <td>{{ subItem.is_plaster_gift !== '1' ? subItem.spu_code : '-' }}</td> -->
                <td>{{ subItem.name }}</td>
                <td>{{ subItem.proposal_price ? `￥${subItem.proposal_price}` : '-' }}</td>
                <td v-if="showPrice === '1'">
                  ￥{{ printType == 1 ? subItem.single_cli_price : subItem.single_com_price }}
                </td>
                <td>{{ subItem.num }}</td>
                <td v-if="showPrice === '1'">￥{{ printType == 1 ? subItem.cli_price : subItem.com_price }}</td>
              </tr>
              <tr v-if="isShowTotal === index">
                <td :colspan="showPrice === '1' ? 3 : 2" class="font-w19">总计</td>
                <td class="font-w19">{{ totalNum }}</td>
                <td class="font-w19" v-if="showPrice === '1'">
                  ￥{{ printType == 1 ? totalCliMoney : totalComMoney }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <!-- 发货物流 -->
        <div class="send-logistics" v-if="item[1].length">
          <p class="title font-19">发货物流</p>
          <table border="1px" cellspacing="0">
            <thead>
              <tr>
                <th style="width: 33.33%">快递公司</th>
                <th style="width: 66.66%">单号</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(subItem, ind) in item[1]" :key="'sub' + ind">
                <td>{{ subItem.express_company_text }}</td>
                <td>{{ subItem.express_no }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 出库专用章 -->
      <div class="companySeal">
        <img src="@/assets/image/companySeal.png" alt="" />
      </div>

      <div class="print-footer">
        <div class="flex-between page-footer">
          <!-- <p class="font-19">制单：{{ make }}</p>
          <p class="font-19">审单：{{ audit }}</p>
          <p class="font-19">发货：{{ ship }}</p> -->
        </div>
        <p class="flex-center" v-if="sendGoodsList.length > 1">第{{ index + 1 }}页,共{{ sendGoodsList.length }}页</p>
      </div>
    </div>
  </div>
</template>

<script type="text/javascript">
import util from '@/utils/util';

export default {
  name: '',
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      // a[[[],[]]]
      // sendGoodsList: [[36], [5,28],[0,35]], // 发货清单 36
      // sendGoodsList: [], // 发货清单 36
      list: [], // 发货清单
      sendGoodsLogistics: [], // 发货物流
      code: '', // 订单编号
      time_text: '', // 订单日期
      company_name: '', // 所属省公司
      ent_name: '', // 诊所名称
      address: '', // 收货地址
      consignee: '', // 联系人
      mobile: '', // 电话
      printType: '', // 1：诊所发货单， 2：省公司进货单
      make: '',
      audit: '',
      ship: '',
      totalNum: 0, // 总数
      totalCliMoney: 0, // 总金额
      totalComMoney: 0, //
      isShowTotal: '', // 发货清单是否展示总计
      is_addition: '', //是否二次发货
      showPrice: '1',
      purchaseType: ''
    };
  },
  computed: {
    getPurchaseText() {
      return this.purchaseType.includes('OPENING');
    },
    getPurMainType() {
      return this.purchaseType.includes('RXJ');
    },
    sendGoodsList() {
      let size = 36;
      let result = []; // 最终返回的数据
      let tempList = []; // 发货清单的所有数据
      let tempLogisticsList = []; // 发货物流的所有数据

      // 以size为基础，切割发货的数据
      console.log('this.list', this.list);
      for (let i = 0; i < Math.ceil(this.list.length / size); i++) {
        let start = i * size;
        let end = (i + 1) * size;
        tempList.push(this.list.slice(start, end));
      }

      console.log('tempList', tempList);

      // 设置展示总计的标识
      this.isShowTotal = tempList.length - 1;

      // 计算发货树最后一个数据有多少位，标记为centerNum
      let centerNum = 0;
      if (!tempList.length) {
        centerNum = 0;
      } else {
        centerNum = tempList[tempList.length - 1].length;
      }

      // 切割发货物流,根据发货清单最后一位的数据长度动态切割发货物流的第一个数据长度
      for (let i = 0; i < Math.ceil(this.sendGoodsLogistics.length / size); i++) {
        if (i === 0) {
          let start = 0;
          let end = size - centerNum === 0 ? size : size - centerNum;
          tempLogisticsList.push(this.sendGoodsLogistics.slice(start, end));
        } else {
          let start = i * size - centerNum;
          let end = (i + 1) * size - centerNum;
          tempLogisticsList.push(this.sendGoodsLogistics.slice(start, end));
        }
      }

      // 将发货清单装载进最终数据
      tempList.map(item => {
        result.push([item, []]);
      });

      // 将发货物流装载进最终数据,首位的数组的装载需要依据发货清单末尾空余多少位进行装载,
      // 发货清单末尾数组的长度加上发货物流首位数组的长度等于size

      if (tempLogisticsList.length) {
        if (result[tempList.length - 1][0].length !== size) {
          // 说明此处需要拼接
          result[tempList.length - 1][1] = result[tempList.length - 1][1].concat(tempLogisticsList[0]);
          tempLogisticsList.map((item, index) => {
            if (index > 0) {
              result.push([[], item]);
            }
          });
        } else {
          tempLogisticsList.map((item, index) => {
            result.push([[], item]);
          });
        }
      }
      console.log('result', result);
      return result;
    }
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    printBill(pur_code, val1, val2, printType, showPrice) {
      this.totalCliMoney = 0;
      this.totalComMoney = 0;
      this.totalNum = 0;
      this.printType = printType;
      this.showPrice = showPrice;
      this.list = this._.cloneDeep(val1);
      this.list.map(item => {
        this.totalNum = util.mathAdd(Number(this.totalNum), Number(item.num));
        this.totalCliMoney = util.mathAdd(Number(this.totalCliMoney), Number(item.cli_price));
        this.totalComMoney = util.mathAdd(Number(this.totalComMoney), Number(item.com_price));
      });
      this.sendGoodsLogistics = val2;
      let params = {
        pur_code
      };
      this.$api.purchasePrint(params).then(res => {
        console.log('-> %c res  === %o ', 'font-size: 15px', res);
        console.log('purchasePrint', res);
        let consignee_info = res.consignee_info;
        this.code = res.code;
        this.time_text = res.time_text;
        this.company_name = res.company_name;
        this.ent_name = res.ent_name;
        if (!Array.isArray(consignee_info) && !util.isEmptyObject(consignee_info)) {
          this.address = `${consignee_info.prov.name}${consignee_info.city.name}${consignee_info.county.name}${consignee_info.detail}`;
          this.consignee = consignee_info.consignee;
          this.mobile = consignee_info.mobile;
        }
        this.ship_order_list = res.ship_order_list;
        this.make = res.op_info.make;
        this.audit = res.op_info.audit;
        this.ship = res.op_info.ship;
        this.is_addition = res.is_addition;
        this.purchaseType = res.type;
        setTimeout(() => {
          this.$print(this.$refs.bills, {}, this.beforePrint, this.afterPrint);
        }, 100);
      });
    },
    beforePrint() {},
    afterPrint() {}
  },
  filters: {}
};
</script>
<style lang="less" scoped>
@media print {
  body {
    margin: 1cm;
  }

  .is-split {
    page-break-after: always;
  }

  @page {
    size: A4 portrait; // A4大小 纵向
    // padding: 50px 100px;
    margin: 0px;
  }
}

table {
  text-align: center;
  margin-bottom: 20px;
  width: 100%;

  thead {
    background: #e4e4e4;

    th {
      font-weight: 800;
      font-size: 19px;
    }
  }

  thead,
  td {
    height: 28px;
    line-height: 28px;
    font-size: 18px;
    color: #000000;
    font-weight: normal;
  }
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-content: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
}

.page-cut {
  page-break-after: always;
}
</style>

<style lang="less" scoped>
.print-wrapper {
  width: 100%;
  margin-left: -8px;
  font-family: STSong, SimSun;
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: space-between;
  // padding: 100px 100px 0px;
  font-size: 18px;

  .print-loop {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 60px 100px 60px;
  }

  // logo
  .print-logo {
    //>img{
    //  width: 232px;
    //  height: 84px;
    //}
  }

  // title
  .print-title {
    font-size: 32px;
    font-weight: 900;
    line-height: 51px;
    color: #000;
    text-align: center;
  }

  // title-info
  .print-title-info {
    p {
      margin-bottom: 10px;
    }

    padding-top: 26px;

    hr {
      margin-bottom: 10px;
    }
  }

  .print-content {
    // flex: 1;
    height: 1300px;
    // m发货清单
    .send-list {
      margin-top: 30px;
    }

    // 发货物流
    .send-logistics {
      margin-top: 30px;
    }
  }

  // 打印底部
  .print-footer {
    width: 100%;
    // border-top: 1px #777777 solid;
    padding-top: 20px;

    .page-footer {
      width: 100%;
    }
  }
}

.font-18 {
  font-size: 18px;
  color: #000000;
}

.font-w19 {
  font-size: 19px;
  color: #000000;
  font-weight: 900 !important;
}

.font-w20 {
  font-size: 20px;
  color: #000000;
  font-weight: 900;
}

.title {
  padding-bottom: 10px;
}

.companySeal {
  position: fixed;
  bottom: 50px;
  right: 100px;
  background: transparent;
  z-index: 1000;

  img {
    aspect-ratio: 1.3/1;
    width: 300px;
    opacity: 1;
  }
}
</style>
