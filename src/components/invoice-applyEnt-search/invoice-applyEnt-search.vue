<template>
  <Select
    ref="applyEnt-search"
    transfer
    :value="value"
    :clearable="isClearable"
    :loading="searchLoading"
    :remote-method="search"
    filterable
    @on-clear="clearSub"
    @on-query-change="queryChange"
    class="filterable-select"
    :placeholder="placeholder"
    @on-select="selectSup"
  >
    <!--      <Option value="" v-show="showAll">全部</Option>-->
    <Option v-for="(option, index) in applyEnt_list" :key="index" :label="option.desc" :value="option.id">
      <span>{{ option.desc }}</span>
      <span class="disabled-tag" v-if="option.is_disabled === '1'">已禁用</span>
    </Option>
  </Select>
</template>

<script>
import S from '@/utils/util';
export default {
  name: 'apply-search',
  components: {},
  mixins: [],
  props: {
    isClearable: {
      type: Boolean,
      default: true
    },
    value: {
      type: String,
      default: ''
    },
    showAll: {
      type: <PERSON>olean,
      default: true
    },
    type: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '输入或选择申请方'
    }
  },
  data() {
    return {
      searchLoading: false,
      applyEnt_list: []
    };
  },
  computed: {},
  watch: {},
  created() {
    if (!this.$route.query.ent_id) {
      this.searchMethod();
    } else {
      let list = JSON.parse(localStorage.getItem('applyEnt_list')) || [];
      this.applyEnt_list = list;
      this.$emit('input', list[0] && list[0].id);
    }
  },
  mounted() {},
  methods: {
    searchMethod: S.debounce(function (query) {
      this.searchLoading = true;
      let params = {
        name: query || ''
      };
      this.$api.getEntList(params).then(res => {
        this.searchLoading = false;
        this.applyEnt_list = S.descToArrHandle(res.applicant, 'name');
        localStorage.setItem('applyEnt_list', JSON.stringify(this.applyEnt_list));
      });
    }, 200),
    search() {},
    selectSup(val) {
      this.$emit('input', val.value);
    },
    queryChange(val) {
      this.searchMethod(val);
    },
    clear() {
      this.$refs['applyEnt-search'].clearSingleSelect();
    },
    clearSub() {
      this.searchMethod();
      this.$emit('input', '');
    }
  }
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
.disabled-tag {
  color: #ccc;
  margin-left: 10px;
}
</style>
