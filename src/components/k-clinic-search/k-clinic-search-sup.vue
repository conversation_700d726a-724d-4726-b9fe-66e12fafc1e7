<template>
  <div class="flex" style="margin-top: 1px">
    <Select
      ref="cli-search"
      transfer
      :value="value"
      :clearable="isClearable"
      :loading="searchLoading"
      :remote-method="search"
      filterable
      @on-clear="clearSub"
      @on-query-change="queryChange"
      class="filterable-select"
      :placeholder="placeholder"
      @on-select="selectSup"
    >
      <!--      <Option value="" v-show="showAll">全部</Option>-->
      <Option v-for="(option, index) in cli_list" :key="index" :value="option.id">{{ option.name }}</Option>
    </Select>
  </div>
</template>

<script>
import util from '@/utils/util';
export default {
  name: 'cli-search',
  components: {},
  mixins: [],
  props: {
    isClearable: {
      type: Boolean,
      default: true
    },
    value: {
      type: String,
      default: ''
    },
    showAll: {
      type: Boolean,
      default: true
    },
    type: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: '请输入搜索采购主体'
    },
    company_id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      searchLoading: false,
      cli_list: [],
      query: ''
    };
  },
  computed: {},
  watch: {
    company_id: {
      handler(val) {
        this.com_id = val;
        console.log('123', val);
        this.searchMethod();
      }
    }
  },
  created() {
    if (!this.$route.query.ent_code && !this.$route.query.clinic_id) {
      // 12.27 兼容诊所搜索
      this.searchMethod();
    } else {
      let list = JSON.parse(localStorage.getItem('cli_list')) || [];
      this.cli_list = list;
      this.$emit('input', list[0] && list[0].ent_code);
    }
  },
  mounted() {},
  methods: {
    searchMethod: util.debounce(function (query) {
      console.log('query', query);
      this.searchLoading = true;
      let params = {
        name: query || '',
        company_id: this.company_id
      };
      this.$api.getCommonCliList(params).then(res => {
        this.searchLoading = false;
        this.cli_list = res.list;
        localStorage.setItem('cli_list', JSON.stringify(this.cli_list));
      });
    }, 200),
    search() {},
    selectSup(val) {
      // console.log("-> %c type  === %o ", "font-size: 24px;color:#67C23A ", type)
      this.$emit('input', val.value);
    },
    queryChange(val) {
      this.searchMethod(val);
    },
    clear() {
      this.$refs['cli-search'].clearSingleSelect();
    },
    clearSub() {
      this.searchMethod();
      this.$emit('input', '');
    },
    changeEntType() {
      this.$emit('input', '');
      this.searchMethod();
    }
  }
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
</style>
