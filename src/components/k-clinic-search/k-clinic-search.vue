<template>
  <div>
    <Select
      transfer
      :value="value"
      :clearable="isClearable"
      :loading="searchLoading"
      :remote-method="search"
      filterable
      :disabled="disable"
      :transfer-class-name="className"
      @on-clear="clearSub"
      @on-query-change="queryChange"
      class="filterable-select"
      placeholder="请输入搜索诊所"
      ref="clinic"
      @on-select="selectSup"
    >
      <Option value="" label="全部诊所">全部诊所</Option>
      <Option v-for="(option, index) in clinic_list" :key="option.id" :value="option.id">{{ option.name }}</Option>
    </Select>
    <!--    <el-select  placeholder="placeholder" size='small'>-->
    <!--      <el-option-->
    <!--          v-for="item in 4"-->
    <!--          :key="item.value"-->
    <!--          :label="item.label"-->
    <!--          :value="item.value">-->
    <!--      </el-option>-->
    <!--    </el-select>-->
  </div>

  <!-- <Select :value="value" style="width:200px" @on-select="selectSup" @on-clear="clearSub" :clearable="isClearable" placeholder="请选择供应商">
		<Option v-for="(option, index) in supplier_list"  :key="option.id" :value="option.id">{{ option.name }}</Option>
	</Select> -->
</template>

<script>
import util from '@/utils/util';
export default {
  name: 'k-clinic-search',
  components: {},
  mixins: [],
  props: {
    isClearable: {
      type: Boolean,
      default: true
    },
    value: {
      type: String,
      default: 'all'
    },
    disable: {
      type: Boolean,
      default: false
    },
    company_id: {
      type: String,
      default: ''
    },
    className: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      searchLoading: false,
      // supplierList: [],
      clinic_list: [],
      query: '',
      com_id: ''
    };
  },
  computed: {},
  watch: {
    company_id: {
      handler(val, oldVal) {
        this.com_id = val;
        this.searchMethod();
      }
    }
  },
  created() {
    if (!this.$route.query.clinic_id) {
      this.searchMethod();
    } else {
      let list = JSON.parse(localStorage.getItem('clinic_list')) || [];
      this.clinic_list = list;
      this.$emit('input', list[0] && list[0].id);
    }
  },
  mounted() {},
  methods: {
    searchMethod: util.debounce(function (query) {
      this.searchLoading = true;
      const params = { name: query, company_id: this.company_id, pageSize: 100 };
      this.$api.getCommonList(params).then(res => {
        this.searchLoading = false;
        this.clinic_list = res.list;
        localStorage.setItem('clinic_list', JSON.stringify(this.clinic_list));
      });
    }, 200),
    search() {},
    selectSup(val) {
      console.log('val', val);
      this.$emit('input', val.value);
      this.$emit('clickItem', val.value);
    },
    queryChange(val) {
      this.query = val;
      this.searchMethod(val);
    },
    clearSub() {
      this.searchMethod('');

      this.$emit('input', '');
      this.$emit('clickItem');
    }
  }
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
</style>
