<template>
  <div>
    <!--    <Select-->
    <!--        transfer-->
    <!--        :value="value"-->
    <!--        :clearable="isClearable"-->
    <!--        :loading="searchLoading"-->
    <!--        :remote-method="search"-->
    <!--        filterable-->
    <!--        :disabled="disable"-->
    <!--        :transfer-class-name="className"-->
    <!--        transfer-->
    <!--        @on-clear="clearSub"-->
    <!--        @on-query-change="queryChange"-->
    <!--        class="filterable-select"-->
    <!--        placeholder="请输入搜索诊所"-->
    <!--        ref='clinic'-->
    <!--        @on-select="selectSup">-->
    <!--      <Option value="" label="全部诊所">全部诊所</Option>-->
    <!--      <Option v-for="(option, index) in clinic_list" :key="option.id" :value="option.id">{{ option.name }}</Option>-->
    <!--    </Select>-->
    <el-select
      :value="value"
      size="small"
      filterable
      remote
      reserve-keyword
      class="com-search"
      :clearable="showAll"
      placeholder="请输入搜索诊所"
      :remote-method="searchMethod"
      @change="selectSup"
      :loading="searchLoading"
    >
      <el-option label="全部诊所" value="" v-if="!showAll"></el-option>

      <el-option v-for="item in clinic_list" :key="item.id" :label="item.name" :value="item.id">
        <span>{{ item.name }}</span>
        <span class="disabled-tag" v-if="item.is_disabled === '1'">已禁用</span>
      </el-option>
    </el-select>
  </div>

  <!-- <Select :value="value" style="width:200px" @on-select="selectSup" @on-clear="clearSub" :clearable="isClearable" placeholder="请选择供应商">
    <Option v-for="(option, index) in supplier_list"  :key="option.id" :value="option.id">{{ option.name }}</Option>
  </Select> -->
</template>

<script>
import util from '@/utils/util';

export default {
  name: 'k-clinic-search',
  components: {},
  mixins: [],
  props: {
    isClearable: {
      type: Boolean,
      default: true
    },
    value: {
      type: String,
      default: 'all'
    },
    disable: {
      type: Boolean,
      default: false
    },
    company_id: {
      type: String,
      default: ''
    },
    className: {
      type: String,
      default: ''
    },
    showAll: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      searchLoading: false,
      // supplierList: [],
      clinic_list: [],
      query: '',
      com_id: ''
    };
  },
  computed: {},
  watch: {
    company_id: {
      handler(val) {
        this.com_id = val;
        if (val) {
          this.searchMethod();
        } else {
          this.clinic_list = [];
        }
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    searchMethod: util.debounce(function (query) {
      this.searchLoading = true;
      const params = { name: query, company_id: this.company_id, pageSize: 100 };

      this.$api.getCommonList(params).then(res => {
        console.log('-> res', res);
        this.searchLoading = false;
        if (this.company_id) {
          this.clinic_list = res.list;
        } else {
          this.clinic_list = [];
        }
        localStorage.setItem('clinic_list', JSON.stringify(this.clinic_list));
      });
    }, 200),
    search() {},
    selectSup(val) {
      console.log('val', val);
      this.searchMethod('');
      this.$emit('input', val);
      this.$emit('clickItem', val);
    },
    queryChange(val) {
      this.query = val;
      this.searchMethod(val);
    },
    clearSub() {
      this.searchMethod('');

      this.$emit('input', '');
      this.$emit('clickItem');
    }
  }
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
.disabled-tag {
  color: #ccc;
  margin-left: 10px;
}
</style>
