<template>
  <div class="text-tooltip">
    <el-tooltip
      :width="300"
      class="item"
      effect="dark"
      transition="none"
      :enterable="false"
      :disabled="isShowTooltip"
      :content="content"
      :placement="placement"
    >
      <p class="over-flow" :style="{ WebkitLineClamp }" :class="className" @mouseover="onMouseOver(refName)">
        <span :ref="refName">{{ content || '-' }}</span>
      </p>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  name: 'textTooltip',
  props: {
    // 显示的文字内容
    content: {
      type: String,
      default: () => {
        return '';
      },
    },
    // 位置
    placement: {
      type: String,
      default: () => {
        return 'top';
      },
    },
    // 外层框的样式，在传入的这个类名中设置文字显示的宽度
    className: {
      type: String,
      default: () => {
        return '';
      },
    },
    // 为页面文字标识（如在同一页面中调用多次组件，此参数不可重复）
    refName: {
      type: String,
      default: () => {
        return '';
      },
    },
  },
  data() {
    return {
      isShowTooltip: true,
      WebkitLineClamp: 2,
    };
  },
  methods: {
    onMouseOver(str) {
      let contentHeight = this.$refs[str].offsetHeight;
      const lineHight = this.getLineHeight(this.$refs[str]);
      const maxHeight = lineHight * this.WebkitLineClamp;
      // 若文字高度大于设置的最大高度，则显示tooltip
      if (maxHeight < contentHeight) {
        this.isShowTooltip = false;
      } else {
        // 否则 关掉tooltip功能
        this.isShowTooltip = true;
      }
    },
    computeStyle(elem, prop) {
      return window.getComputedStyle(elem, null).getPropertyValue(prop);
    },
    getLineHeight(elem) {
      var lh = this.computeStyle(elem, 'line-height');
      if (lh === 'normal') {
        // Normal line heights vary from browser to browser. The spec recommends
        // a value between 1.0 and 1.2 of the font size. Using 1.1 to split the diff.
        lh = parseInt(this.computeStyle(elem, 'font-size')) * 1.2;
      }
      return parseInt(lh);
    },
  },
};
</script>

<style lang="less" scoped>
.over-flow {
  // overflow: hidden;
  // white-space: nowrap;
  // text-overflow: ellipsis;
  display: -webkit-box;
  overflow: hidden;
  // -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
}
.wid190 {
  width: 100%;
}
p {
  margin: 0;
}
</style>

<style>
.el-tooltip__popper {
  max-width: 400px;
}
</style>
