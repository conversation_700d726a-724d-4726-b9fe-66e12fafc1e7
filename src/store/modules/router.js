import {developRoutes, errorRoutes} from '@/router/router';
import Main from '@/components/main';
import io from 'utils/request';
import store from '@/store';
import axios from 'axios';
import util from 'utils/util';
import {cloneDeep} from 'lodash';
// console.log( '-> util', util.isProdEnv )
const state = {
  routes: [],
  // 所有的元素编码
  idcodes: []
};

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.routes = routes.concat(developRoutes).concat(errorRoutes);
  },
  // 设置所有的元素编码
  SET_IDCODES: (state, payload) => {
    state.idcodes = payload;
  }
};

const actions = {
  // 获取用户的路由
  getUserRoutes({commit}) {
    return new Promise((resolve, reject) => {
      io.get('/pms_plat/permission.index.routes', {params: {new: 1}})
        .then(
          menus => {
            // axios.get('/routes/query').then(menus => {
            // 处理路由
            // let routes = handlerRoutes(menus.data.data)
            let routes = handlerRoutes(menus);
            commit('SET_ROUTES', routes);
            // 设置元素编码
            let codes = handlerIdCodes(menus);
            commit('SET_IDCODES', codes);
            // 处理菜单
            store.dispatch('menus/handlerMenus', state.routes).then();
            resolve(handleAddRoutes(state.routes));
          },
          data => {
            reject(data.errmsg);
          }
        )
        .catch(error => {
          reject(error);
        });
    });
    // if(util.isProdEnv){
    //
    // }else {
    //   return new Promise((resolve, reject) => {
    // 		  axios.get('/routes/query').then(menus => {
    // 			  // 处理路由
    // 		  let routes = handlerRoutes(menus.data.data)
    // 		  commit('SET_ROUTES', routes)
    // 		  // 处理菜单
    // 		  store.dispatch('menus/handlerMenus', state.routes).then()
    // 		  resolve(state.routes)
    // 	  }, data => {
    // 		  reject(data.errmsg)
    // 	  }).catch(error => {
    // 		  reject(error)
    // 	  })
    //   })
    // }
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};

function handlerRoutes(routes) {
  let res = [];
  routes = routes.filter(item => item.path.indexOf('erp') > -1)
  let i = 0;
  if (routes.length > 0) {
    routes.forEach(route => {
      let tmpRoute = route;
      tmpRoute.path = `/${route.path}`;
      tmpRoute.component = Main;
      if (route.children != undefined) {
        let tmpChildren = [];
        // route.children = route.children.filter(item=>item.path)
        let pathList = route.children.map(item => '/' + item.path);
        route.children.forEach((c_route, idx) => {
          let tmpCRoute = c_route;
          if (c_route.type !== 'GROUP') {
            tmpCRoute.path = `/${c_route.path}`;
            tmpCRoute.component = () => import(`@/view${c_route.path}`);
          }
          if (!pathList.includes(tmpRoute.path)) {
            if (c_route.path.startsWith('/erp') && idx === 1) {
              tmpRoute.path = tmpCRoute.path;
            } else {
              if (idx === 0) {
                tmpRoute.path = tmpCRoute.path;
              }
            }
          }

          tmpCRoute.meta.type = c_route.type;
          tmpCRoute.meta.p_id = c_route.p_id || '';
          tmpChildren.push(tmpCRoute);
        });
        tmpRoute.children = tmpChildren;
      }
      if (i == 0) {
        tmpRoute.redirect = tmpRoute.path;
        tmpRoute.path = '/';
      }
      i++;
      res.push(tmpRoute);
    });
  }
  console.log(routes)
  res.unshift({
    path:'/',
    redirect: routes[0].redirect,
  })
  return res;
}

function handleAddRoutes(routes) {
  // console.log("-> %c routes  === %o", "font-size: 15px;color: green;", routes)
  const copyRoutes = cloneDeep(routes);
  copyRoutes.map(route => {
    if (route.path.startsWith('/erp')) {
      route.children = route.children.filter(child => child.type !== 'GROUP');
    }
  });
  return copyRoutes;
}

/**
 * @description: 获取路由中所有的元素编码
 */
function handlerIdCodes(routes) {
  let codes = [];
  routes.forEach(item => {
    item.children.forEach(child_item => {
      if (child_item.type == 'PAGE') {
        child_item.element_list &&
        child_item.element_list.forEach(ele_item => {
          codes.push(ele_item.idcode);
        });
      }
    });
  });
  return codes;
}
