const state = {
  // 一级菜单
  firstMenus: [],

  // 所有二级菜单
  secondMenusAll: [],

  // 显示二级菜单
  // 没有二级菜单时，要隐藏二级菜单
  showSecondMenu: true,
  customPageStyle: {
    padding: '16px',
    margin: '10px',
    background: '#fff'
  }
};

const mutations = {
  CHANGE_STATE: (state, payload) => {
    state[payload.type] = payload.value;
  },
  SET_FIRST_MENUS: (state, firstMenus) => {
    state.firstMenus = firstMenus;
  },
  SET_SECOND_MENUS: (state, secondMenus) => {
    state.secondMenusAll = secondMenus;
  },
  SET_SHOW_SECOND_MENU: (state, v) => {
    if (v) {
      document.getElementsByTagName('body')[0].classList.remove('without-second-sidebar');
    } else {
      document.getElementsByTagName('body')[0].classList.add('without-second-sidebar');
    }
    state.showSecondMenu = v;
  },
  SET_CUSTOM_PAGE_STYLE: (state, v) => {
    console.log('-> %c v  === %o', 'font-size: 15px;color: green;', v);
    state.customPageStyle = { ...state.customPageStyle, ...v };
  },
  RESET_CUSTOM_PAGE_STYLE: state => {
    state.customPageStyle = {
      padding: '16px',
      margin: '10px',
      background: '#fff'
    };
  }
};

const actions = {
  handlerMenus({ commit }, routes) {
    return new Promise(resolve => {
      let { firstMenus, secondMenusAll } = handlerMenus(routes);
      commit('SET_FIRST_MENUS', firstMenus);
      commit('SET_SECOND_MENUS', secondMenusAll);
      resolve();
    });
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};

function handlerMenus(routes) {
  let firstMenus = [],
    secondMenusAll = {};

  routes.forEach(route => {
    if (route.type == 'MENU') {
      firstMenus.push({
        id: route.id,
        name: route.meta.title,
        path: route.path,
        redirect: route.redirect,
        query: route.query,
        type: route.type,
        meta: route.meta
      });

      if (route.children != undefined) {
        secondMenusAll['pid' + route.id] = [];
        route.children.forEach(c_route => {
          secondMenusAll['pid' + route.id].push({
            id: c_route.id,
            p_id: c_route.p_id,
            name: c_route.meta.title,
            path: c_route.path,
            query: c_route.query,
            type: c_route.type,
            meta: c_route.meta
          });
        });
      }
    }
  });

  return {
    firstMenus: firstMenus,
    secondMenusAll: secondMenusAll
  };
}
