const state = {
  title: '',
  clientHeight: '', // 浏览器高度
  showHelpWrapper: false, // 显示右侧帮助栏区域
  shouldRefresh: false
};

const mutations = {
  SET_TITLE: (state, title) => {
    state.title = title;
  },
  SET_CLIENT_HEIGHT: (state, clientHeight) => {
    state.clientHeight = clientHeight;
  },
  SHOW_HELP_WRAPPER: (state, showHelpWrapper) => {
    state.showHelpWrapper = showHelpWrapper;
  },
  CHANGE_FRESH_STATUS: (state, payload) => {
    state.shouldRefresh = payload;
  }
};

const actions = {
  setTitle({ commit }, title) {
    commit('SET_TITLE', title);
  },
  setClientHeight({ commit }, clientHeight) {
    commit('SET_CLIENT_HEIGHT', clientHeight);
  },
  showHelpWrapper({ commit }, showHelpWrapper) {
    commit('SHOW_HELP_WRAPPER', showHelpWrapper);
  }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions
};
