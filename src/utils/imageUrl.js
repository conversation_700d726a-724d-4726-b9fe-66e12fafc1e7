import config from '@/config';
import liluri from './lil-uri/lil-uri';

const { ImageDomian } = config;

let imageUrl = {};

/**
 *
 * 原图添加图片样式  !!!不在七牛存储中的图片使用默认的cdn域名，也不会添加图片样式
 * @param string $path 图片相对路径\图片URL
 * @param string|null $style 图片样式规格 例如：\KIF\Qiniu\ImageStyle::IMG_STYLE_200
 * @return string $url 返回完整的图片URL
 */
imageUrl.imageStyle = function (path, style) {
  if (path.trim().length <= 0) {
    return path;
  }

  let url = this.imageCdnDomain(path);

  // gif图片不加图片样式
  // 有些gif图裁剪存在。报错：{"error":"unsupported format:gif: not enough image data"}
  if (url.split('.').pop().toLowerCase() == 'gif') {
    return url;
  }

  return url + '-' + style;
};

/**
 *
 * 原图添加图片处理命令 !!!不在七牛存储中的图片使用默认的cdn域名，也不会添加图片处理命令
 * @param string path 图片相对路径\图片URL
 * @param string cmds 处理命令
 * @return string url 返回完整的图片URL
 */
imageUrl.imageCmds = function (path, cmds) {
  if (path.trim().length <= 0) {
    return path;
  }

  let url = this.imageCdnDomain(path);
  return url + '?' + cmds;
};

/**
 *
 * 图片添加cdn域名  !!!不在七牛存储中的图片使用默认的cdn域名
 * @param string path 图片相对路径\图片URL
 * @return string url 返回完整的图片URL
 */
imageUrl.imageCdnDomain = function (path) {
  if (path.trim().length <= 0) {
    return path;
  }

  path = this.getImagePath(path);
  return ImageDomian + '/' + path;
};

/**
 *
 * 获取图片相对路径
 * 如果有图片样式，同样会返回原图的相对路径
 * @param string path 图片相对路径\图片URL
 * @return string
 */
imageUrl.getImagePath = function (path) {
  if (path.trim().length <= 0) {
    return path;
  }
  if (path.match(/http/)) {
    path = liluri(path).path();
  }

  path = path.replace(/-B\..+$/, '');
  return path.replace(/^\//, '');
};
// function parse(uri)   {
//   var REGEX = /^(?:([^:\/?#]+):\/\/)?((?:([^\/?#@]*)@)?([^\/?#:]*)(?:\:(\d*))?)?([^?#]*)(?:\?([^#]*))?(?:#((?:.|\n)*))?/i
//   var parts = decode(uri || '').match(REGEX)
//   var auth = (parts[3] || '').split(':')
//   var host = auth.length ? (parts[2] || '').replace(/(.*\@)/, '') : parts[2]
//   return {
//     uri: parts[0],
//     protocol: parts[1],
//     host: host,
//     hostname: parts[4],
//     port: parts[5],
//     auth: parts[3],
//     user: auth[0],
//     password: auth[1],
//     path: parts[6],
//     search: parts[7],
//     hash: parts[8]
//   }
// }
// function decode (uri) {
//   try{
//     return decodeURIComponent(uri)
//   } catch (e) {
//     return unescape(uri)
//   }
// }
export default imageUrl;
