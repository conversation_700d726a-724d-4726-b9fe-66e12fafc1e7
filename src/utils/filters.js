import imageUrl from '@/utils/imageUrl'; // 图片样式处理工具
import util from '@/utils/util'; // 工具
export function date_format(timestamp, format = 'YYYY-MM-DD HH:mm:ss', dot = '-') {
  if (!timestamp || timestamp == 0) return dot;
  return util.moment.unix(timestamp).format(format);
}

// 格式化数字和金额
export function number_format(number, decimals = 2, dec_point = '.', thousands_sep = ',') {
  return util.number_format(number, decimals, dec_point, thousands_sep);
}

// 原图添加图片样式
export function imageStyle(path, style) {
  return imageUrl.imageStyle(path, style);
}

// 原图添加图片处理命令
export function imageCmds(path, style) {
  return imageUrl.imageCmds(path, style);
}

// 图片添加cdn域名
export function imageCdnDomain(path, style) {
  return imageUrl.imageCdnDomain(path, style);
}
