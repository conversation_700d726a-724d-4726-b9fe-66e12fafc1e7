/**
 * 请求接口
 * <AUTHOR>
 */
import {Message, LoadingBar} from 'view-design';
import axios from 'axios';
import global_config from '@/config';
import S from '@/utils/util';
import {getClinicid, getUid, isLogin, } from '@/utils/runtime';
import store from '@/store';
import {prolongCookie} from "./runtime";

// 创建 axios 实例
const instance = axios.create({
  baseURL: global_config.ApiDomain,
  timeout: 39000,
  headers: {'content-type': 'application/x-www-form-urlencoded'},
});

function changeParams(config, params) {
  Object.keys(params).forEach(item => {
    if (typeof params[item] == 'object') {
      params[item] = JSON.stringify(params[item]);
    }
    if (typeof params[item] == 'undefined') {
      params[item] = '';
    }
  });
  config.params = params;
  config.params['ifsign'] = S.makeApiSign(config.params);
  config.data = undefined;
}

// 请求拦截器
instance.interceptors.request.use(
  config => {
    //进度条
    // LoadingBar.start()
    // console.log(config,"请求拦截器");
    // 设置请求签名参数
    if(isLogin()){
      prolongCookie()
    }
    // 并重新设置请求的参数
    if (config.method == 'get') {
      let params = config.params || config.data || {};

      // 将数组和对象类型的数据转换为JSON字符串
      // Object.keys(params).forEach((item) => {
      //   if (typeof (params[item]) == 'object') {
      //     params[item] = JSON.stringify(params[item])
      //   }
      //   if (typeof (params[item]) == 'undefined') {
      //     params[item] = ''
      //   }
      // })
      changeParams(config, params);
    } else {
      if (config.method === 'post') {
        store.commit('app/CHANGE_FRESH_STATUS', true);
      }
      let data = config.data || config.params || {};
      //将数组和对象类型的数据转换为JSON字符串
      Object.keys(data).forEach(item => {
        if (typeof data[item] == 'object') {
          data[item] = JSON.stringify(data[item]);
        }
        if (typeof data[item] == 'undefined') {
          data[item] = '';
        }
      });

      data['ifsign'] = S.makeApiSign(data);

      // 默认情况下，axios将JavaScript对象序列化为JSON。
      // 要以application / x-www-form-urlencoded格式发送数据
      // 可以使用URLSearchParams API
      const params = new URLSearchParams();
      for (let k in data) params.append(k, data[k]);
      config.data = params;
      config.params = undefined;
    }

    // 环境参数
    let spm = '';
    let uid = getUid();
    // let clinicid = getClinicid()
    let evnParams = {
      timezone: 8,
      resolution: document.body.clientWidth + '*' + document.body.clientHeight,
      channel: 'h5',
      os: 'h5',
      device_id: '',
      uidentity: uid,
      spm: spm,
      app_version: global_config.codeVersion,
    };
    config.headers['IFENV'] = JSON.stringify(evnParams);

    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  response => {
    // console.log(response,"响应拦截器");
    const res = response.data;
    // LoadingBar.finish()
    if (res.errcode == '4007') {
      // Message.error(res.errmsg)
      logout();
      window.location.href = global_config.PlatDomain
    } else {
      if (res.errcode != 0) {
        // 登录超时
        Message.error(res.errmsg);
        return Promise.reject(res || 'Error');
        // return res
      }
    }
    let last_version = response.headers['x-version'];
    if (!S.isServeRun && last_version != undefined && last_version != '') {
      store.dispatch('version/checkVersion', last_version).then();
    }
    return res.data;
  },
  error => {
    return Promise.reject({
      errmsg: String(error),
    });
  }
);

export default instance;
