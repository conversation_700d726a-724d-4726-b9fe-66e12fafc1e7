import {MessageBox} from 'element-ui';
import * as runtime from '@/utils/runtime';
import {getUser} from '../utils/runtime'

const sessionName = 'sessionUserInfo';

function objHasValue(obj) {
  if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj).length !== 0;
  }
  return false;
}

function checkCookie(callback) {
  // console.log('checkCookie2222');
  // 获取当前的Cookie
  var currentCookie = JSON.stringify(runtime.getUser());

  // 检查Cookie是否发生变化
  if (currentCookie !== checkCookie.lastCookie) {
    // 触发Cookie变化的处理逻辑
    callback(currentCookie);
    // 更新上次检查的Cookie
    checkCookie.lastCookie = currentCookie;
  }
}

export default {
  install(Vue, options) {
    // 初始时获取一次Cookie
    checkCookie.lastCookie = JSON.stringify(runtime.getUser());

    window.addEventListener('beforeunload', event => {
      // 页面刷新时，清除sessionStorage
      sessionStorage.removeItem(sessionName);
    });

    // window.addEventListener('storage', e => {
    //   console.log(sessionStorage, '==================');
    // });

    document.addEventListener(
      'visibilitychange',
      function (e) {
        // console.log(213123)
        //浏览器tab切换监听事件
        const origin = window.location.origin;
        const userInfo = runtime.getUser();

        checkCookie(() => {
          if (!objHasValue(JSON.parse(sessionStorage.getItem(sessionName)))) {
            sessionStorage.setItem(sessionName, JSON.stringify(runtime.getUser()));
          }
        });
        // checkCookie(() => {
        //   if (!objHasValue(JSON.parse(sessionStorage.getItem(sessionName)))) {
        //     sessionStorage.setItem(sessionName, JSON.stringify(userInfo));
        //   }
        // });

        if (!document.visibilityState === 'visible') {
          try {
            MessageBox.close();
          } catch (error) {
            console.log(error);
          }
        }

        if (document.hidden || origin === window.location.origin) {
          // console.log('page 展示')
          if (objHasValue(userInfo)) {
            if (runtime.isLogin()) {
              if (objHasValue(JSON.parse(sessionStorage.getItem(sessionName)))) {
                const sessionUserInfo = JSON.parse(sessionStorage.getItem(sessionName));
                if (sessionUserInfo.uid !== userInfo.uid) {
                  try {
                    MessageBox.close();
                  } catch (error) {
                    console.log(error);
                  }
                  MessageBox.alert(
                    '出现该弹窗是因为你在其他页签上切换过账号，为了防止不同账号之间的页面信息错位，我们需要你登录到最新的账号',
                    '系统检测到你当前的页面已经发生了账号变更',
                    {
                      type: 'warning',
                      confirmButtonText: '跳转新的账号',
                      showClose: false,
                    }
                  ).then(() => {
                    sessionStorage.removeItem(sessionName);
                    window.location.href = '/';

                    // if (window.location.href.indexOf('login') > -1) {
                    //   window.location.href = '/';
                    // } else {
                    //   window.location.reload();
                    // }
                  });
                } else {
                  console.log('已登录，未切换账号');
                  try {
                    MessageBox.close();
                  } catch (error) {
                    console.log(error);
                  }
                  if (window.location.href.indexOf('login') > -1) {
                    window.location.href = '/';
                  }
                }
              } else {
                console.log('已登录，设置sessionStorage');
                sessionStorage.setItem(sessionName, JSON.stringify(userInfo));
              }
            }
          } else {
            if (window.location.href.indexOf('login') > -1) return;
            try {
              MessageBox.close();
            } catch (error) {
              console.log(error);
            }
            MessageBox.alert(
              '出现该弹窗是因为你在其他页签上已退出登录，为了防止不同账号之间的页面信息错位，我们需要你重新登录',
              '系统检测到当前页面所在的账号已退出登录',
              {
                type: 'warning',
                confirmButtonText: '跳转登录页',
                showClose: false,
              }
            ).then(() => {
              sessionStorage.removeItem(sessionName);
              runtime.logout();
              window.location.reload();
            });
          }
        }
      },
      false
    );
  },
};
