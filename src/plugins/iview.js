import Vue from 'vue';
import <PERSON><PERSON> from 'view-design';
import selectMixin from './iviewCustomPlugin/selectMixin';
import { Select } from 'view-design';
// ViewUI.Table.components.tableBody.components.TableTr.methods.
ViewUI.Table.components.tableBody.components.TableTr.methods.rowClsName = function (_index) {
  return this.$parent.$parent.rowClassName(this.row, _index);
};
ViewUI.Select.mixins.push(selectMixin);
ViewUI.Tooltip.deactivated = function () {
  this.handleClosePopper();
};
Vue.use(ViewUI, {
  transfer: true,
  capture: false,
});

/**
 * @description: 优化select重新打开后，滚动条没有置顶
 * */
Select.watch.dropVisible = function (val) {
  if (val === false && (this.publicValue === undefined || this.publicValue === '')) {
    let nodeList = Array.from(document.getElementsByClassName('ivu-select-dropdown-list'));
    nodeList &&
      nodeList.forEach(item => {
        item.scrollIntoView();
      });
  }
};
