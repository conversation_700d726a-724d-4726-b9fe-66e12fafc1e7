import Vue from 'vue';
import popupManager from 'element-ui/lib/utils/popup/popup-manager';
import {
  Cascader,
  Select,
  Option,
  Upload,
  DatePicker,
  Tooltip,
  Popover,
  Input,
  CheckboxGroup,
  Checkbox,
  Loading,
  Descriptions,
  DescriptionsItem,
} from 'element-ui';
Vue.use(Cascader)
  .use(Select)
  .use(Option)
  .use(Upload)
  .use(DatePicker)
  .use(Tooltip)
  .use(Popover)
  .use(Input)
  .use(CheckboxGroup)
  .use(Checkbox)
  .use(Loading.directive)
  .use(Descriptions)
  .use(DescriptionsItem);

// 设置poppver的初始z-index的值,防止出现poppver被modal遮挡的问题
popupManager.zIndex = 1000000;
