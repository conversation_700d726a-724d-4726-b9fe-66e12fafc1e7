module.exports = {
  /**
   * 项目标题
   */
  title: '树家ERP管理平台 - 测试',

  /**
   * 项目短标题
   */
  shortTitle: '树家ERP管理平台 - 测试',

  /**
   * 代码版本
   */
  codeVersion: process.env.VUE_APP_CODE_VERSION,

  /**
   * @description: crypto 加密相关信息
   * */
  cryptoVersion: '2',

  /**
   * @description:加密key
   * @description:满足16位，php对于key的处理与js不同，统一保持16位key即可
   */
  cryptoKey: 'qHOoDOJPt2XUKnUs',

  /**
   * @description:加密iv偏移量
   */
  cryptoIv: 'qHOoDOJPt2XUKnUs',

  /**
   * 接口请求签名Key
   */
  mapiSignKey: '0pQpYWAov3',

  /**
   * 接口地址
   * 如：https://mapi.biranmall.com/ 、https://mapi.biranmall.com/app/
   */
  ApiDomain: 'https://erp2sit-7mpjre3egwvwzbvvojneq.rsjxx.com/api-server/',
  PlatDomain: 'https://plat2sit-towu5zmnknduxnwy1m.rsjxx.com',
  /**
   * 静态资源地址
   * 如：https://wwwcdn.biranmall.com/ 、 https://wwwcdn.biranmall.com/app/
   */
  // CdnDomain: 'https://wwwcdn2test.rsjxx.com/pms-plat/',
  CdnDomain: 'https://wwwcdn2sit.rsjxx.com/fpc-rsjerp/',

  /**
   * 图片资源地址
   * 如：https://test2img.biranmall.com/
   */
  ImageDomian: 'https://static.rsjxx.com',

  /**
   * 本地存储Key的前缀
   * 如：测试环境前缀 TEST
   */
  // storageNamePrefix: 'TEST-FPC-RSJERP',
  // storageNamePlatPrefix: 'TEST-PMS-PLAT',
  storageNamePrefix: 'TEST-PMS-PLAT-V1',
};
/*
	'5qLqYMX2dx', // h5 门店加盟接口
 '0pQpYWAov3', // PMS平台接口
 'nAYA9Vy6Lv', // PMS供应商接口
 'j3236VNDny', // PMS省公司接口*/

/*正式环境

 'L9y9DPJbxE', // h5 门店加盟接口
 '1505VLEOXA', // PMS平台接口
 '4D6DYJEwWB', // PMS供应商接口
 'wyYykj0xN3', // PMS省公司接口*/
