const configFiles = require.context('./', true, /\.js$/);
const configs = configFiles.keys().reduce((configs, configPath) => {
  const fileName = configPath.replace(/^\.\/(.*)\.\w+$/, '$1');
  if (fileName == 'index') {
    return configs;
  }
  const value = configFiles(configPath);
  configs[fileName] = value;
  return configs;
}, {});

let env = '';
console.log(process.env.VUE_APP_NODE_ENV);
if (process.env.VUE_APP_NODE_ENV === 'production') {
  env = 'prod';
} else if (process.env.VUE_APP_NODE_ENV === 'test') {
  env = 'test';
} else if (process.env.VUE_APP_NODE_ENV === 'test-74') {
  env = 'test-74';
}
export default configs['index.' + env];
