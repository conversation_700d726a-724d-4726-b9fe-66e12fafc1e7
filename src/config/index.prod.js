module.exports = {
  /**
   * 项目标题
   */
  title: '树家ERP管理平台',

  /**
   * 项目短标题
   */
  shortTitle: '树家ERP管理平台',

  /**
   * 代码版本
   */
  codeVersion: process.env.VUE_APP_CODE_VERSION,

  /**
   * @description: crypto 加密相关信息
   * */
  cryptoVersion: '2',

  /**
   * @description:加密key
   * @description:满足16位，php对于key的处理与js不同，统一保持16位key即可
   */
  cryptoKey: 'g9X8ukgJQoWnPxgc',

  /**
   * @description:加密iv偏移量
   */
  cryptoIv: 'g9X8ukgJQoWnPxgc',

  /**
   * 接口请求签名Key
   */
  mapiSignKey: '1505VLEOXA',

  /**
   * 接口地址
   * 如：https://mapi.biranmall.com/ 、https://mapi.biranmall.com/app/
   */
  ApiDomain: 'https://erp-ykv6wopb3bqjbnvgogjjq.rsjxx.com/api-server/',
  // ApiDomain: 'http://pms.rsjxx.com:8073/api-server/',//开发环境调试
  PlatDomain: 'https://pms.rsjxx.com/plat',
  /**
   * 静态资源地址
   * 如：https://wwwcdn.biranmall.com/ 、 https://wwwcdn.biranmall.com/app/
   */
  CdnDomain: 'https://wwwcdn.rsjxx.com/fpc-rsjerp/',

  /**
   * 图片资源地址
   * 如：https://img01.biranmall.com/
   */
  ImageDomian: 'http://static.rsjxx.com',

  /**
   * 本地存储Key的前缀
   * 如：测试环境前缀 TEST
   */
  // storageNamePrefix: 'FPC-RSJERP',
  // storageNamePlatPrefix: 'PMS-PLAT-V1',
  storageNamePrefix: 'PMS-PLAT-V1',
};
