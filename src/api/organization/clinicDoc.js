class ClinicDoc {
  // 获取医生列表
  async getPhysicianList(query) {
    return await this.$http.get('/pms_plat/physician.index.list', { params: query });
  }

  // 获取医生列表
  async getPhysicianOptions(query) {
    return await this.$http.get('/pms_plat/physician.index.options', { params: query });
  }

  // 保存医生信息
  async savePhysicianDetail(params) {
    return await this.$http.post('/pms_plat/physician.save', params);
  }

  // 获取详情
  async getPhysicianDetail(params) {
    return await this.$http.get('/pms_plat/physician.index.info', { params });
  }
  // 获取科室
  async getClinicOptionsList(params) {
    return await this.$http.get('/pms_plat/physician.index.clinicList', { params });
  }
  // 获取科室
  async getDepartment(params) {
    return await this.$http.get('/pms_plat/physician.getDepartment', { params });
  }
  // 根据身份证号获取已经审批过的信息
  async getInfoByIdCard(params) {
    return await this.$http.get('/pms_plat/physician.getInfoByIdCard', { params });
  }
  // 根据身份证号获取已经审批过的信息
  async setPhysicianAudit(params) {
    return await this.$http.post('/pms_plat/physician.index.audit', params);
  }

  // 获取审核列表
  async getPhysicianAuditList(query) {
    return await this.$http.get('/pms_plat/physician.index.physicianauditlog', { params: query });
  }
}

export default ClinicDoc;
