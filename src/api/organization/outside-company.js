class outsideCompany {
  // 获取外部公司列表
  async getOutsideCompanyList(params) {
    return this.$http.get('/pms_plat/outside.outsideCompany.list', { params });
  }
  // 获取外部公司枚举
  async getOutsideCompanyOptions(params) {
    return this.$http.get('/pms_plat/outside.outsideCompany.options', { params });
  }
  // 获取外部公司详情
  async getOutsideCompanyDetail(params) {
    return this.$http.get('/pms_plat/outside.outsideCompany.detail', { params });
  }
  // 获取外部公司操作记录
  async getOutsideCompanyFieldLog(params) {
    return this.$http.get('/pms_plat/outside.outsidecompany.getfieldlog', { params });
  }
}

export default outsideCompany;
