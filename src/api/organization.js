class Method_organization {
  /**
   *
   * @param {*} extendCls 最终继承类
   */
  //省公司列表
  async getComList(params) {
    return await this.$http.get('/pms_plat/company.index.list', { params });
  }
  //省公司选项列表
  async getComOptions() {
    return await this.$http.get('/pms_plat/company.index.options');
  }
  //省公司详情
  async getComDetail(params) {
    return this.$http.get('/pms_plat/company.index.info', { params });
  }
  //省公司启用禁用
  async changeComStatus(params) {
    return await this.$http.get('/pms_plat/company.index.changestatus', { params });
  }
  //编辑省公司
  async postComEdit(params) {
    return this.$http.get('/pms_plat/company.index.edit', { params });
  }
  //诊所列表
  async getClinicList(params) {
    return this.$http.get('/pms_plat/clinic.enter.list', { params });
  }
  //诊所入住详情
  async getClinicDetail(params) {
    return this.$http.get('/pms_plat/clinic.enter.info', { params });
  }
  //诊所入驻选项
  async getClinicOptions(params) {
    return this.$http.get('/pms_plat/clinic.enter.options', { params });
  }
  // 审核禁用/启用
  async auditClinicDisabled(params) {
    return this.$http.post('/pms_plat/clinic.enter.auditDisabled', params);
  }

  // 财务 - 开业预付款明细下载地址
  async getclinicincomeurl(params) {
    return this.$http.get('/pms_plat/finance.report.getclinicincomeurl', { params });
  }
  // 获取已经开通服务的省
  async getRegionBySup(params) {
    return this.$http.get('/pms_plat/clinic.enter.getregionbysup', { params });
  }
  // 根据省获取对应的供应商
  async getInsureSupByRegion(params) {
    return this.$http.get('/pms_plat/clinic.enter.getinsuresupbyregion', { params });
  }
  // 诊所开通医保
  async openInsurance(params) {
    return this.$http.post('/pms_plat/clinic.enter.openinsure', params);
  }

  /**
   * @description: 2022.01.18-城市合伙人需求
   * */
  // 获取城市合伙人列表
  async getPartnerList(params) {
    return this.$http.get('/pms_plat/clinic.partner.list', { params });
  }

  // 获取城市合伙人枚举数据
  async getPartnerOptions(params) {
    return this.$http.get('/pms_plat/clinic.partner.options', { params });
  }

  // 获取城市合伙人详情信息
  async getPartnerInfo(params) {
    return this.$http.get('/pms_plat/clinic.partner.info', { params });
  }

  /**
   * @description: 2023.02.23-诊所采购权限
   * */
  // 获取诊所采购权限信息
  async getPurchasingAuthorityInfo(params) {
    return this.$http.get('/pms_plat/clinic.enter.purchasingAuthorityInfo', { params });
  }
  // 编辑诊所采购权限信息
  async setPurchasingAuthority(params) {
    return this.$http.post('/pms_plat/clinic.enter.setPurchasingAuthority', params);
  }

  async editClinic(params) {
    return await this.$http.post('/pms_plat/clinic.enter.edit', params);
  }

  async exportClinicList(params) {
    return await this.$http.get('/pms_plat/clinic.enter.export', { params });
  }

  async getSeetList(params) {
    return await this.$http.get('/pms_plat/clinic.enter.getEditFieldLog', { params });
  }

  // 导出城市合伙人报表
  async getPartnerExport(params) {
    return this.$http.get('/pms_plat/clinic.partner.export', { params });
  }

  // 城市合伙人审核
  async examinePartnerStatus(params) {
    return this.$http.post('/pms_plat/clinic.partner.status', params);
  }

  // 获取城市合伙人操作记录
  async getPartnerEditFieldLogList(params) {
    return this.$http.get('/pms_plat/clinic.partner.getEditFieldLog', { params });
  }
}
export default Method_organization;
