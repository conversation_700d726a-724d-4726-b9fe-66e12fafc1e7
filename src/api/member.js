class Method_member {
  // 更新密码
  async getChgpass(params) {
    const res = await this.$http.post('/pms_plat/member.chgpass', params);
    return res;
  }

  /*   鉴权配置 */

  // 获取鉴权列表
  async getAuthorizationList(params) {
    return await this.$http.get('/pms_plat/loginauthorization.list', { params });
  }

  // 登录鉴权枚举
  async getAuthorizationOption(params) {
    return await this.$http.get('/pms_plat/loginauthorization.option', { params });
  }

  // 生成手机号
  async getLoginMobile(params) {
    return await this.$http.get('/pms_plat/loginauthorization.generatemobile', { params });
  }

  // 鉴权登录记录
  async getAuthorizationRecord(params) {
    return await this.$http.get('/pms_plat/loginauthorization.record', { params });
  }

  // 鉴权配置
  async getAuthorizationSetting(params) {
    return await this.$http.post('/pms_plat/loginauthorization.setting', params);
  }

  // 鉴权失效
  async getAuthorizationDisable(params) {
    return await this.$http.post('/pms_plat/loginauthorization.disable', params);
  }

  // 角色列表
  async getMemberOptions(params) {
    return await this.$http.get('/pms_plat/member.options', { params });
  }
}

export default Method_member;
