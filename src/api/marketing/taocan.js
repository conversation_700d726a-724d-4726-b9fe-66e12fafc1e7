export default class Taocan{
  // 获取选项枚举
  async getPmspromOptions(params) {
    return await this.$http.get('/pms_plat/promotion.pmsprom.options', {params});
  }

  // 获取活动列表
  async getPmspromList(params) {
    return await this.$http.get('/pms_plat/promotion.pmsprom.list', {params});
  }

  // 获取商品sku列表
  async searchPmspromGoodslist(params) {
    return await this.$http.get('/pms_plat/promotion.pmsprom.goodslist', {params});
  }

  // 保存套餐活动信息
  async savePmspromTaocan(params) {
    return await this.$http.post('/pms_plat/promotion.pmsprom.savetaocan', params);
  }

  // 获取套餐活动信息
  async getPmspromInfo(params) {
    return await this.$http.get('/pms_plat/promotion.pmsprom.info', {params});
  }

  // 活动状态变更
  async updatePmspromStatus(params) {
    return await this.$http.post('/pms_plat/promotion.pmsprom.status', params);
  }

}
