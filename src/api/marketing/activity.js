class Activity {
  // 获取促销管理列表
  async getJoinInPromotionList(params) {
    return await this.$http.get('/pms_plat/joinin.promotion.list', { params });
  }
  // 获取促销管理列表
  async editJoinInPromotion(params) {
    return await this.$http.post('/pms_plat/joinin.promotion.edit', params);
  }

  // 获取促销管理商品列表
  async getJoinInPackageList(params) {
    return await this.$http.get('/pms_plat/joinin.package.spulist', { params });
  }

  // 获取促销管理选项信息
  async getJoinInPromotionOptions(params) {
    return await this.$http.get('/pms_plat/joinin.promotion.options', { params });
  }

  // 获取促销管理详情
  async getJoinInPromotionInfo(params) {
    return await this.$http.get('/pms_plat/joinin.promotion.info', { params });
  }
  // 获取促销管理二维码
  async getJoinInPromotionQrCode(params) {
    return await this.$http.get('/pms_plat/joinin.promotion.getmobileqr', { params });
  }
  // 获取促销管理二维码
  async changeJoinInPromotionStatus(params) {
    return await this.$http.get('/pms_plat/joinin.promotion.status', { params });
  }

  // 获取秒杀明细
  async getJoininPurUrl(params) {
    return await this.$http.get('/pms_plat/joinin.report.getpururl', { params });
  }
}

export default Activity;
