class Method_erp {
  /*   供应商    */

  // 获取供应商列表
  async getErpSupplierList(params) {
    return await this.$http.get('/pms_plat/erp.supplier.list', { params });
  }

  // 创建供应商
  async createErpSupplier(params) {
    return await this.$http.post('/pms_plat/erp.supplier.create', params);
  }

  // 编辑供应商
  async editErpSupplier(params) {
    return await this.$http.post('/pms_plat/erp.supplier.edit', params);
  }

  // 获取供应商详情
  async getErpSupplierDetail(params) {
    return await this.$http.get('/pms_plat/erp.supplier.detail', { params });
  }

  // 供货产品列表
  async getErpSupplierProduct(params) {
    return await this.$http.get('/pms_plat/erp.supplier.products', { params });
  }

  // 获取采购单列表
  async getErpSupplierPurchase(params) {
    return await this.$http.get('/pms_plat/erp.supplier.purchase', { params });
  }

  // 获取采购退货单列表
  async getErpSupplierPurchaseReturn(params) {
    return await this.$http.get('/pms_plat/erp.supplier.purchasereturn', { params });
  }

  /* 仓库管理 */

  // 获取仓库列表
  async getErpWarehouseList(params) {
    return await this.$http.get('/pms_plat/erp.warehouse.list', { params });
  }
  // 根据月份获取报表仓库
  async getErpFinancesWarehouseList(params) {
    return await this.$http.get('/pms_plat/erp.finances.warehouseList', { params });
  }

  // 创建仓库
  async createErpWarehouse(params) {
    return await this.$http.post('/pms_plat/erp.warehouse.create', params);
  }

  // 编辑仓库信息
  async editErpWarehouse(params) {
    return await this.$http.post('/pms_plat/erp.warehouse.edit', params);
  }

  // 获取仓库详情
  async getErpWarehouseDetail(params) {
    return await this.$http.get('/pms_plat/erp.warehouse.detail', { params });
  }

  // 获取仓库详情
  async getErpWarehouseProds(params) {
    return await this.$http.get('/pms_plat/erp.warehouse.prods', { params });
  }

  // 创建采购退货单
  async purchasereturnCreate(params) {
    return await this.$http.post('/pms_plat/erp.purchasereturn.create', params);
  }

  // 获取采购退货单的商品详情
  async getErpPurchasereturnDetail(params) {
    return await this.$http.get('/pms_plat/erp.purchasereturn.detail', { params });
  }

  /**
   * @description: 入库
   * */
  // 创建入库单
  async erpInboundCreate(params) {
    return await this.$http.post('/pms_plat/erp.inbound.create', params);
  }

  // 获取入库单列表
  async getErpInboundList(params) {
    return await this.$http.get('/pms_plat/erp.inbound.list', { params });
  }

  // 获取入库单信息
  async getErpInboundInfo(params) {
    return await this.$http.get('/pms_plat/erp.inbound.info', { params });
  }

  // 获取入库单产品明细
  async getErpInboundDetail(params) {
    return await this.$http.get('/pms_plat/erp.inbound.detail', { params });
  }

  // 获取入库单产品明细
  async getErpInboundOptions(params) {
    return await this.$http.get('/pms_plat/erp.inbound.options', { params });
  }

  // 入库单审核
  async erpInboundStatus(params) {
    return await this.$http.post('/pms_plat/erp.inbound.status', params);
  }

  // 入库单编辑
  async erpInboundEdit(params) {
    return await this.$http.post('/pms_plat/erp.inbound.edit', params);
  }

  /*   产品出库    */

  // 创建出库单
  async erpOutboundCreate(params) {
    return await this.$http.post('/pms_plat/erp.outbound.create', params);
  }

  // 批量创建出库单
  async erpOutboundBatchCreate(params) {
    return await this.$http.post('/pms_plat/erp.outbound.batchcreate', params);
  }

  // 获取出库单列表
  async getErpOutboundList(params) {
    return await this.$http.get('/pms_plat/erp.outbound.list', { params });
  }

  // 导出待出库订单模版
  async exportOutOrderTemplate(params) {
    return await this.$http.get('/pms_plat/erp.outbound.exporttemplate', { params });
  }

  // 获取出库单信息
  async getErpOutboundInfo(params) {
    return await this.$http.get('/pms_plat/erp.outbound.info', { params });
  }

  // 获取出库单产品明细
  async getErpOutboundDetail(params) {
    return await this.$http.get('/pms_plat/erp.outbound.detail', { params });
  }

  // 获取出库单产品明细
  async getErpOutboundExpress(params) {
    return await this.$http.get('/pms_plat/erp.outbound.express', { params });
  }

  // 获取出库单产品明细
  async getErpOutboundOptions(params) {
    return await this.$http.get('/pms_plat/erp.outbound.options', { params });
  }

  /*   产品管理    */

  // 获取产品列表
  async getErpProductList(params) {
    return await this.$http.get('/pms_plat/erp.product.list', { params });
  }

  // 获取产品选项信息
  async getErpProductOptions(params) {
    return await this.$http.get('/pms_plat/erp.product.options', { params });
  }

  // 创建产品
  async createErpProduct(params) {
    return await this.$http.post('/pms_plat/erp.product.create', params);
  }

  // 更新产品
  async updateErpProduct(params) {
    return await this.$http.post('/pms_plat/erp.product.edit', params);
  }

  // 获取产品明细
  async getErpProductDetail(params) {
    return await this.$http.get('/pms_plat/erp.product.detail', { params });
  }

  /*          采购订单       */

  // 创建采购订单
  async purchaseCreate(params) {
    return await this.$http.post('/pms_plat/erp.purchase.create', params);
  }

  // 编辑采购订单
  async purchaseEdit(params) {
    return await this.$http.post('/pms_plat/erp.purchase.edit', params);
  }
  // 编辑采购订单
  async purchaseModification(params) {
    return await this.$http.post('/pms_plat/erp.purchase.modification', params);
  }

  // 获取采购订单列表
  async getErpPurchaseList(params) {
    return await this.$http.get('/pms_plat/erp.purchase.list', { params });
  }
  // 获取采购订单列表
  async getErpPurchaseOptions(params) {
    return await this.$http.get('/pms_plat/erp.purchase.options', { params });
  }
  // 获取采购订单列表
  async invalidErpPurchase(params) {
    return await this.$http.post('/pms_plat/erp.purchase.invalid', params);
  }

  // 修改采购单状态
  async changeErpPurchaseStatus(params) {
    return await this.$http.post('/pms_plat/erp.purchase.status', params);
  }

  // 获取销售订单列表
  async getErpSaleOrderList(params) {
    return await this.$http.get('/pms_plat/erp.order.list', { params });
  }

  // 获取采购订单信息
  async getErpPurchaseInfo(params) {
    return await this.$http.get('/pms_plat/erp.purchase.info', { params });
  }

  // 获取采购订单产品明细
  async getErpPurchaseDetail(params) {
    return await this.$http.get('/pms_plat/erp.purchase.detail', { params });
  }

  // 获取采购订单入库记录
  async getErpPurchaseInboundDetail(params) {
    return await this.$http.get('/pms_plat/erp.purchase.inbounddetail', { params });
  }

  // 获取采购订单退货记录
  async getErpPurchaseReturnRecord(params) {
    return await this.$http.get('/pms_plat/erp.purchase.returndetail', { params });
  }

  /*          采购退货       */
  // 获取采购退货列表
  async getErpPurchaseReturnList(params) {
    return await this.$http.get('/pms_plat/erp.purchasereturn.list', { params });
  }

  // 获取退款的商品数据
  async getErpPurchasereturnInfo(params) {
    return await this.$http.get('/pms_plat/erp.purchasereturn.info', { params });
  }

  // 修改采购单状态
  async changeErpPurchaseReturnStatus(params) {
    return await this.$http.post('/pms_plat/erp.purchasereturn.status', params);
  }

  // 获取采购退货单出库明细
  async getErpPurchaseOutboundDetail(params) {
    return await this.$http.get('/pms_plat/erp.purchasereturn.outbounddetail', { params });
  }

  // 编辑采购退货单
  async purchasereturnEdit(params) {
    return await this.$http.post('/pms_plat/erp.purchasereturn.edit', params);
  }

  /*          操作记录       */
  // 获取操作日志
  async getErpOperationlog(params) {
    return await this.$http.get('/pms_plat/erp.operationlog.record', { params });
  }

  /*          销售订单相关接口       */
  // 获取销售订单列表
  async getErpOrderList(params) {
    return await this.$http.get('/pms_plat/erp.order.list', { params });
  }

  // 获取销售订单信息
  async getErpOrderInfo(params) {
    return await this.$http.get('/pms_plat/erp.order.info', { params });
  }

  // 销售订单产品明细
  async getErpOrderProductDetail(params) {
    return await this.$http.get('/pms_plat/erp.order.detail', { params });
  }

  // 创建销售订单
  async createSalesOrder(params) {
    return await this.$http.post('/pms_plat/erp.order.create', params);
  }

  // 编辑销售订单
  async editSalesOrder(params) {
    return await this.$http.post('/pms_plat/erp.order.edit', params);
  }

  // 修改销售订单状态
  async changeSalesOrderStatus(params) {
    return await this.$http.post('/pms_plat/erp.order.status', params);
  }

  // 销售订单产品明细
  async getErpOrderOutboundDetail(params) {
    return await this.$http.get('/pms_plat/erp.order.outbounddetail', { params });
  }

  // 销售退货单列表
  async getErpOrderReturnList(params) {
    return await this.$http.get('/pms_plat/erp.order.returndetail', { params });
  }

  // 销售平台配置
  async getErpOrderPlatform(params) {
    return await this.$http.get('/pms_plat/erp.order.platform', { params });
  }

  /**
   * 销售退货相关接口
   */

  // 获取销售退货订单列表
  async getErpReturnOrderList(params) {
    return await this.$http.get('/pms_plat/erp.orderreturn.list', { params });
  }

  // 获取销售退货订单信息
  async getErpReturnOrderInfo(params) {
    return await this.$http.get('/pms_plat/erp.orderreturn.info', { params });
  }

  // 销售退货订单产品明细
  async getErpReturnOrderProductDetail(params) {
    return await this.$http.get('/pms_plat/erp.orderreturn.detail', { params });
  }

  // 创建销售退货订单
  async createSalesReturnOrder(params) {
    return await this.$http.post('/pms_plat/erp.orderreturn.create', params);
  }

  // 修改销售退货订单
  async editSalesReturnOrder(params) {
    return await this.$http.post('/pms_plat/erp.orderreturn.edit', params);
  }

  // 编辑状态
  async changeSalesReturnOrderStatus(params) {
    return await this.$http.post('/pms_plat/erp.orderreturn.status', params);
  }

  // 获取销售退货入库记录
  async getOrderReturnInboundDetail(params) {
    return await this.$http.get('/pms_plat/erp.orderreturn.inbounddetail', { params });
  }

  // 获取销售退货订单列表
  async getErpProductstockList(params) {
    return await this.$http.get('/pms_plat/erp.productstock.list', { params });
  }

  // 获取销售退货订单列表
  async getErpProductstockBills(params) {
    return await this.$http.get('/pms_plat/erp.productstock.bills', { params });
  }

  // 获取出入库类型枚举
  async getErpProductstockBillsOptions(params) {
    return await this.$http.get('/pms_plat/erp.productstock.billsoptions', { params });
  }

  /**
   * 物流管理相关接口
   */

  // 获取物流管理列表
  async getErpLogisticsList(params) {
    return await this.$http.get('/pms_plat/erp.logistics.list', { params });
  }
  // 获取物流管理列表
  async getErpLogisticsOptions(params) {
    return await this.$http.get('/pms_plat/erp.logistics.options', { params });
  }
  // 获取快递单详情
  async getErpLogisticsInfo(params) {
    return await this.$http.get('/pms_plat/erp.logistics.info', { params });
  }
  // 更新物流信息
  async getErpLogisticsQueryUpdate(params) {
    return await this.$http.get('/pms_plat/erp.logistics.queryupdate', { params });
  }
  // 获取快递单号查询物流轨迹
  async getErpLogisticsQuery(params) {
    return await this.$http.get('/pms_plat/erp.logistics.query', { params });
  }
  // 获取快递单号查询物流轨迹并订阅
  async getErpLogisticsQuerySubscribe(params) {
    return await this.$http.get('/pms_plat/logistics.querysubscribe', { params });
  }
  // 创建快递单号订阅
  async createErpLogistics(params) {
    return await this.$http.post('/pms_plat/erp.logistics.create', params);
  }
  /*
   * @description: 库存调拨
   * */

  // 获取库存调拨列表
  async getErpTransferList(params) {
    return await this.$http.get('/pms_plat/erp.transfer.list', { params });
  }

  // 获取库存调拨枚举值
  async getErpTransferOptions(params) {
    return await this.$http.get('/pms_plat/erp.transfer.options', { params });
  }

  // 创建库存调拨单
  async createTransfer(params) {
    return await this.$http.post('/pms_plat/erp.transfer.create', params);
  }

  // 修改调拨单信息
  async EditTransfer(params) {
    return await this.$http.post('/pms_plat/erp.transfer.edit', params);
  }

  /**
   * 物流管理相关接口
   */

  // 获取调拨单信息
  async getErpTransferInfo(params) {
    return await this.$http.get('/pms_plat/erp.transfer.info', { params });
  }

  // 获取调拨单产品明细
  async getErpTransferDetail(params) {
    return await this.$http.get('/pms_plat/erp.transfer.detail', { params });
  }

  // 获取调拨单产品明细
  async changeErpTransferStatus(params) {
    return await this.$http.post('/pms_plat/erp.transfer.status', params);
  }
  // 解决回显税率异常枚举
  async getErpProductNewOptions(params) {
    return await this.$http.get('/pms_plat/erp.product.options', { params });
  }

  // 渠道销售单一键采购
  async getOneKeyPurchaseInfo(params) {
    return await this.$http.get('/pms_plat/erp.order.onekeypurchase', { params });
  }
}

export default Method_erp;
