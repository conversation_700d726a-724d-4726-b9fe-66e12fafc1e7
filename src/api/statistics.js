class statiscis {
  /**
   * @file: statiscis.js
   * @description: 数据统计接口
   * @author: jiuxia
   * @date: 2022.3.29
   */

  // 趋势图
  async getTrend(query) {
    return await this.$http.get('/pms_plat/statisv2.index.trend', { params: query });
  }
  //获取简况
  async getProfile(params) {
    return await this.$http.get('/pms_plat/statisv2.index.brief', { params });
  }
  /**
   * 商品分析
   * */

  // 获取整体概况
  async getGoodsOverview(query) {
    return await this.$http.get('/pms_plat/statisv2.goods.overview', { params: query });
  }

  // 商品销售场景分析
  async getGoodsItemanalysis(query) {
    return await this.$http.get('/pms_plat/statisv2.goods.Itemanalysis', { params: query });
  }

  // 商品销售分析
  async getGoodsSalesanalysis(query) {
    return await this.$http.get('/pms_plat/statisv2.goods.salesanalysis', { params: query });
  }

  // 诊疗方剂常用
  async getGoodsPresrank(query) {
    return await this.$http.get('/pms_plat/statisv2.goods.presrank', { params: query });
  }

  // 诊疗治疗品常用排行
  async getGoodsPrestreatrank(query) {
    return await this.$http.get('/pms_plat/statisv2.goods.prestreatrank', { params: query });
  }

  // 商品交易明细
  async getGoodsTradelist(query) {
    return await this.$http.get('/pms_plat/statisv2.goods.tradelist', { params: query });
  }

  // 商品退款明细
  async getGoodsRefundlist(query) {
    return await this.$http.get('/pms_plat/statisv2.goods.refundlist', { params: query });
  }

  // HIS开方应用分析
  async getGoodsHisOrderanalysis(query) {
    return await this.$http.get('/pms_plat/statisv2.goods.hisorderanalysis', { params: query });
  }

  // 商城订单分析
  async getGoodsShopOrderanalysis(query) {
    return await this.$http.get('/pms_plat/statisv2.goods.shoporderanalysis', { params: query });
  }

  /**
   * 数据概况
   * */
  // 数据概况信息
  async getOverviewInfo(params) {
    return await this.$http.get('/pms_plat/statisv2.overview.index', { params });
  }
  // 数据转化分析
  async getOverviewRatio(params) {
    return await this.$http.get('/pms_plat/statisv2.overview.ratio', { params });
  }
  // 数据概况枚举
  async getOverviewOptions(params) {
    return await this.$http.get('/pms_plat/statisv2.index.options', { params });
  }
  /**
   * 交易分析
   * */
  // 数据汇总
  async getTradeOverview(query) {
    return await this.$http.get('/pms_plat/statisv2.trade.overview', { params: query });
  }

  // 交易相关信息
  async getTradeTradedetails(query) {
    return await this.$http.get('/pms_plat/statisv2.trade.tradedetails', { params: query });
  }

  // 交易统计
  async getTradeTradesummary(query) {
    return await this.$http.get('/pms_plat/statisv2.trade.tradesummary', { params: query });
  }

  // 导出交易统计
  async tradeExporttradesummary(query) {
    return await this.$http.get('/pms_plat/statisv2.trade.exporttradesummary', { params: query });
  }

  // 交易订单明细
  async getTradeOrders(query) {
    return await this.$http.get('/pms_plat/statisv2.trade.orders', { params: query });
  }

  // 交易退款明细
  async getTradeRefundorders(query) {
    return await this.$http.get('/pms_plat/statisv2.trade.refundorders', { params: query });
  }

  /**
   * 客户分析
   * */
  // 整体概况
  async getCustomerOverview(query) {
    return await this.$http.get('/pms_plat/statisv2.customer.overview', { params: query });
  }

  // 客户消费情况分析
  async getCustomerConsumeinfo(query) {
    return await this.$http.get('/pms_plat/statisv2.customer.consumeinfo', { params: query });
  }

  // 客户分布分析(画像,待服务,首购)
  async getCustomerAnalysis(query) {
    return await this.$http.get('/pms_plat/statisv2.customer.analysis', { params: query });
  }

  /**
   * 储值分析
   * */

  // 整体概况
  async getRechargeOverview(query) {
    return await this.$http.get('/pms_plat/statisv2.recharge.overview', { params: query });
  }

  // 储值分布分析
  async getRechargeAnalysis(query) {
    return await this.$http.get('/pms_plat/statisv2.recharge.analysis', { params: query });
  }

  // 储值统计记录
  async getRechargeRecord(query) {
    return await this.$http.get('/pms_plat/statisv2.recharge.record', { params: query });
  }

  // 储值统计记录导出
  async rechargeExporturl(query) {
    return await this.$http.get('/pms_plat/statisv2.recharge.exporturl', { params: query });
  }

  // 数据报表 品项部发货报表
  async getGoodsReportShipUrl(query) {
    return await this.$http.get('/pms_plat/goods.report.shipurl', { params: query });
  }
  /**
   * @description 2023-04-21 数据报表
   */
  async getReportManagesalesurl(query) {
    return await this.$http.get('/pms_plat/statisv2.report.managesalesurl', { params: query });
  }

  // 是否已全部确认库存成本
  async isCostFinished(params) {
    return await this.$http.get('/pms_plat/product.cost.isFinishedConfirm', { params });
  }
  // 直营诊所报表导出
  async getDirectClinicReportUrl(query) {
    return await this.$http.get('/pms_plat/finance.report.getDirectClinicUrl', { params: query });
  }

  // 获取品项SPU信息
  async getReportPxspuInfo(params) {
    return await this.$http.get('/pms_plat/statisv2.report.pxspuinfo', { params });
  }

  // 设置品项SPU范围
  async getReportPxspuSet(params) {
    return await this.$http.post('/pms_plat/statisv2.report.pxspuset', params);
  }



}

export default statiscis;
