export default class ErpOutstock {
  //导入出库单数据检测
  importOutstockCheck(params) {
    return this.$http.post('/pms_plat/erp.outbound.importoutbound', params);
  }

  // 导入出库单
  importOutstock(params) {
    return this.$http.post('/pms_plat/erp.outbound.importoutbound', params);
  }

  //待出库订单数量
  getUnOutOrderCount(params) {
    return this.$http.get('/pms_plat/erp.outbound.pendingordercount', {
      params,
    });
  }

  // downloadNoStockOrder(params) {
  //   return this.$http.get("/pms_plat/erp.outbound.exportpendingorder", {
  //     params,
  //   });
  // }
  downloadOrderTemplate(params) {
    return this.$http.get('/pms_plat/erp.outbound.downloadimporttemplateurl', {
      params,
    });
  }

  //校验出库单excel数据
  validateProOutOrder(params) {
    return this.$http.post('/pms_plat/erp.outbound.importoutbounddetection', params);
  }

  // 导出出入库明细
  exportProOutOrder(params) {
    return this.$http.get('/pms_plat/erp.productstock.billsExport', {
      params,
    });
  }
  // 新收入统计-诊所远程下拉枚举值
  async getErpFinancesReport(params) {
    return await this.$http.get('/pms_plat/erp.finances.export', { params });
  }
}
