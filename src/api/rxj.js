class Method_rxj {
  // 获取养疗馆列表
  async getRxjList(params) {
    const res = await this.$http.get('/pms_plat/rxj.enter.list', { params });
    return res;
  }

  // 获取养疗馆状态枚举列表
  async getRxjEnterOptions(params) {
    const res = await this.$http.get('/pms_plat/rxj.enter.options', { params });
    return res;
  }

  // 榕小家入驻详情
  async getRxjEnterInfo(params) {
    const res = await this.$http.get('/pms_plat/rxj.enter.info', { params });
    return res;
  }

  // 远程搜索采购主体
  /**
   * @param { name } 主体名
   * @parma { type } 默认为空查询全部，CLI : 所有诊所主体，RXJ：所有榕小家主体
   * */
  async searchPurchasesubject(params) {
    return await this.$http.get('/pms_plat/common.index.purchasesubject', { params });
  }

  // 榕小家开通
  async openConfigure(params) {
    return await this.$http.post('/pms_plat/rxj.enter.openconfigure', params);
  }

  // 小程序发布
  async changeRxjPublish(params) {
    return await this.$http.post('/pms_plat/rxj.enter.publish', params);
  }

  // 榕小家入驻编辑
  async changeRxjEnter(params) {
    return await this.$http.post('/pms_plat/rxj.enter.edit', params);
  }

  // 榕小家进件申请
  async changeRxjLedger(params) {
    return await this.$http.post('/pms_plat/rxj.ledger.apply', params);
  }

  // 榕小家进件申请
  async getLedgerBankCodeList(params) {
    return await this.$http.get('/pms_plat/rxj.ledger.bankCode', { params });
  }
}

export default Method_rxj;
