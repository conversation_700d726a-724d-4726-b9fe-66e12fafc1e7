class DownloadCenter {
  // 创建下载任务
  async createDownloadCenterMission(params) {
    return await this.$http.post('/pms_plat/exportCenter.create', params);
  }
  // 获取下载中心列表
  async getDownloadCenterList(params) {
    return await this.$http.get('/pms_plat/exportCenter.list', { params });
  }

  // 重试下载任务
  async retryDownloadMission(params) {
    return await this.$http.post('/pms_plat/exportCenter.retry', params);
  }
  // 导出类型枚举值
  async getDownloadOptions(params) {
    return await this.$http.get('/pms_plat/exportCenter.options', { params });
  }
}

export default DownloadCenter;
