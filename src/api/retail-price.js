class ReatilPrice {
  /**
   * @description: 菜单模块
   */

  /**
   * @description: 功能管理
   */

  // 功能管理-元素列表
  // async getElementlist(params) {
  //   return await this.$http.get('pms_plat/permission.resource.elementlist', {
  //     params,
  //   });
  // }
  //
  // // 功能管理-添加元素
  // async addelement(params) {
  //   return await this.$http.post('pms_plat/permission.resource.addelement', params);
  // }

  // 获取零售列表
  async getReatailList(params) {
    return await this.$http.get('pms_plat/goods.retail.list', {
      params,
    });
  }
  // 获取零售枚举
  async getReatailOption(params) {
    return await this.$http.get('pms_plat/goods.retail.options', {
      params,
    });
  }
  // 修改零售价
  async setReatailPrice(params) {
    return await this.$http.post('pms_plat/goods.retail.changeretailprice', params);
  }
  // 获取商品调价记录
  async getReatailHistory(params) {
    return await this.$http.get('pms_plat/goods.retail.operationlog', {
      params,
    });
  }
  // 获取商品调价模板
  async getReatailTemplate(params) {
    return await this.$http.get('pms_plat/goods.retail.changeretailpricetemplate', {
      params,
    });
  }
  // 批量调整商品零售价
  async setBatchRetailPrice(params) {
    return await this.$http.post('pms_plat/goods.retail.batchchangeretailprice', params);
  }
}

export default ReatilPrice;
