export default class CustomerService {
  // 获取省份列表
  async getProvinceAreaList(params) {
    return await this.$http.get('/pms_plat/customerservice.index.getprovincelist', { params });
  }
  // 添加客服
  async createCustomerService(params) {
    return await this.$http.post('/pms_plat/customerservice.index.store', params);
  }
  // 编辑客服
  async updateCustomerService(params) {
    return await this.$http.post('/pms_plat/customerservice.index.update', params);
  }
  // 获取客服列表
  async getCustomerServiceList(params) {
    return await this.$http.get('/pms_plat/customerservice.index.list', { params });
  }
  // 获取客服详情
  async getCustomerServiceDetail(params) {
    return await this.$http.get('/pms_plat/customerservice.index.show', { params });
  }
  // 获取当前默认客服
  async getDefaultCustomerService(params) {
    return await this.$http.get('/pms_plat/customerservice.index.getdefaultsrv', { params });
  }
  // 选项列表
  async getCustomerServiceOptions(params) {
    return await this.$http.get('/pms_plat/customerservice.index.options', { params });
  }
  // 设置客服状态
  async changeCustomerServiceStatus(params) {
    return await this.$http.post('/pms_plat/customerservice.index.status', params);
  }
}
