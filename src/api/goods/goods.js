class Method_goods {
  /**
   * @description组织供应商
   */

  // 获取商品列表
  async getGoodsList(params) {
    return await this.$http.get('/pms_plat/goods.index.list', { params });
  }

  // 获取商品选项枚举值
  async getGoodsOptions(params) {
    return await this.$http.get('/pms_plat/goods.index.options', { params });
  }

  // 获取商品选项枚举值
  async changeGoodsStatus(params) {
    return await this.$http.get('/pms_plat/goods.index.changestatus', { params });
  }

  // 搜索中草药
  async searchProd(params) {
    return await this.$http.get('/pms_plat/goods.index.searchprod', { params });
  }

  // 根据通用名获取助记码
  async getPhonetic(params) {
    return await this.$http.get('/pms_plat/goods.index.phonetic', { params });
  }

  // 获取商品信息
  async getGoodsInfo(params) {
    return await this.$http.get('/pms_plat/goods.index.info', { params });
  }

  // 获取一级分类
  async getSearchClass(params) {
    return await this.$http.get('/pms_plat/goods.index.searchclass', { params });
  }

  // 创建中草药
  async postGoodsSave(params) {
    return await this.$http.post('/pms_plat/goods.index.save', params);
  }
  /*?#######################售卖相关#####################*/
  // 售卖列表
  async getSaleList(params) {
    return await this.$http.get('/pms_plat/goods.sales.list', { params });
  }

  // 售卖枚举信息
  async getSaleOptions() {
    return await this.$http.get('/pms_plat/goods.sales.options');
  }
  // 售卖上下架
  async changeSaleStatus(params) {
    return await this.$http.get('/pms_plat/goods.sales.status', { params });
  }
  // 获取可创建加个的省公司
  async getSaleCompany(params) {
    return await this.$http.get('/pms_plat/goods.sales.goodscompany', { params });
  }
  // 获取售价信息
  async getSaleInfo(params) {
    return await this.$http.get('/pms_plat/goods.sales.info', { params });
  }
  // ! 获取售卖城市列表
  async getSaleCities(params) {
    return await this.$http.get('/pms_plat/goods.sales.citylist', { params });
  }
  // ! 获取售卖城市列表
  async addGoodsSales(params) {
    return await this.$http.post('/pms_plat/goods.sales.add', params);
  }
  // ! 移除商品价格条目
  async removeGoodsSales(params) {
    return await this.$http.post('/pms_plat/goods.sales.remove', params);
  }
  // ! 更新商品价格条目
  async updateGoodsSales(params) {
    return await this.$http.post('/pms_plat/goods.sales.update', params);
  }

  /**
   * @description: 采购(2022.3.2)
   */

  // 获取包裹详情
  async getPackinfo(params) {
    const res = await this.$http.get('/pms_plat/purchase.shiporder.packinfo', { params });
    return res;
  }

  /**
   * @description: 商品库
   */

  // 获取商品库列表
  async getGoodsLibList(params) {
    return await this.$http.get('/pms_plat/goods.libgoods.list', { params });
  }

  // 编辑商品库
  async changeGoodsLib(params) {
    return await this.$http.post('/pms_plat/goods.libgoods.edit', params);
  }

  // 获取商品库选项枚举值
  async getGoodsLibOptions(params) {
    return await this.$http.get('/pms_plat/goods.libgoods.options', { params });
  }

  // 复制
  async copyGoodsLib(params) {
    return await this.$http.get('pms_plat/goods.libgoods.copy', { params });
  }

  // 获取商品信息
  async getGoodsLibInfo(params) {
    return await this.$http.get('pms_plat/goods.libgoods.getinfo', { params });
  }

  // 获取商品库关联货品列表
  async getProdLibList(params) {
    return await this.$http.get('pms_plat/product.libprod.list', { params });
  }

  // 获取商品库操作记录
  async getGoodsOperationlog(params) {
    return await this.$http.get('/pms_plat/goods.libgoods.operationlog', { params });
  }

  // 获取商品库操作记录
  async getLogSnapshot(params) {
    return await this.$http.get('/pms_plat/goods.libgoods.logsnapshot', { params });
  }

  // 改变商品状态
  async changeGoodsLibStatus(params) {
    return await this.$http.post('/pms_plat/goods.libgoods.status', params);
  }

  /**
   * @description: 服务列表(2022.9.15)
   * */
  // 获取服务列表数据
  async getGoodsservicelibList(params) {
    return await this.$http.get('/pms_plat/goods.libgoodsservice.list', { params });
  }

  // 获取服务类型
  async getGoodsservicelibOptions(params) {
    return await this.$http.get('/pms_plat/goods.libgoodsservice.options', { params });
  }

  // 服务状态操作
  async getGoodsservicelibStatus(params) {
    return await this.$http.post('/pms_plat/goods.libgoodsservice.status', params);
  }

  // 编辑服务
  async getGoodsservicelibEdit(params) {
    return await this.$http.post('/pms_plat/goods.libgoodsservice.edit', params);
  }

  // 获取服务信息 - 暂时不需要此接口
  // async getGoodsservicelibGet(params){
  // 	return await this.$http.post('/pms_plat/goods.libgoodsservice.get', params)
  // }

  /*   货品列表选项  */
  async getGoodsListOptions() {
    return await this.$http.get('pms_plat/product.libprod.options');
  }

  /*   货品列表  */
  async getProductsList(params) {
    return await this.$http.get('pms_plat/product.libprod.list', { params });
  }

  /*   货品详情  */
  async getProductsInfo(params) {
    return await this.$http.get('pms_plat/product.libprod.info', { params });
  }

  /*   商品售卖搜索SKU  */
  async searchERPSku(params) {
    return await this.$http.get('/pms_plat/goods.distribution.searcherpproduct', { params });
  }

  /*   生成分发商品信息  */
  async createDistributionGoods(params) {
    return await this.$http.post('/pms_plat/goods.distribution.generate', params);
  }

  /*   生成分发商品信息  */
  async getERPGoodsDetail(params) {
    return await this.$http.get('/pms_plat/goods.distribution.detail', {
      params
    });
  }
}
export default Method_goods;
