export default class Method_Goods_AUDIT {
  //  获取商品审核模版列表
  async getGoodsAuditList(params) {
    return await this.$http.get('/pms_plat/goods.libgoodsaudit.list', {
      params,
    });
  }

  //  获取商品审核模版选项
  async getGoodsAuditOptions(params) {
    return await this.$http.get('/pms_plat/goods.libgoodsaudit.options', {
      params,
    });
  }

  //  获取商品审核模版详情
  async getGoodsAuditInfo(params) {
    return await this.$http.get('/pms_plat/goods.libgoodsaudit.info', {
      params,
    });
  }

  // 审核商品模板
  async auditGoodsTemplate(params) {
    return await this.$http.post('/pms_plat/goods.libgoodsaudit.auditstatus', params);
  }

  // 创建模板审核
  async createGoodsAudit(params) {
    return await this.$http.post('/pms_plat/goods.libgoodsaudit.add', params);
  }
}
