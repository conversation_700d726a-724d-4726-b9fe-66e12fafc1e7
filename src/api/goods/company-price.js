export default class CompanyPrice {
  //  获取省公司价格枚举
  async getCompanyPriceOptions(params) {
    return await this.$http.get('/pms_plat/goods.priceconfig.options', {
      params,
    });
  }
  //  获取省公司价格列表
  async getCompanyPriceList(params) {
    return await this.$http.get('/pms_plat/goods.priceconfig.list', {
      params,
    });
  }
  //  获取省公司价格调整记录
  async getCompanyPriceRecordList(params) {
    return await this.$http.get('/pms_plat/goods.priceconfig.operationLog', {
      params,
    });
  }
  //  单个调价
  async changeCompanyPriceOnly(params) {
    return await this.$http.post('/pms_plat/goods.priceconfig.changeOnly', params);
  }
  //  批量调价
  async changeCompanyPriceMore(params) {
    return await this.$http.post('/pms_plat/goods.priceconfig.changeMore', params);
  }
  //  获取供应商列表
  async getCommonSupplierList(params) {
    return await this.$http.get('/pms_plat/common.supplier.list', {
      params,
    });
  }
}
