class Method_plateform {
  /**
   * @description: 菜单模块
   */

  /**
   * @description: 功能管理
   * */

  // 功能管理-元素列表
  async getElementlist(params) {
    return await this.$http.get('/pms_plat/permission.resource.elementlist', { params });
  }

  // 功能管理-添加元素
  async addelement(params) {
    return await this.$http.post('/pms_plat/permission.resource.addelement', params);
  }

  // 功能管理-编辑元素
  async editelement(params) {
    return await this.$http.post('/pms_plat/permission.resource.editelement', params);
  }

  // 删除功能
  async delResource(params) {
    return await this.$http.post('/pms_plat/permission.resource.del', params);
  }
  // 编辑角色名称
  async editRoleName(params) {
    return await this.$http.post('/pms_plat/permission.role.edit', params);
  }
}

export default Method_plateform;
