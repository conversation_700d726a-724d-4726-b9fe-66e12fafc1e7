export default class AfterSale {
  // 售后列表
  async getOrderrefundList(params) {
    return await this.$http.get('/pms_plat/purchase.orderrefund.list', { params });
  }
  // 售后相关options
  async getOrderrefundOptions(params) {
    return await this.$http.get('/pms_plat/purchase.orderrefund.options', { params });
  }

  /*银联售后*/

  // 售后详情
  async getOrderrefundDetail(params) {
    return await this.$http.get('/pms_plat/purchase.orderrefund.detail', { params });
  }

  // 售后审核
  async getOrderrefundAudit(params) {
    return await this.$http.post('/pms_plat/purchase.orderrefund.audit', params);
  }

  // 是否收货，驳回/同意退款
  async getOrderrefundReceive(params) {
    return await this.$http.post('/pms_plat/purchase.orderrefund.receive', params);
  }

  // 财务，驳回/同意退款
  async getOrderrefundRefundAudit(params) {
    return await this.$http.post('/pms_plat/purchase.orderrefund.refundAudit', params);
  }

  // 售后明细列表
  async getOrderrefundApplyTabList(params) {
    return await this.$http.get('/pms_plat/purchase.orderrefund.applyTabList', { params });
  }

  // 采购订单详情的售后采购详情操作日志
  async getOperationLog(params) {
    return await this.$http.get('/pms_plat/purchase.orderrefund.operationLog', { params });
  }
  /*线下售后*/

  // 获取采购单售后商品
  async getOrderRefundGoods(params) {
    return await this.$http.get('/pms_plat/joinin.purchase.getordergoods', { params });
  }
  // 获取售后单日志
  async getOrderRefundLog(params) {
    return await this.$http.get('/pms_plat/joinin.orderrefund.getoperationlog', { params });
  }
  // 提交售后申请
  async applyOrderRefund(params) {
    return await this.$http.post('/pms_plat/joinin.orderrefund.apply', params);
  }
  // 获取快递公司列表
  async getExpressList(params) {
    return await this.$http.post('/pms_plat/common.getexpresslist', { params });
  }

  // 售后检查是否需要进行同步
  async checkSyncStatus(params) {
    return await this.$http.get('/pms_plat/purchase.orderrefund.checkSyncStatus', { params });
  }

  // 获取仓库
  async getRefundAddress(params) {
    return await this.$http.get('/pms_plat/purchase.orderrefund.refundAddress', { params });
  }
}
