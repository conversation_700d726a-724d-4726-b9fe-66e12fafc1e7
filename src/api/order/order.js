class Method_order {
  /**
   * @description首采接口
   */

  // 获取采购单列表
  async getPurchaseList(params) {
    return await this.$http.get('/pms_plat/joinin.purchase.list', { params });
  }

  // 获取枚举字段
  async getPurchaseOptions(params) {
    return await this.$http.get('/pms_plat/joinin.purchase.options', {
      params
    });
  }

  // 获取采购单信息
  async getPurchaseInfo(params) {
    return await this.$http.get('/pms_plat/joinin.purchase.info', { params });
  }

  // 发货
  async purchaseShip(params) {
    return await this.$http.post('/pms_plat/joinin.purchase.ship', params);
  }

  // 更改状态
  async setPurchaseStatus(params) {
    return await this.$http.post('/pms_plat/joinin.purchase.status', params);
  }

  // 打印
  async purchasePrint(params) {
    return await this.$http.get('/pms_plat/joinin.purchase.print', { params });
  }

  // 录入物流信息
  async enterLogisticsInfor(params) {
    return await this.$http.post('/pms_plat/joinin.purchase.express', params);
  }

  /**
   * @description 首次产品包
   */

  // 获取首次产品包列表
  async getPackageList(params) {
    return await this.$http.get('/pms_plat/joinin.package.list', { params });
  }

  // 获取枚举类型
  async getPackageOptions() {
    return await this.$http.get('/pms_plat/joinin.package.options');
  }

  // 获取明细列表
  async getPackageInfo(params) {
    return await this.$http.get('/pms_plat/joinin.package.info', { params });
  }

  // 首次开业包审核
  async setPackageStatus(params) {
    return await this.$http.post('/pms_plat/joinin.package.status', params);
  }

  // =============订单管理模块-订单列表==============
  // 获取省公司
  async getPOrderOptions(params) {
    return await this.$http.get('/pms_plat/purchase.order.options', { params });
  }

  // 获取订单列表   新列表
  async getPOList(params) {
    return await this.$http.get('/pms_plat/purchase.order.listnew', { params });
  }

  // 获取订单详情(废弃)
  async getPODetail(params) {
    return await this.$http.get('/pms_plat/purchase.order.detail', { params });
  }
  // 获取订单详情
  async getNewPODetail(params) {
    return await this.$http.get('/pms_plat/purchase.order.newdetail', { params });
  }

  // 获取退款列表
  async getRefundList(params) {
    return await this.$http.get('/pms_plat/joinin.refund.list', { params });
  }

  // 获取退款详情
  async getRefundDetail(params) {
    return await this.$http.get('/pms_plat/joinin.refund.info', { params });
  }

  // 退款状态变更
  async changeRefundStatus(params) {
    return await this.$http.get('/pms_plat/joinin.refund.changestatus', {
      params
    });
  }

  /**
   * @description: 订单新增类型和导出功能
   * */
  // 订单导出的接口
  async getPurchaselisturl(params) {
    return await this.$http.get('/pms_plat/joinin.report.getpurchaselisturl', {
      params
    });
  }

  async getPduducList(params) {
    return await this.$http.get('/pms_plat/joinin.package.exportlist', {
      params
    });
  }
}

export default Method_order;
