class Method_supplier {
  /**
   * @description组织供应商
   */

  // 获取供应商列表
  async getSupplierList(params) {
    return await this.$http.get('pms_plat/erp.supplier.list', { params });
  }

  // 供应商状态修改
  async changeStatus(params) {
    return await this.$http.get('/pms_plat/supplier.index.changestatus', { params });
  }

  // 供应商选项-noUse
  async supplierOptions(params) {
    return await this.$http.get('/pms_plat/supplier.index.options', { params });
  }

  // 获取供应商详情
  async getSupplierInfo(params) {
    return await this.$http.get('/pms_plat/supplier.index.info', { params });
  }

  // 获取供应商详情
  async postSupplierEdit(params) {
    return await this.$http.post('/pms_plat/supplier.index.edit', params);
  }

  /**
   * @description 诊所供应商
   */
  // 获取诊所供应商列表
  async getCliSupplierList(params) {
    return await this.$http.get('/pms_plat/supplier.hissupplier.list', { params });
  }
  // 获取诊所供应商列表
  async getCliSupplierInfo(params) {
    return await this.$http.get('/pms_plat/supplier.hissupplier.info', { params });
  }
  // 供应商修改状态
  async changeSupplierStatus(params) {
    return await this.$http.post('/pms_plat/supplier.hissupplier.status', params);
  }
}
export default Method_supplier;
