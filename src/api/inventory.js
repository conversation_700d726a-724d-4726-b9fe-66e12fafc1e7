export default class Inventory {
  // 创建盘点单
  async createErpStockList(params) {
    return await this.$http.post('/pms_plat/erp.stocktake.store', params);
  }

  // 获取盘点单列表
  async getErpStockList(params) {
    return await this.$http.get('/pms_plat/erp.stocktake.list', { params });
  }

  // 获取盘点单详情
  async getErpStockDetail(params) {
    return await this.$http.get('/pms_plat/erp.stocktake.show', { params });
  }

  // 下载盘点导入模板
  async downLoadErpStockTemplate(params) {
    return await this.$http.get('/pms_plat/erp.stocktake.downimporttemplate', {
      params,
    });
  }

  // 修改盘点单
  async editErpStockList(params) {
    return await this.$http.post('/pms_plat/erp.stocktake.update', params);
  }

  // 审核盘点单
  async reviewErpStockList(params) {
    return await this.$http.post('/pms_plat/erp.stocktake.audit', params);
  }

  // 作废盘点单
  async invalidErpStockList(params) {
    return await this.$http.post('/pms_plat/erp.stocktake.invalid', params);
  }

  // 盘点导入数据检测
  async stockImportDetec(params) {
    return await this.$http.post('/pms_plat/erp.stocktake.importprecheck', params);
  }

  // 盘点状态options
  async getWarehouseOptions(params) {
    return await this.$http.get('/pms_plat/erp.stocktake.options', {
      params,
    });
  }

  // 获取最后盘点时间
  async getLaststocktaketime(params) {
    return await this.$http.get('/pms_plat/erp.stocktake.getlaststocktaketime', {
      params,
    });
  }
}
