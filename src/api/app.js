class Method_app {
  /**
   *
   *
   * @param {*} extendCls 最终继承类
   */
  /* example	async	getFinanceOptions(){
		return await this.$http.get('/pms_plat/finance.comaccount.options')
	}*/
  async accountlogin(params) {
    const res = await this.$http.post('/pms_plat/index.accountlogin', params);
    console.log(res);
    return res;
  }

  async login(params) {
    const res = await this.$http.post('/pms_plat/index.login', params);
    console.log(res);
    return res;
  }

  async sendauthcode(params) {
    const res = await this.$http.post('/pms_plat/mobile.sendauthcode', params);
    return res;
  }

  async changeRetrievepass(params) {
    const res = await this.$http.post('/pms_plat/index.retrievepass', params);
    return res;
  }
}
export default Method_app;
