export default class SuperCode {
  // 获取验证码列表
  async getSuperCodeList(query) {
    return await this.$http.get('/pms_plat/Authorization.Authorization.list', {
      params: query,
    });
  }
  // 获取验证码登陆日志
  async getSuperCodeHistory(query) {
    return await this.$http.get('/pms_plat/Authorization.Authorization.logLIst', {
      params: query,
    });
  }

  // 获取options
  async getSuperCodeOptions(query) {
    return await this.$http.get('/pms_plat/Authorization.Authorization.options', {
      params: query,
    });
  }

  // 获取列表详情
  async getSuperCodeDetail(query) {
    return await this.$http.get('/pms_plat/Authorization.Authorization.getinfo', {
      params: query,
    });
  }

  // 新增验证码
  async setAuthorCode(params) {
    return await this.$http.post('/pms_plat/Authorization.Authorization.create', params);
  }

  // 修改验证码
  async setAuthorCodeEdit(params) {
    return await this.$http.post('/pms_plat/Authorization.Authorization.edit', params);
  }

  // 禁用启用
  async setSuperStatus(params) {
    return await this.$http.post('/pms_plat/Authorization.Authorization.status', params);
  }
}
