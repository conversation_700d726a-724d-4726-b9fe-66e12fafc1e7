{"name": "fpc-rsjerp", "version": "0.1.0", "private": true, "scripts": {"serve:test": "vue-cli-service serve --mode serve.test", "serve:prod": "vue-cli-service serve --mode serve.prod", "serve:test-74": "vue-cli-service serve --mode serve.test-74", "build:test-74": "vue-cli-service build --mode build.test-74", "build:test": "vue-cli-service build --mode build.test", "build:prod": "vue-cli-service build --mode build.prod", "lint": "vue-cli-service lint"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@tinymce/tinymce-vue": "^3.2.6", "axios": "^0.21.1", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "echarts": "^5.2.1", "element-china-area-data": "^5.0.2", "element-ui": "^2.15.5", "file-saver": "^2.0.5", "font-awesome": "^4.7.0", "html2canvas": "^1.4.1", "js-cookie": "^2.2.1", "js-md5": "^0.7.3", "lil-uri": "^0.2.2", "lodash": "^4.17.21", "moment": "^2.29.1", "number-precision": "^1.5.2", "qiniu-js": "^3.1.2", "qrcodejs2": "0.0.2", "resize-detector": "^0.3.0", "secure-ls": "^1.2.6", "svg-sprite-loader": "^4.1.3", "tinymce": "^6.2.0", "ulid": "^2.3.0", "v-region": "^2.2.2", "v-viewer": "^1.6.4", "view-design": "^4.4.0", "vue": "^2.6.11", "vue-amap": "^0.5.10", "vue-awesome-countdown": "^1.1.4", "vue-ls": "^3.2.2", "vue-router": "^3.2.0", "vue-ueditor-wrap": "^2.5.6", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "vuex-persistedstate": "^4.0.0", "xlsx": "^0.17.4"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "babel-plugin-component": "^1.1.1", "compression-webpack-plugin": "^1.1.12", "eslint": "^6.7.2", "eslint-config-prettier": "^6.15.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^6.2.2", "less": "^2.7.3", "less-loader": "^4.0.5", "mockjs": "^1.1.0", "prettier": "^2.8.7", "terser-webpack-plugin": "^4.2.3", "vue-cli-plugin-element": "~1.0.1", "vue-template-compiler": "^2.0.0"}, "volta": {"node": "14.20.0", "npm": "8.14.0"}}